"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { MdLanguage, MdClose } from "react-icons/md";
import { getLanguageDisplayName } from "@/lib/language-detection";

interface LanguageSwitchDialogProps {
  /** 是否显示弹框 */
  open: boolean;
  /** 当前语言 */
  currentLanguage: string;
  /** 建议切换到的语言 */
  suggestedLanguage: string;
  /** 用户点击切换时的回调 */
  onSwitch: () => void;
  /** 用户点击取消时的回调 */
  onCancel: () => void;
  /** 弹框关闭时的回调 */
  onClose: () => void;
  /** 自定义文本（可选） */
  texts?: {
    title?: string;
    description?: string;
    switchButton?: string;
    cancelButton?: string;
  };
}

/**
 * 语言切换确认弹框组件
 */
export default function LanguageSwitchDialog({
  open,
  currentLanguage,
  suggestedLanguage,
  onSwitch,
  onCancel,
  onClose,
  texts,
}: LanguageSwitchDialogProps) {
  const currentLangName = getLanguageDisplayName(currentLanguage);
  const suggestedLangName = getLanguageDisplayName(suggestedLanguage);

  // 生成按钮文案，按钮文案与对应语言保持一致
  const getSwitchButtonText = (targetLang: string, targetLangName: string) => {
    return targetLang === "zh" ? `切换到${targetLangName}` : `Switch to ${targetLangName}`;
  };

  const getCancelButtonText = (currentLang: string, currentLangName: string) => {
    return currentLang === "zh" ? `保持${currentLangName}` : `Keep ${currentLangName}`;
  };

  // 默认文本（英文）
  const defaultTexts = {
    title: "Switch Language?",
    description: `We detected that your browser language is ${suggestedLangName}. Would you like to switch from ${currentLangName} to ${suggestedLangName}?`,
    switchButton: getSwitchButtonText(suggestedLanguage, suggestedLangName),
    cancelButton: getCancelButtonText(currentLanguage, currentLangName),
  };

  // 中文文本
  const chineseTexts = {
    title: "切换语言？",
    description: `我们检测到您的浏览器语言是${suggestedLangName}。您是否要从${currentLangName}切换到${suggestedLangName}？`,
    switchButton: getSwitchButtonText(suggestedLanguage, suggestedLangName),
    cancelButton: getCancelButtonText(currentLanguage, currentLangName),
  };

  // 根据当前语言选择文本
  const finalTexts = texts || (currentLanguage === "zh" ? chineseTexts : defaultTexts);

  const handleSwitchClick = () => {
    onSwitch();
    onClose();
  };

  const handleCancelClick = () => {
    onCancel();
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MdLanguage className="text-xl text-primary" />
            {finalTexts.title}
          </DialogTitle>
          <DialogDescription className="text-left">
            {finalTexts.description}
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleCancelClick}
            className="w-full sm:w-auto"
          >
            {finalTexts.cancelButton}
          </Button>
          <Button
            onClick={handleSwitchClick}
            className="w-full sm:w-auto"
          >
            {finalTexts.switchButton}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * 语言切换确认弹框的简化版本
 * 自动根据语言参数生成文本
 */
export function SimpleLanguageSwitchDialog({
  open,
  currentLanguage,
  suggestedLanguage,
  onSwitch,
  onCancel,
  onClose,
}: Omit<LanguageSwitchDialogProps, "texts">) {
  return (
    <LanguageSwitchDialog
      open={open}
      currentLanguage={currentLanguage}
      suggestedLanguage={suggestedLanguage}
      onSwitch={onSwitch}
      onCancel={onCancel}
      onClose={onClose}
    />
  );
}
