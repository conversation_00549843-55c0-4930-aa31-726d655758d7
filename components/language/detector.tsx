"use client";

import React, { useEffect, useState } from "react";
import { useParams, useRouter, usePathname } from "next/navigation";
import { shouldSuggestLanguageSwitch, getLanguageDetectionInfo } from "@/lib/language-detection";
import {
  shouldShowLanguageSuggestion,
  saveLanguageSwitchAccepted,
  saveLanguageSwitchDeclined,
} from "@/lib/language-preference";
import { SimpleLanguageSwitchDialog } from "./switch-dialog";
import I18nLanguageSwitchDialog from "./i18n-switch-dialog";

interface LanguageDetectorProps {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 检测延迟（毫秒），默认1000ms */
  detectionDelay?: number;
  /** 自定义语言切换逻辑 */
  onLanguageSwitch?: (fromLanguage: string, toLanguage: string) => void;
  /** 是否使用国际化文本，默认true */
  useI18n?: boolean;
}

/**
 * 语言检测器组件
 * 在用户首次访问时检测浏览器语言并提供切换建议
 */
export default function LanguageDetector({
  debug = false,
  detectionDelay = 1000,
  onLanguageSwitch,
  useI18n = true,
}: LanguageDetectorProps) {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  
  const currentLocale = params.locale as string;
  
  const [showDialog, setShowDialog] = useState(false);
  const [suggestedLanguage, setSuggestedLanguage] = useState<string | null>(null);
  const [isDetectionComplete, setIsDetectionComplete] = useState(false);

  /**
   * 执行语言切换
   */
  const handleLanguageSwitch = (fromLanguage: string, toLanguage: string) => {
    if (onLanguageSwitch) {
      onLanguageSwitch(fromLanguage, toLanguage);
    } else {
      // 默认的语言切换逻辑
      let newPathName = pathname.replace(`/${fromLanguage}`, `/${toLanguage}`);
      if (!newPathName.startsWith(`/${toLanguage}`)) {
        newPathName = `/${toLanguage}${newPathName}`;
      }
      router.push(newPathName);
    }
  };

  /**
   * 用户选择切换语言
   */
  const handleSwitchAccepted = () => {
    if (!suggestedLanguage) return;

    const detectedLanguage = shouldSuggestLanguageSwitch(currentLocale);
    if (detectedLanguage) {
      saveLanguageSwitchAccepted(currentLocale, suggestedLanguage, detectedLanguage);
      handleLanguageSwitch(currentLocale, suggestedLanguage);
    }
  };

  /**
   * 用户选择不切换语言
   */
  const handleSwitchDeclined = () => {
    if (!suggestedLanguage) return;

    const detectedLanguage = shouldSuggestLanguageSwitch(currentLocale);
    if (detectedLanguage) {
      saveLanguageSwitchDeclined(currentLocale, suggestedLanguage, detectedLanguage);
    }
  };

  /**
   * 执行语言检测
   */
  const performLanguageDetection = () => {
    try {
      // 检查是否应该建议语言切换
      const suggested = shouldSuggestLanguageSwitch(currentLocale);
      
      if (debug) {
        const detectionInfo = getLanguageDetectionInfo(currentLocale);
        console.log("Language Detection Info:", detectionInfo);
      }

      if (suggested) {
        // 检查是否应该显示建议
        const shouldShow = shouldShowLanguageSuggestion(currentLocale, suggested);
        
        if (debug) {
          console.log("Should show language suggestion:", shouldShow);
          console.log("Suggested language:", suggested);
        }

        if (shouldShow) {
          setSuggestedLanguage(suggested);
          setShowDialog(true);
        }
      }
    } catch (error) {
      console.error("Language detection failed:", error);
    } finally {
      setIsDetectionComplete(true);
    }
  };

  /**
   * 组件挂载时执行语言检测
   */
  useEffect(() => {
    // 只在客户端执行
    if (typeof window === "undefined") return;

    // 延迟执行检测，确保页面完全加载
    const timer = setTimeout(() => {
      performLanguageDetection();
    }, detectionDelay);

    return () => clearTimeout(timer);
  }, [currentLocale, detectionDelay]);

  /**
   * 调试信息显示
   */
  if (debug && isDetectionComplete) {
    return (
      <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
        <h4 className="font-bold mb-2">Language Detection Debug</h4>
        <div className="space-y-1">
          <div>Current: {currentLocale}</div>
          <div>Suggested: {suggestedLanguage || "None"}</div>
          <div>Show Dialog: {showDialog ? "Yes" : "No"}</div>
        </div>
        {showDialog && (
          <SimpleLanguageSwitchDialog
            open={showDialog}
            currentLanguage={currentLocale}
            suggestedLanguage={suggestedLanguage!}
            onSwitch={handleSwitchAccepted}
            onCancel={handleSwitchDeclined}
            onClose={() => setShowDialog(false)}
          />
        )}
      </div>
    );
  }

  // 正常模式下只渲染弹框
  return (
    <>
      {showDialog && suggestedLanguage && (
        useI18n ? (
          <I18nLanguageSwitchDialog
            open={showDialog}
            currentLanguage={currentLocale}
            suggestedLanguage={suggestedLanguage}
            onSwitch={handleSwitchAccepted}
            onCancel={handleSwitchDeclined}
            onClose={() => setShowDialog(false)}
          />
        ) : (
          <SimpleLanguageSwitchDialog
            open={showDialog}
            currentLanguage={currentLocale}
            suggestedLanguage={suggestedLanguage}
            onSwitch={handleSwitchAccepted}
            onCancel={handleSwitchDeclined}
            onClose={() => setShowDialog(false)}
          />
        )
      )}
    </>
  );
}

/**
 * 语言检测器的简化版本
 * 使用默认配置
 */
export function SimpleLanguageDetector() {
  return <LanguageDetector />;
}

/**
 * 带调试功能的语言检测器
 * 用于开发和测试
 */
export function DebugLanguageDetector() {
  return <LanguageDetector debug={true} detectionDelay={500} />;
}
