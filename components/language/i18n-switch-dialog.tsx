"use client";

import React from "react";
import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { MdLanguage } from "react-icons/md";
import { getLanguageDisplayName } from "@/lib/language-detection";

interface I18nLanguageSwitchDialogProps {
  /** 是否显示弹框 */
  open: boolean;
  /** 当前语言 */
  currentLanguage: string;
  /** 建议切换到的语言 */
  suggestedLanguage: string;
  /** 用户点击切换时的回调 */
  onSwitch: () => void;
  /** 用户点击取消时的回调 */
  onCancel: () => void;
  /** 弹框关闭时的回调 */
  onClose: () => void;
}

/**
 * 使用国际化文本的语言切换确认弹框组件
 */
export default function I18nLanguageSwitchDialog({
  open,
  currentLanguage,
  suggestedLanguage,
  onSwitch,
  onCancel,
  onClose,
}: I18nLanguageSwitchDialogProps) {
  const t = useTranslations("language_switch");

  const currentLangName = getLanguageDisplayName(currentLanguage);
  const suggestedLangName = getLanguageDisplayName(suggestedLanguage);

  // 调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('I18nLanguageSwitchDialog Debug:', {
      currentLanguage,
      suggestedLanguage,
      currentLangName,
      suggestedLangName,
      title: t("title"),
      description: t("description"),
      switchButton: t(`switch_button_${suggestedLanguage}`),
      cancelButton: t(`cancel_button_${currentLanguage}`),
    });
  }

  const handleSwitchClick = () => {
    onSwitch();
    onClose();
  };

  const handleCancelClick = () => {
    onCancel();
    onClose();
  };

  // 使用 next-intl 的参数传递功能
  const description = t("description", {
    currentLanguage: currentLangName,
    suggestedLanguage: suggestedLangName,
  });

  // 按钮文案与对应语言保持一致
  const switchButtonText = t(`switch_button_${suggestedLanguage}`, {
    suggestedLanguage: suggestedLangName,
  });

  const cancelButtonText = t(`cancel_button_${currentLanguage}`, {
    currentLanguage: currentLangName,
  });

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MdLanguage className="text-xl text-primary" />
            {t("title")}
          </DialogTitle>
          <DialogDescription className="text-left">
            {description}
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleCancelClick}
            className="w-full sm:w-auto"
          >
            {cancelButtonText}
          </Button>
          <Button
            onClick={handleSwitchClick}
            className="w-full sm:w-auto"
          >
            {switchButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
