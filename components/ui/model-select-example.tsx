"use client"

import * as React from "react"
import {
  RichSelect,
  RichSelectContent,
  RichSelectItem,
  RichSelectTrigger,
  RichSelectValue,
  type RichSelectOption,
} from "./rich-select"

// AI模型数据示例
const aiModels: RichSelectOption[] = [
  {
    value: "imagen-3",
    label: "Imagen 3",
    description: "Particularly suitable for commercial use",
    icon: "i-com--logo-google-circle", // UnoCSS图标类
  },
  {
    value: "dall-e-3",
    label: "DALL-E 3",
    description: "Advanced image generation with high quality",
    icon: "i-com--logo-openai-circle",
  },
  {
    value: "midjourney-v6",
    label: "Midjourney v6",
    description: "Artistic and creative image generation",
    icon: "i-com--logo-midjourney-circle",
  },
  {
    value: "stable-diffusion-xl",
    label: "Stable Diffusion XL",
    description: "Open source high-resolution image generation",
    icon: "i-com--logo-stability-circle",
  },
  {
    value: "claude-3-opus",
    label: "Claude 3 Opus",
    description: "Most capable model for complex tasks",
    icon: "i-com--logo-anthropic-circle",
  },
]

// 使用React Icons的版本
const aiModelsWithReactIcons: RichSelectOption[] = [
  {
    value: "imagen-3",
    label: "Imagen 3",
    description: "Particularly suitable for commercial use",
    icon: <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">G</div>,
  },
  {
    value: "dall-e-3",
    label: "DALL-E 3",
    description: "Advanced image generation with high quality",
    icon: <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">O</div>,
  },
  {
    value: "midjourney-v6",
    label: "Midjourney v6",
    description: "Artistic and creative image generation",
    icon: <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">M</div>,
  },
  {
    value: "stable-diffusion-xl",
    label: "Stable Diffusion XL",
    description: "Open source high-resolution image generation",
    icon: <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold">S</div>,
  },
  {
    value: "claude-3-opus",
    label: "Claude 3 Opus",
    description: "Most capable model for complex tasks",
    icon: <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold">C</div>,
  },
]

// ModelSelect组件示例
export function ModelSelectExample() {
  const [selectedModel, setSelectedModel] = React.useState<string>("")

  return (
    <div className="w-full max-w-md space-y-4">
      <div>
        <label htmlFor="modelName" className="block text-sm font-medium mb-2">
          选择AI模型
        </label>
        <RichSelect value={selectedModel} onValueChange={setSelectedModel}>
          <RichSelectTrigger size="lg" className="w-full">
            <RichSelectValue placeholder="请选择一个AI模型">
              {selectedModel && (
                <div className="flex items-center gap-2">
                  {renderSelectedOption(selectedModel, aiModelsWithReactIcons)}
                </div>
              )}
            </RichSelectValue>
          </RichSelectTrigger>
          <RichSelectContent>
            {aiModelsWithReactIcons.map((model) => (
              <RichSelectItem
                key={model.value}
                value={model.value}
                option={model}
                disabled={model.disabled}
              />
            ))}
          </RichSelectContent>
        </RichSelect>
      </div>

      {/* 使用UnoCSS图标的版本（如果你的项目支持） */}
      <div>
        <label className="block text-sm font-medium mb-2">
          使用UnoCSS图标版本
        </label>
        <RichSelect value={selectedModel} onValueChange={setSelectedModel}>
          <RichSelectTrigger size="lg" className="w-full">
            <RichSelectValue placeholder="请选择一个AI模型">
              {selectedModel && (
                <div className="flex items-center gap-2">
                  {renderSelectedOptionWithCSS(selectedModel, aiModels)}
                </div>
              )}
            </RichSelectValue>
          </RichSelectTrigger>
          <RichSelectContent>
            {aiModels.map((model) => (
              <RichSelectItem
                key={model.value}
                value={model.value}
                option={model}
                disabled={model.disabled}
              />
            ))}
          </RichSelectContent>
        </RichSelect>
      </div>
    </div>
  )
}

// 渲染选中选项的辅助函数（React Icons版本）
function renderSelectedOption(value: string, options: RichSelectOption[]) {
  const option = options.find(opt => opt.value === value)
  if (!option) return null

  return (
    <>
      {option.icon}
      <div className="flex flex-col gap-0.5">
        <span className="text-sm font-semibold">{option.label}</span>
        <span className="text-xs font-medium text-muted-foreground">
          {option.description}
        </span>
      </div>
    </>
  )
}

// 渲染选中选项的辅助函数（CSS图标版本）
function renderSelectedOptionWithCSS(value: string, options: RichSelectOption[]) {
  const option = options.find(opt => opt.value === value)
  if (!option) return null

  return (
    <>
      <span className={`${option.icon} size-8`} />
      <div className="flex flex-col gap-0.5">
        <span className="text-sm font-semibold">{option.label}</span>
        <span className="text-xs font-medium text-muted-foreground">
          {option.description}
        </span>
      </div>
    </>
  )
}

// 简化版本 - 只显示基本信息
export function SimpleModelSelect() {
  const [selectedModel, setSelectedModel] = React.useState<string>("imagen-3")

  return (
    <RichSelect value={selectedModel} onValueChange={setSelectedModel}>
      <RichSelectTrigger size="lg" className="w-full max-w-md">
        <RichSelectValue>
          {selectedModel && (
            <div className="flex items-center gap-2">
              {renderSelectedOption(selectedModel, aiModelsWithReactIcons)}
            </div>
          )}
        </RichSelectValue>
      </RichSelectTrigger>
      <RichSelectContent>
        {aiModelsWithReactIcons.map((model) => (
          <RichSelectItem
            key={model.value}
            value={model.value}
            option={model}
          />
        ))}
      </RichSelectContent>
    </RichSelect>
  )
}

export default ModelSelectExample
