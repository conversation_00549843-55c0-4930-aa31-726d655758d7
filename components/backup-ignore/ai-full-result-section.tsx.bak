"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, Loader2, Eye, Download } from "lucide-react";

interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;

  };
}

interface AIFullResultSectionProps {
  generationResult: GenerationResult | null;
  modelType: string;
}

export function AIFullResultSection({ generationResult, modelType }: AIFullResultSectionProps) {
  const renderResult = () => {
    if (!generationResult) {
      return (
        <div className="text-center text-gray-500 py-8">
          请在左侧输入提示词并点击生成ss
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* 生成状态 */}
        <div className="flex items-center gap-2">
          {generationResult.status === 'success' && <CheckCircle className="w-5 h-5 text-green-600" />}
          {generationResult.status === 'failed' && <AlertCircle className="w-5 h-5 text-red-600" />}
          {(generationResult.status === 'pending' || generationResult.status === 'running') && <Loader2 className="w-5 h-5 animate-spin" />}
          <span className="font-medium">
            {generationResult.status === 'success' && '生成完成'}
            {generationResult.status === 'failed' && '生成失败'}
            {(generationResult.status === 'pending' || generationResult.status === 'running') && '生成中...'}
          </span>
        </div>

        {/* 结果内容 */}
        {generationResult.status === 'success' && generationResult.result && (
          <div className="space-y-4">
            {/* 文本结果 */}
            {generationResult.result.text && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm">{generationResult.result.text}</pre>
              </div>
            )}

            {/* 图像结果 */}
            {generationResult.result.images && (
              <div className="grid grid-cols-1 gap-4">
                {generationResult.result.images.map((image, index) => (
                  <div key={index} className="space-y-2">
                    <img
                      src={image.url}
                      alt={`Generated image ${index + 1}`}
                      className="w-full rounded-lg shadow-md"
                    />
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" asChild>
                        <a href={image.url} target="_blank" rel="noopener noreferrer">
                          <Eye className="w-4 h-4 mr-1" />
                          查看
                        </a>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <a href={image.url} download>
                          <Download className="w-4 h-4 mr-1" />
                          下载
                        </a>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 视频结果 */}
            {generationResult.result.video && (
              <div className="space-y-2">
                <video
                  src={generationResult.result.video.url}
                  controls
                  className="w-full rounded-lg shadow-md"
                />
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <a href={generationResult.result.video.url} target="_blank" rel="noopener noreferrer">
                      <Eye className="w-4 h-4 mr-1" />
                      查看
                    </a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href={generationResult.result.video.url} download>
                      <Download className="w-4 h-4 mr-1" />
                      下载
                    </a>
                  </Button>
                </div>
              </div>
            )}

            {/* 使用统计 */}
            {generationResult.usage && (
              <div className="text-sm text-gray-600">
                本次生成消耗 {generationResult.usage.credits_consumed} 积分
              </div>
            )}
          </div>
        )}

        {/* 错误信息 */}
        {generationResult.status === 'failed' && generationResult.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              生成失败: {generationResult.error.detail}
            </AlertDescription>
          </Alert>
        )}

        {/* 进度显示 */}
        {(generationResult.status === 'pending' || generationResult.status === 'running') && (
          <div className="text-center py-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              正在生成中，请稍候...
              {generationResult.progress !== undefined && ` (${generationResult.progress}%)`}
            </p>
          </div>
        )}
      </div>
    );
  };

  const getResultTitle = () => {
    switch (modelType) {
      case 'text':
        return '文本生成结果';
      case 'image':
        return '图像生成结果';
      case 'video':
        return '视频生成结果';
      default:
        return '生成结果';
    }
  };

  const getResultDescription = () => {
    switch (modelType) {
      case 'text':
        return '文本生成的结果将在这里显示';
      case 'image':
        return '图像生成的结果将在这里显示';
      case 'video':
        return '视频生成的结果将在这里显示';
      default:
        return '生成结果将在这里显示';
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>{getResultTitle()}</CardTitle>
        <CardDescription>
          {getResultDescription()}
        </CardDescription>
      </CardHeader>
      <CardContent className="h-full overflow-y-auto">
        {renderResult()}
      </CardContent>
    </Card>
  );
}
