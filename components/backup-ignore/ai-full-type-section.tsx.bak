"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Brain, Image, Video } from "lucide-react";

interface AIFullTypeSectionProps {
  activeTab: string;
  onTabChange: (value: string) => void;
}

export function AIFullTypeSection({ activeTab, onTabChange }: AIFullTypeSectionProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">模型类型</h3>
      <Tabs value={activeTab} onValueChange={onTabChange} orientation="vertical" className="w-full">
        <TabsList className="grid w-full grid-rows-3 h-auto">
          <TabsTrigger value="text" className="flex items-center gap-2 justify-start px-4 py-3">
            <Brain className="w-4 h-4" />
            文本生成
          </TabsTrigger>
          <TabsTrigger value="image" className="flex items-center gap-2 justify-start px-4 py-3">
            <Image className="w-4 h-4" />
            图片生成
          </TabsTrigger>
          <TabsTrigger value="video" className="flex items-center gap-2 justify-start px-4 py-3">
            <Video className="w-4 h-4" />
            视频生成
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
