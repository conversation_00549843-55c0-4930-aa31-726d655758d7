"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Send, AlertCircle, Coins } from "lucide-react";
import { AISharedSelectorFunction } from "./ai-shared-selector-function";
import { toast } from "sonner";

interface AIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  credits_per_unit: number;
  unit_type: string;
  description?: string;
  supported_features?: string[];
}

interface AIFullConfigSectionProps {
  modelType: string;
  selectedModel: AIModel | null;
  onModelChange: (model: AIModel | null) => void;
  prompt: string;
  onPromptChange: (prompt: string) => void;
  options: any;
  onOptionsChange: (options: any) => void;
  loading: boolean;
  onGenerate: () => void;
  costEstimate: any;
  userCredits: number;
}

export function AIFullConfigSection({
  modelType,
  selectedModel,
  onModelChange,
  prompt,
  onPromptChange,
  options,
  onOptionsChange,
  loading,
  onGenerate,
  costEstimate,
  userCredits
}: AIFullConfigSectionProps) {
  const renderModelOptions = () => {
    if (!selectedModel) return null;

    const { model_type, supported_features = [] } = selectedModel;

    return (
      <div className="space-y-4">
        {/* 文本生成选项 */}
        {model_type === 'text' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="max_tokens">最大输出长度</Label>
              <Input
                id="max_tokens"
                type="number"
                placeholder="1000"
                value={options.max_tokens || ''}
                onChange={(e) => onOptionsChange({...options, max_tokens: parseInt(e.target.value) || 1000})}
              />
            </div>
            <div>
              <Label htmlFor="temperature">创造性 (0-1)</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="0.7"
                value={options.temperature || ''}
                onChange={(e) => onOptionsChange({...options, temperature: parseFloat(e.target.value) || 0.7})}
              />
            </div>
          </div>
        )}

        {/* 图像生成选项 */}
        {model_type === 'image' && (
          <div className="grid grid-cols-2 gap-4">
            {supported_features.includes('variants') && (
              <div>
                <Label htmlFor="variants">生成数量</Label>
                <Select value={options.variants?.toString() || '1'} onValueChange={(value) => onOptionsChange({...options, variants: parseInt(value)})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1张</SelectItem>
                    <SelectItem value="2">2张</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
            
            {(supported_features.includes('aspectRatio') || supported_features.includes('size')) && (
              <div>
                <Label htmlFor="size">图像尺寸</Label>
                <Select value={options.size || options.aspectRatio || '1:1'} onValueChange={(value) => onOptionsChange({...options, size: value, aspectRatio: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1:1">正方形 (1:1)</SelectItem>
                    <SelectItem value="16:9">横屏 (16:9)</SelectItem>
                    <SelectItem value="9:16">竖屏 (9:16)</SelectItem>
                    <SelectItem value="4:3">标准 (4:3)</SelectItem>
                    <SelectItem value="3:2">照片1 (3:2)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        )}

        {/* 通用选项 */}
        <div>
          <Label htmlFor="cdn">CDN区域</Label>
          <Select value={options.cdn || 'global'} onValueChange={(value) => onOptionsChange({...options, cdn: value})}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="global">全球</SelectItem>
              <SelectItem value="zh">中国</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 模型选择 */}
      <div>
        <h3 className="text-lg font-semibold mb-4">模型选择</h3>
        <AISharedSelectorFunction
          selectedModel={selectedModel?.model_id}
          modelType={modelType}
          onModelChange={onModelChange}
          showCard={false}
        />
      </div>

      {/* 选中模型的简洁说明 */}
      {selectedModel && (
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 text-sm">
            <Coins className="w-4 h-4 text-gray-500" />
            <span className="text-gray-600">
              {selectedModel.model_name} - {selectedModel.credits_per_unit} 积分/{selectedModel.unit_type}
            </span>
            {selectedModel.description && (
              <span className="text-gray-500">• {selectedModel.description}</span>
            )}
          </div>
        </div>
      )}

      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle>输入提示词</CardTitle>
          <CardDescription>
            描述您想要生成的内容
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="prompt">提示词</Label>
            <Textarea
              id="prompt"
              placeholder="请输入您的提示词..."
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              rows={4}
            />
          </div>

          {renderModelOptions()}

          {/* 成本预估 */}
          {costEstimate && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                预估消耗: {costEstimate.cost_estimate.estimated_credits} 积分
                {!costEstimate.user_credits.can_afford && (
                  <span className="text-red-600 ml-2">
                    (积分不足，还需 {costEstimate.user_credits.shortfall} 积分)
                  </span>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* 积分显示 - 紧贴生成按钮上方 */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Coins className="w-4 h-4" />
            <span>当前积分余额: {userCredits}</span>
          </div>

          <Button
            onClick={onGenerate}
            disabled={loading || !selectedModel || !prompt.trim() || (costEstimate && !costEstimate.user_credits.can_afford)}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                生成中...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                开始生成
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
