"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import type { CostEstimate as CostEstimateType } from "./types";

interface CostEstimateProps {
  costEstimate: CostEstimateType | null;
}

export function CostEstimate({ costEstimate }: CostEstimateProps) {
  if (!costEstimate) return null;

  return (
    <Alert className="bg-gradient-to-r from-accent/10 to-primary/10 border-border/30 backdrop-blur-sm w-full max-w-full overflow-x-hidden">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        Estimated cost: {costEstimate.cost_estimate.estimated_credits} credits
        {!costEstimate.user_credits.can_afford && (
          <span className="text-destructive ml-2">
            (Not enough credits, need {costEstimate.user_credits.shortfall} credits)
          </span>
        )}
      </AlertDescription>
    </Alert>
  );
}
