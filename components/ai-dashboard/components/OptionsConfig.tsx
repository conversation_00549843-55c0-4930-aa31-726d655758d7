"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  RichSelect,
  RichSelectContent,
  RichSelectItem,
  RichSelectTrigger,
  RichSelectValue,
} from "@/components/ui/rich-select";
import { Upload, X } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import type { AIModel, GenerationOptions } from "./types";

interface OptionsConfigProps {
  selectedModel: AIModel | null;
  options: GenerationOptions;
  onOptionsChange: (options: GenerationOptions) => void;
}

export function OptionsConfig({
  selectedModel,
  options,
  onOptionsChange
}: OptionsConfigProps) {
  const [uploadedImages, setUploadedImages] = useState<string[]>(options.uploadedImages || []);
  const [uploading, setUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // 同步外部 options 变化到本地状态
  useEffect(() => {
    setUploadedImages(options.uploadedImages || []);
  }, [options.uploadedImages]);

  // 防止页面默认的拖放行为
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault();
    };

    const handlePageDrop = (e: DragEvent) => {
      e.preventDefault();
      // 如果不是在我们的拖放区域内，则阻止默认行为
      if (!dropZoneRef.current?.contains(e.target as Node)) {
        return false;
      }
    };

    document.addEventListener('dragover', preventDefault);
    document.addEventListener('drop', handlePageDrop);

    return () => {
      document.removeEventListener('dragover', preventDefault);
      document.removeEventListener('drop', handlePageDrop);
    };
  }, []);

  if (!selectedModel) return null;

  const { model_type, supported_features = [] } = selectedModel;

  const updateOptions = (newOptions: Partial<GenerationOptions>) => {
    onOptionsChange({ ...options, ...newOptions });
  };

  // 处理文件上传的通用函数
  const processFileUpload = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/ai/upload-image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.code === 0) {
        const newImages = [...uploadedImages, result.data.url];
        setUploadedImages(newImages);
        updateOptions({ uploadedImages: newImages });
      } else {
        alert(result.msg || '上传失败');
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('上传失败，请重试');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await processFileUpload(file);
  };

  // 拖放事件处理函数
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev + 1);

    // 检查是否包含文件
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => {
      const newCount = prev - 1;
      if (newCount === 0) {
        setIsDragOver(false);
      }
      return newCount;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 设置拖放效果
    if (e.dataTransfer.types.includes('Files')) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragOver(false);
    setDragCounter(0);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // 过滤出图片文件
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert('请拖放图片文件');
      return;
    }

    // 如果有多个图片文件，提示用户只会处理第一个
    if (imageFiles.length > 1) {
      alert(`检测到 ${imageFiles.length} 个图片文件，将上传第一个：${imageFiles[0].name}`);
    }

    // 处理第一个图片文件
    await processFileUpload(imageFiles[0]);
  };

  const removeImage = (index: number) => {
    const newImages = uploadedImages.filter((_, i) => i !== index);
    setUploadedImages(newImages);
    updateOptions({ uploadedImages: newImages });
  };

  const supportsImageUpload = supported_features.includes('image_upload');

  return (
    <div className="space-y-2 w-full max-w-full overflow-x-hidden">
      {/* 图片上传功能 - 支持的模型显示 */}
      {supportsImageUpload && (
        <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
          <Label className="text-sm font-medium text-foreground mb-2 block">
            {model_type === 'video' ? '首帧图片' :
             model_type === 'image' ? '参考图片' : '上传图片'}
          </Label>

          <div className="space-y-2">
            {/* 拖放上传区域 */}
            <div
              ref={dropZoneRef}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              className={`
                relative border-2 border-dashed rounded-xl p-6 transition-all duration-300 cursor-pointer
                ${isDragOver
                  ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg shadow-primary/20'
                  : 'border-border/50 hover:border-border hover:bg-muted/20'
                }
                ${uploading ? 'pointer-events-none opacity-50' : ''}
                group
              `}
              onClick={() => !uploading && fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center justify-center gap-3 text-center">
                <div className={`
                  p-3 rounded-full transition-all duration-300
                  ${isDragOver
                    ? 'bg-primary text-primary-foreground scale-110 animate-pulse'
                    : 'bg-muted group-hover:bg-muted/80'
                  }
                `}>
                  <Upload className={`w-6 h-6 transition-transform duration-300 ${
                    isDragOver ? 'scale-110' : 'group-hover:scale-105'
                  }`} />
                </div>

                <div className="space-y-1">
                  <p className={`text-sm font-medium transition-colors duration-300 ${
                    isDragOver ? 'text-primary' : 'text-foreground'
                  }`}>
                    {uploading ? '正在上传图片...' :
                     isDragOver ? '松开鼠标完成上传' : '点击选择或拖拽图片到此处'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    支持 JPG、PNG、GIF、WebP 等格式，最大 10MB
                  </p>
                  {isDragOver && (
                    <p className="text-xs text-primary font-medium animate-bounce">
                      检测到图片文件，松开即可上传
                    </p>
                  )}
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>

            {/* 已上传图片预览 */}
            {uploadedImages.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {uploadedImages.map((imageUrl, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={imageUrl}
                      alt={`上传图片 ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg border border-border/50"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeImage(index)}
                      className="absolute top-1 right-1 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 文本生成选项 */}
      {model_type === 'text' && (
        <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-full">
            <div>
              <Label htmlFor="max_tokens" className="text-sm font-medium text-foreground">最大输出长度</Label>
              <Input
                id="max_tokens"
                type="number"
                placeholder="1000"
                value={options.max_tokens || ''}
                onChange={(e) => updateOptions({ max_tokens: parseInt(e.target.value) || 1000 })}
                className="mt-1 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border rounded-xl w-full max-w-full"
              />
            </div>
            <div>
              <Label htmlFor="temperature" className="text-sm font-medium text-foreground">创造性 (0-1)</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="0.7"
                value={options.temperature || ''}
                onChange={(e) => updateOptions({ temperature: parseFloat(e.target.value) || 0.7 })}
                className="mt-1 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border rounded-xl w-full max-w-full"
              />
            </div>
          </div>
        </div>
      )}

      {/* 图像生成选项 */}
      {model_type === 'image' && (
        <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-full">
            {supported_features.includes('variants') && (
              <div>
                <Label htmlFor="variants" className="text-sm font-medium text-foreground">生成数量</Label>
                <RichSelect
                  value={options.variants?.toString() || '1'}
                  onValueChange={(value) => updateOptions({ variants: parseInt(value) })}
                >
                  <RichSelectTrigger size="lg" className="mt-1 w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl">
                    <RichSelectValue />
                  </RichSelectTrigger>
                  <RichSelectContent className="z-[150]">
                    <RichSelectItem value="1" option={{value: "1", label: "1张", description: "生成1张图片"}}>1张</RichSelectItem>
                    <RichSelectItem value="2" option={{value: "2", label: "2张", description: "生成2张图片"}}>2张</RichSelectItem>
                  </RichSelectContent>
                </RichSelect>
              </div>
            )}

            {(supported_features.includes('aspectRatio') || supported_features.includes('size')) && (
              <div>
                <Label htmlFor="size" className="text-sm font-medium text-foreground">图像尺寸</Label>
                <RichSelect
                  value={options.size || options.aspectRatio || '1:1'}
                  onValueChange={(value) => updateOptions({ size: value, aspectRatio: value })}
                >
                  <RichSelectTrigger size="lg" className="mt-1 w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl">
                    <RichSelectValue />
                  </RichSelectTrigger>
                  <RichSelectContent className="z-[150]">
                    <RichSelectItem value="1:1" option={{value: "1:1", label: "正方形", description: "1:1 比例"}}>正方形 (1:1)</RichSelectItem>
                    <RichSelectItem value="16:9" option={{value: "16:9", label: "横屏", description: "16:9 比例"}}>横屏 (16:9)</RichSelectItem>
                    <RichSelectItem value="9:16" option={{value: "9:16", label: "竖屏", description: "9:16 比例"}}>竖屏 (9:16)</RichSelectItem>
                    <RichSelectItem value="4:3" option={{value: "4:3", label: "标准", description: "4:3 比例"}}>标准 (4:3)</RichSelectItem>
                    <RichSelectItem value="3:2" option={{value: "3:2", label: "照片", description: "3:2 比例"}}>照片 (3:2)</RichSelectItem>
                  </RichSelectContent>
                </RichSelect>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
