"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquare, Image, Video } from "lucide-react";
import { ResultDisplay } from "./ResultDisplay";
import type { WorkspaceGenerationResult } from "./types";

interface ResultCardProps {
  activeTab: string;
  generationResult: WorkspaceGenerationResult | null;
  isFullscreen: boolean;
  isMobile: boolean;
}

export function ResultCard({
  activeTab,
  generationResult,
  isFullscreen,
  isMobile
}: ResultCardProps) {
  return (
    <Card className={`${
      isFullscreen
        ? 'h-full max-h-full overflow-hidden'  // 全屏模式：严格限制高度，防止溢出
        : isMobile
          ? 'min-h-[60vh]'
          : 'h-full'
    } flex flex-col bg-gradient-to-br from-card via-card to-accent/5`}
    style={isFullscreen ? { height: '100%', maxHeight: '100%' } : undefined}>
      <CardHeader className="border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10 flex-shrink-0">
        <CardTitle className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${
            activeTab === 'text' ? 'bg-gradient-to-r from-blue-500 to-purple-500' :
            activeTab === 'image' ? 'bg-gradient-to-r from-green-500 to-teal-500' :
            'bg-gradient-to-r from-orange-500 to-red-500'
          }`}>
            {activeTab === 'text' && <MessageSquare className="w-5 h-5 text-white" />}
            {activeTab === 'image' && <Image className="w-5 h-5 text-white" />}
            {activeTab === 'video' && <Video className="w-5 h-5 text-white" />}
          </div>
          <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            GENERATION RESULT
          </span>
        </CardTitle>
        <CardDescription className="text-muted-foreground/80">
          {activeTab === 'text' && 'The result of text generation will be displayed here, supporting copy and export'}
          {activeTab === 'image' && 'The result of image generation will be displayed here, supporting preview and download'}
          {activeTab === 'video' && 'The result of video generation will be displayed here, supporting play and download'}
        </CardDescription>
      </CardHeader>
      <CardContent
        className={`flex-1 min-h-0 p-6 ${
          isFullscreen
            ? 'overflow-y-auto overflow-x-hidden'  // 全屏模式：强制内部滚动
            : 'overflow-y-auto'
        }`}
        style={isFullscreen ? {
          height: 'calc(100% - 120px)',  // 减去header高度
          maxHeight: 'calc(100% - 120px)',
          overflowY: 'auto',
          overflowX: 'hidden'
        } : undefined}>
        <ResultDisplay generationResult={generationResult} />
      </CardContent>
    </Card>
  );
}
