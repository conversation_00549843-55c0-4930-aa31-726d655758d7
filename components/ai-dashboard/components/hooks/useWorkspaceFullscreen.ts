"use client";

import { useRef, useEffect } from "react";
import { useFullscreen } from "@/hooks/use-fullscreen";

export function useWorkspaceFullscreen() {
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const moduleRef = useRef<HTMLDivElement>(null);

  // 全屏时的滚动控制
  useEffect(() => {
    if (isFullscreen) {
      // 禁用 body 滚动
      document.body.classList.add('overflow-hidden');
      document.documentElement.classList.add('overflow-hidden');
    } else {
      // 恢复 body 滚动
      document.body.classList.remove('overflow-hidden');
      document.documentElement.classList.remove('overflow-hidden');
    }

    // 组件卸载时清理
    return () => {
      document.body.classList.remove('overflow-hidden');
      document.documentElement.classList.remove('overflow-hidden');
    };
  }, [isFullscreen]);

  const handleFullscreenToggle = () => {
    // 使用CSS全屏，让AIModelModule组件占满浏览器窗口
    console.log('全屏按钮被点击', moduleRef.current);
    if (moduleRef.current) {
      console.log('元素存在，调用toggleFullscreen');
      toggleFullscreen(moduleRef.current);
    } else {
      console.log('元素不存在');
    }
  };

  return {
    isFullscreen,
    moduleRef,
    handleFullscreenToggle
  };
}
