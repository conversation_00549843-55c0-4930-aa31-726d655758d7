"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Maximize } from "lucide-react";

interface WorkspaceHeaderProps {
  onEnterFullscreen: () => void;
}

export function WorkspaceHeader({ onEnterFullscreen }: WorkspaceHeaderProps) {
  return (
    <div className="px-6 py-12 bg-gradient-to-br from-background/50 via-background to-accent/5 backdrop-blur-sm">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg">
            <Brain className="w-8 h-8 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              AI Workspace
            </h1>
            <p className="text-muted-foreground mt-1">Unleash Unlimited Creativity</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="lg"
          onClick={onEnterFullscreen}
          className="flex items-center gap-2"
        >
          <Maximize className="w-5 h-5" />
          Full Screen
        </Button>
      </div>
    </div>
  );
}
