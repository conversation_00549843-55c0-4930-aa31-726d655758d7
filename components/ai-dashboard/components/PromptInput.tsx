"use client";

import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface PromptInputProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
}

export function PromptInput({ prompt, onPromptChange }: PromptInputProps) {
  return (
    <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
      <Label htmlFor="prompt" className="text-sm font-medium text-foreground">Prompt</Label>
      <Textarea
        id="prompt"
        placeholder="Enter your prompt..."
        value={prompt}
        onChange={(e) => onPromptChange(e.target.value)}
        rows={4}
        className="mt-2 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border resize-none rounded-xl w-full max-w-full"
      />
    </div>
  );
}
