"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, Image, Video } from "lucide-react";
import type { ModelTypeConfig } from "./types";

interface ModelTypeSelectorProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  isFullscreen: boolean;
  isDesktop: boolean;
  typeButtonClassName: string;
}

export function ModelTypeSelector({
  activeTab,
  onTabChange,
  isFullscreen,
  isDesktop,
  typeButtonClassName
}: ModelTypeSelectorProps) {
  const modelTypes: ModelTypeConfig[] = [
    { value: 'text', label: 'TEXT', icon: MessageSquare, color: 'from-blue-500 to-purple-500' },
    { value: 'image', label: 'IMAGE', icon: Image, color: 'from-green-500 to-teal-500' },
    { value: 'video', label: 'VIDEO', icon: Video, color: 'from-orange-500 to-red-500' }
  ];

  return (
    <div className="space-y-3">
      {modelTypes.map(({ value, label, icon: Icon, color }) => (
        <Button
          key={value}
          variant={activeTab === value ? "default" : "ghost"}
          className={`${typeButtonClassName} transition-all duration-200 ${
            activeTab === value
              ? `bg-gradient-to-r ${color} text-white shadow-lg`
              : 'hover:bg-accent/50'
          }`}
          onClick={() => onTabChange(value)}
        >
          <div className={`p-2 rounded-lg ${activeTab === value ? 'bg-white/20' : 'bg-accent/20'}`}>
            <Icon className="w-4 h-4" />
          </div>
          {(!isDesktop || !isFullscreen) && <span className="font-medium">{label}</span>}
          {isFullscreen && isDesktop && (
            <span className="text-xs font-medium text-center leading-tight">{label.replace('Generate', '')}</span>
          )}
        </Button>
      ))}
    </div>
  );
}
