import { MessageSquare, Image, Video, Zap } from "lucide-react";

export interface AIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  credits_per_unit: number;
  unit_type: string;
  description?: string;
  supported_features?: string[];
  icon?: string;
}

export interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export interface AINonFullMainProps {
  modelType?: string;
  onResultChange?: (result: GenerationResult | null) => void;
  onGeneratingChange?: (isGenerating: boolean) => void;
}

export interface GenerationOptions {
  size?: string;
  aspectRatio?: string;
  variants?: number;
  temperature?: number;
  max_tokens?: number;
  cdn?: string;
  uploadedImages?: string[];
  referenceImages?: string[];
  firstFrameUrl?: string;
}

export interface CostEstimate {
  cost_estimate: {
    estimated_credits: number;
  };
  user_credits: {
    can_afford: boolean;
    shortfall?: number;
  };
}

export const MODEL_TYPE_ICONS = {
  text: MessageSquare,
  image: Image,
  video: Video,
  multimodal: Zap
};

export interface WorkspaceGenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export interface ModelTypeConfig {
  value: string;
  label: string;
  icon: any;
  color: string;
}


