"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Brain, Minimize } from "lucide-react";

interface FullscreenToolbarProps {
  onExitFullscreen: () => void;
}

export function FullscreenToolbar({ onExitFullscreen }: FullscreenToolbarProps) {
  return (
    <div className="sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 px-6 py-4 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="p-2 rounded-xl bg-gradient-to-r from-primary to-accent">
            <Brain className="w-6 h-6 text-primary-foreground" />
          </div>
          <h1 className="text-xl font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            AI Workspace - Full Screen
          </h1>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onExitFullscreen}
          className="flex items-center gap-2 z-50 relative"
        >
          <Minimize className="w-4 h-4" />
          Exit Full
        </Button>
      </div>
    </div>
  );
}
