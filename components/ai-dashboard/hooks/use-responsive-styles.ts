"use client";

import { useMemo } from 'react';
import { useDeviceLayout, type DeviceType } from './use-device-layout';

/**
 * 响应式样式配置接口
 * 用于定义不同设备类型下的样式
 *
 * 例如：
 * {
 *   mobile: { width: 'w-full', padding: 'p-4' },
 *   tablet: { width: 'w-1/2', padding: 'p-6' },
 *   desktop: { width: 'w-1/3', padding: 'p-8' }
 * }
 */
export interface ResponsiveStyleConfig {
  mobile?: Record<string, string>;   // 移动端样式（< 768px）
  tablet?: Record<string, string>;   // 平板样式（768px - 1199px）
  desktop?: Record<string, string>;  // 桌面端样式（>= 1200px）
}

/**
 * 预定义的样式模板库
 * 包含常用组件的响应式样式配置
 *
 * 作用：提供开箱即用的样式模板，减少重复代码
 * 使用：通过 useResponsiveStyles(config, 'templateName') 调用
 */
export const STYLE_TEMPLATES = {
  // 容器样式模板 - 用于主要的布局容器
  container: {
    mobile: {
      display: 'flex flex-col',    // 移动端垂直布局
      gap: 'gap-4',               // 间距4
      padding: 'p-4',             // 内边距4
      width: 'w-full',            // 全宽
      height: 'h-auto',           // 高度自适应
    },
    tablet: {
      display: 'grid grid-cols-5', // 平板5列网格
      gap: 'gap-6',               // 间距6
      padding: 'p-6',             // 内边距6
      width: 'w-full',            // 全宽
      height: 'h-auto',           // 高度自适应
    },
    desktop: {
      display: 'grid grid-cols-10', // 桌面10列网格
      gap: 'gap-6',                // 间距6
      padding: 'p-6',              // 内边距6
      width: 'w-full',             // 全宽
      height: 'h-full',            // 全高
    },
  },

  // 侧边栏样式模板 - 用于左侧导航或工具栏
  sidebar: {
    mobile: {
      width: 'w-full',           // 移动端全宽
      height: 'h-auto',          // 高度自适应
      order: 'order-1',          // 显示顺序第一
    },
    tablet: {
      width: 'col-span-2',       // 平板占2列
      height: 'h-auto',          // 高度自适应
      order: 'order-none',       // 默认顺序
    },
    desktop: {
      width: 'col-span-1',       // 桌面占1列
      height: 'h-full',          // 全高
      order: 'order-none',       // 默认顺序
    },
  },

  // 主内容区样式模板 - 用于主要内容展示
  main: {
    mobile: {
      width: 'w-full',           // 移动端全宽
      height: 'h-auto',          // 高度自适应
      order: 'order-2',          // 显示顺序第二
    },
    tablet: {
      width: 'col-span-2',       // 平板占2列
      height: 'h-auto',          // 高度自适应
      order: 'order-none',       // 默认顺序
    },
    desktop: {
      width: 'col-span-3',       // 桌面占3列
      height: 'h-full',          // 全高
      order: 'order-none',       // 默认顺序
    },
  },

  // 结果区样式模板 - 用于结果展示区域
  result: {
    mobile: {
      width: 'w-full',           // 移动端全宽
      height: 'h-auto',          // 高度自适应
      order: 'order-3',          // 显示顺序第三
    },
    tablet: {
      width: 'col-span-3',       // 平板占3列
      height: 'h-auto',          // 高度自适应
      order: 'order-none',       // 默认顺序
    },
    desktop: {
      width: 'col-span-6',       // 桌面占6列
      height: 'h-full',          // 全高
      order: 'order-none',       // 默认顺序
    },
  },
  
  // 卡片样式模板
  card: {
    mobile: {
      padding: 'p-4',
      margin: 'm-0',
      borderRadius: 'rounded-lg',
      background: 'bg-card',
    },
    tablet: {
      padding: 'p-6',
      margin: 'm-0',
      borderRadius: 'rounded-xl',
      background: 'bg-card',
    },
    desktop: {
      padding: 'p-6',
      margin: 'm-0',
      borderRadius: 'rounded-xl',
      background: 'bg-card',
    },
  },
  
  // 按钮样式模板
  button: {
    mobile: {
      size: 'h-12 px-4',
      text: 'text-sm',
      gap: 'gap-2',
      direction: 'flex-row',
    },
    tablet: {
      size: 'h-12 px-4',
      text: 'text-base',
      gap: 'gap-2',
      direction: 'flex-row',
    },
    desktop: {
      size: 'h-12 px-4',
      text: 'text-base',
      gap: 'gap-3',
      direction: 'flex-row',
    },
  },
} as const;

/**
 * 样式合并工具函数
 * 将样式对象的值合并为单个CSS类名字符串
 *
 * @param styles - 样式对象，如 { width: 'w-full', padding: 'p-4' }
 * @returns 合并后的类名字符串，如 'w-full p-4'
 */
function mergeStyles(styles: Record<string, string>): string {
  return Object.values(styles).filter(Boolean).join(' ');
}

/**
 * 获取设备特定样式
 * 根据当前设备类型从配置中提取对应的样式
 *
 * @param config - 响应式样式配置
 * @param deviceType - 设备类型
 * @returns 当前设备的样式对象
 */
function getDeviceStyles(
  config: ResponsiveStyleConfig,
  deviceType: DeviceType
): Record<string, string> {
  return config[deviceType] || config.desktop || {};
}

/**
 * 响应式样式Hook
 * 根据当前设备类型自动选择合适的样式
 *
 * @param config - 自定义样式配置
 * @param templateName - 可选的预定义模板名称
 * @returns 包含样式对象、类名字符串和设备类型的对象
 *
 * 使用示例：
 * const { className } = useResponsiveStyles({
 *   mobile: { width: 'w-full' },
 *   desktop: { width: 'w-1/2' }
 * });
 */
export function useResponsiveStyles(
  config: ResponsiveStyleConfig,
  templateName?: keyof typeof STYLE_TEMPLATES
) {
  const { deviceType } = useDeviceLayout();

  return useMemo(() => {
    // 获取模板样式（如果指定了模板名称）
    const templateStyles = templateName
      ? getDeviceStyles(STYLE_TEMPLATES[templateName], deviceType)
      : {};

    // 获取自定义样式
    const customStyles = getDeviceStyles(config, deviceType);

    // 合并样式（自定义样式优先级更高，会覆盖模板样式）
    const mergedStyles = { ...templateStyles, ...customStyles };

    // 返回样式对象和合并后的类名字符串
    return {
      styles: mergedStyles,        // 原始样式对象
      className: mergeStyles(mergedStyles), // 合并后的CSS类名
      deviceType,                  // 当前设备类型
    };
  }, [config, templateName, deviceType]);
}

// 快捷样式 hooks
export function useContainerStyles(customConfig?: ResponsiveStyleConfig) {
  return useResponsiveStyles(customConfig || {}, 'container');
}

export function useSidebarStyles(customConfig?: ResponsiveStyleConfig) {
  return useResponsiveStyles(customConfig || {}, 'sidebar');
}

export function useMainStyles(customConfig?: ResponsiveStyleConfig) {
  return useResponsiveStyles(customConfig || {}, 'main');
}

export function useResultStyles(customConfig?: ResponsiveStyleConfig) {
  return useResponsiveStyles(customConfig || {}, 'result');
}

export function useCardStyles(customConfig?: ResponsiveStyleConfig) {
  return useResponsiveStyles(customConfig || {}, 'card');
}

export function useButtonStyles(customConfig?: ResponsiveStyleConfig) {
  return useResponsiveStyles(customConfig || {}, 'button');
}
