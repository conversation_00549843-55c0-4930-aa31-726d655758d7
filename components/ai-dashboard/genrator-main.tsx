"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Send } from "lucide-react";

// 导入响应式样式hooks
import { useCardStyles } from "./hooks/use-responsive-styles";
import { useDeviceLayout } from "./hooks/use-device-layout";

// 导入拆分的组件
import {
  ModelSelector,
  OptionsConfig,
  CostEstimate,
  CreditsDisplay,
  GenerateButton,
  PromptInput,
  useAIGeneration,
  type AINonFullMainProps
} from "./components";

export function GeneratorMain({
  modelType,
  onResultChange,
  onGeneratingChange
}: AINonFullMainProps = {}) {
  // 使用自定义hook管理所有状态和逻辑
  const {
    selectedModel,
    models,
    modelsLoading,
    modelsError,
    prompt,
    setPrompt,
    options,
    setOptions,
    loading,
    costEstimate,
    userCredits,
    handleGenerate,
    handleModelSelect
  } = useAIGeneration(modelType, onResultChange, onGeneratingChange);

  // 使用响应式样式
  const { isMobile } = useDeviceLayout();
  const { className: cardClassName } = useCardStyles();

  return (
    <div className="space-y-6 w-full max-w-full overflow-x-hidden">
      {/* 统一的输入配置卡片 - 使用响应式样式 */}
      <Card className={`${cardClassName} bg-gradient-to-br from-card via-card to-accent/5 gap-4! w-full max-w-full overflow-x-hidden`}>
        <CardHeader className="border-b pb-0! border-border/30 bg-gradient-to-r from-muted/20 to-muted/10">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-primary to-accent">
              <Send className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              AI GENERATION CONFIG
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className={`space-y-2 pt-0 w-full max-w-full overflow-x-hidden ${isMobile ? 'px-2 py-1' : 'px-4 py-2'}`}>
          {/* 模型选择组件 */}
          <ModelSelector
            selectedModel={selectedModel}
            models={models}
            modelsLoading={modelsLoading}
            modelsError={modelsError}
            onModelSelect={handleModelSelect}
          />

          {/* 提示词输入组件 */}
          <PromptInput
            prompt={prompt}
            onPromptChange={setPrompt}
          />

          {/* 选项配置组件 */}
          <OptionsConfig
            selectedModel={selectedModel}
            options={options}
            onOptionsChange={setOptions}
          />

          {/* 成本预估组件 */}
          <CostEstimate costEstimate={costEstimate} />

          {/* 积分显示组件 */}
          <CreditsDisplay userCredits={userCredits} />

          {/* 生成按钮组件 */}
          <GenerateButton
            loading={loading}
            selectedModel={selectedModel}
            prompt={prompt}
            costEstimate={costEstimate}
            onGenerate={handleGenerate}
          />
        </CardContent>
      </Card>
    </div>
  );
}
