"use client";

import "./markdown.css";
import dynamic from "next/dynamic";

// 我们不再从库中导入任何 props 类型了
// import type { ... } from '...'; <-- 这行被彻底删除了

// 这部分逻辑是正确的，它能成功加载组件，所以我们保留它
const MarkdownPreview = dynamic(
  () => import("@uiw/react-md-editor").then((mod) => mod.default.Markdown),
  {
    ssr: false,
    loading: () => (
      <div className="markdown bg-background">
        <p>加载中...</p>
      </div>
    ),
  }
);

export default function Markdown({ content }: { content: string }) {
  // 我们直接使用动态加载的 MarkdownPreview 组件。
  // TypeScript 足够聪明，即使我们没有提供明确的类型，
  // 它也能从组件本身推断出可以接受哪些 props。
  return (
    <MarkdownPreview
      className="markdown bg-background"
      source={content}
      components={{
        a: ({ children, ...props }) => (
          <a {...props} target="_blank" rel="noopener noreferrer">
            {children}
          </a>
        ),
      }}
    />
  );
}