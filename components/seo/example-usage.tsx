/**
 * Schema 组件使用示例
 * 这个文件展示了如何在实际项目中使用 Schema 组件
 */

import React from 'react';
import Schema, { SimpleSchema, createGameSchema, createBreadcrumbSchema } from './index';
import type { SchemaProps, BreadcrumbItem } from '@/types/schema';

// 示例 1: 游戏详情页面
export function GameDetailPage() {
  const gameData: SchemaProps = {
    title: "超级马里奥兄弟",
    description: "经典的平台跳跃游戏，与马里奥一起冒险拯救公主。这款游戏自1985年发布以来，一直是任天堂最受欢迎的游戏之一。",
    image: "https://example.com/images/super-mario-bros.jpg",
    url: "https://example.com/games/super-mario-bros",
    datePublished: "1985-09-13T00:00:00Z",
    dateModified: "2024-01-15T10:30:00Z",
    ratingValue: 4.8,
    ratingCount: 15420,
    operatingSystem: "Nintendo Entertainment System, Nintendo Switch, PC",
    breadcrumb: [
      { name: "游戏", url: "/games" },
      { name: "平台游戏", url: "/games/platform" },
      { name: "任天堂", url: "/games/nintendo" }
    ]
  };

  return (
    <div>
      <Schema {...gameData} />
      <h1>{gameData.title}</h1>
      <p>{gameData.description}</p>
      {/* 其他页面内容 */}
    </div>
  );
}

// 示例 2: 游戏分类页面
export function GameCategoryPage() {
  const categoryData: SchemaProps = {
    title: "动作冒险游戏",
    description: "探索我们精选的动作冒险游戏集合，包含刺激的战斗和引人入胜的故事情节。",
    image: "https://example.com/images/action-adventure-games.jpg",
    url: "https://example.com/games/action-adventure",
    breadcrumb: [
      { name: "游戏", url: "/games" },
      { name: "动作冒险", url: "/games/action-adventure" }
    ]
  };

  return (
    <div>
      <Schema {...categoryData} />
      <h1>动作冒险游戏</h1>
      {/* 游戏列表 */}
    </div>
  );
}

// 示例 3: 简单页面（使用 SimpleSchema）
export function AboutPage() {
  return (
    <div>
      <SimpleSchema
        title="关于我们"
        description="了解我们的团队、使命和愿景。我们致力于为玩家提供最优质的游戏体验。"
        image="https://example.com/images/about-us.jpg"
        url="https://example.com/about"
      />
      <h1>关于我们</h1>
      {/* 页面内容 */}
    </div>
  );
}

// 示例 4: 动态生成 Schema 数据
export function DynamicGamePage({ gameId }: { gameId: string }) {
  // 模拟从 API 获取游戏数据
  const gameData = React.useMemo(() => {
    const categories = {
      'mario': { name: '平台游戏', url: '/games/platform' },
      'zelda': { name: '动作冒险', url: '/games/action-adventure' },
      'pokemon': { name: 'RPG', url: '/games/rpg' }
    };

    const category = categories[gameId as keyof typeof categories] || categories.mario;

    return {
      title: `${gameId.charAt(0).toUpperCase() + gameId.slice(1)} 游戏`,
      description: `体验 ${gameId} 系列的精彩游戏世界`,
      image: `https://example.com/images/${gameId}.jpg`,
      url: `https://example.com/games/${gameId}`,
      breadcrumb: [
        { name: "游戏", url: "/games" },
        category
      ]
    };
  }, [gameId]);

  return (
    <div>
      <Schema {...gameData} />
      <h1>{gameData.title}</h1>
      {/* 动态内容 */}
    </div>
  );
}

// 示例 5: 服务端组件中的使用（Next.js App Router）
export async function ServerGamePage({ params }: { params: { id: string } }) {
  // 模拟服务端数据获取
  const game = await fetchGameData(params.id);
  
  const schemaData: SchemaProps = {
    title: game.title,
    description: game.description,
    image: game.coverImage,
    url: `${process.env.NEXT_PUBLIC_WEB_URL}/games/${params.id}`,
    datePublished: game.publishedAt,
    dateModified: game.updatedAt,
    ratingValue: game.averageRating,
    ratingCount: game.reviewCount,
    operatingSystem: game.platforms.join(', '),
    breadcrumb: [
      { name: "游戏", url: "/games" },
      { name: game.category, url: `/games/category/${game.categorySlug}` }
    ]
  };

  return (
    <div>
      <Schema {...schemaData} />
      <h1>{game.title}</h1>
      <img src={game.coverImage} alt={game.title} />
      <p>{game.description}</p>
      {/* 其他游戏详情 */}
    </div>
  );
}

// 示例 6: 手动生成 Schema 数据（用于 API 路由或其他用途）
export async function generateSchemaForAPI(gameId: string) {
  const game = await fetchGameData(gameId);
  
  const schemaData: SchemaProps = {
    title: game.title,
    description: game.description,
    image: game.coverImage,
    url: `${process.env.NEXT_PUBLIC_WEB_URL}/games/${gameId}`,
    datePublished: game.publishedAt,
    breadcrumb: [
      { name: "游戏", url: "/games" },
      { name: game.category, url: `/games/category/${game.categorySlug}` }
    ]
  };

  // 生成应用 schema
  const appSchema = await createGameSchema(schemaData);
  
  // 生成面包屑 schema
  const breadcrumbSchema = createBreadcrumbSchema(schemaData.url, schemaData.breadcrumb);
  
  return {
    app: appSchema,
    breadcrumb: breadcrumbSchema,
    combined: [appSchema, breadcrumbSchema]
  };
}

// 模拟数据获取函数
async function fetchGameData(gameId: string) {
  // 这里应该是实际的数据库查询或 API 调用
  return {
    title: `游戏 ${gameId}`,
    description: `这是游戏 ${gameId} 的描述`,
    coverImage: `https://example.com/images/${gameId}.jpg`,
    publishedAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    averageRating: 4.5,
    reviewCount: 1000,
    platforms: ['PC', 'PlayStation', 'Xbox'],
    category: '动作游戏',
    categorySlug: 'action'
  };
}

// 示例 7: 错误处理示例
export function SchemaWithErrorHandling() {
  const [schemaData, setSchemaData] = React.useState<SchemaProps | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    async function loadData() {
      try {
        const data = await fetchGameData('example');
        setSchemaData({
          title: data.title,
          description: data.description,
          image: data.coverImage,
          url: `${window.location.origin}/games/example`,
          breadcrumb: [
            { name: "游戏", url: "/games" }
          ]
        });
      } catch (err) {
        setError('Failed to load game data');
        console.error('Schema data loading error:', err);
      }
    }

    loadData();
  }, []);

  if (error) {
    return <div>Error loading page</div>;
  }

  if (!schemaData) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <Schema {...schemaData} />
      <h1>{schemaData.title}</h1>
    </div>
  );
}
