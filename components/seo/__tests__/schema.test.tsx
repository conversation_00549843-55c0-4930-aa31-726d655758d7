/**
 * Schema 组件测试文件
 */

import {
  createSchema,
  createGameSchema,
  createArticleSchema,
  createBreadcrumbSchema
} from '../index';
import type { SchemaProps, BreadcrumbItem } from '@/types/schema';

// Mock 环境变量
const originalEnv = process.env;

beforeAll(() => {
  process.env.NEXT_PUBLIC_WEB_URL = 'https://example.com';
  process.env.NEXT_PUBLIC_PROJECT_NAME = 'TestApp';
});

afterAll(() => {
  process.env = originalEnv;
});

describe('Schema 组件测试', () => {
  const mockProps: SchemaProps = {
    title: '测试游戏',
    description: '这是一个测试游戏的描述',
    image: 'https://example.com/test-game.jpg',
    url: 'https://example.com/games/test',
    breadcrumb: [
      { name: '游戏', url: '/games' },
      { name: '动作游戏', url: '/games/action' }
    ]
  };

  describe('createGameSchema', () => {
    it('应该生成基本的游戏 schema', async () => {
      const schema = await createGameSchema(mockProps);

      expect(schema).toMatchObject({
        '@context': 'https://schema.org',
        '@type': 'SoftwareApplication',
        '@id': 'https://example.com/games/test',
        name: '测试游戏',
        description: '这是一个测试游戏的描述',
        url: 'https://example.com/games/test',
        applicationCategory: 'GameApplication'
      });

      expect(schema.image).toMatchObject({
        '@type': 'ImageObject',
        url: 'https://example.com/test-game.jpg',
        contentUrl: 'https://example.com/test-game.jpg'
      });

      expect(schema.author).toMatchObject({
        '@type': 'Organization',
        name: 'TestApp',
        url: 'https://example.com'
      });
    });

    it('应该包含可选的日期字段', async () => {
      const propsWithDates = {
        ...mockProps,
        datePublished: '2024-01-01',
        dateModified: '2024-01-15'
      };

      const schema = await createGameSchema(propsWithDates);

      expect(schema.datePublished).toBe('2024-01-01');
      expect(schema.dateModified).toBe('2024-01-15');
    });

    it('应该包含评分信息', async () => {
      const propsWithRating = {
        ...mockProps,
        ratingValue: 4.5,
        ratingCount: 100
      };

      const schema = await createGameSchema(propsWithRating);

      expect(schema.aggregateRating).toMatchObject({
        '@type': 'AggregateRating',
        ratingValue: 4.5,
        ratingCount: 100
      });
    });

    it('应该包含操作系统信息', async () => {
      const propsWithOS = {
        ...mockProps,
        operatingSystem: 'Windows, macOS'
      };

      const schema = await createGameSchema(propsWithOS);

      // 类型断言为 SoftwareApplicationSchema
      if (schema['@type'] === 'SoftwareApplication') {
        expect(schema.operatingSystem).toBe('Windows, macOS');
      }
    });

    it('应该处理缺失的环境变量', async () => {
      const originalUrl = process.env.NEXT_PUBLIC_WEB_URL;
      const originalName = process.env.NEXT_PUBLIC_PROJECT_NAME;

      process.env.NEXT_PUBLIC_WEB_URL = '';
      process.env.NEXT_PUBLIC_PROJECT_NAME = '';

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const schema = await createGameSchema(mockProps);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Schema: Missing required environment variables NEXT_PUBLIC_WEB_URL or NEXT_PUBLIC_PROJECT_NAME'
      );

      expect(schema.author.name).toBe('ShipAny'); // 默认值
      expect(schema.author.url).toBe(''); // 空字符串

      consoleSpy.mockRestore();

      // 恢复环境变量
      process.env.NEXT_PUBLIC_WEB_URL = originalUrl;
      process.env.NEXT_PUBLIC_PROJECT_NAME = originalName;
    });
  });

  describe('createSchema (通用函数)', () => {
    it('应该生成 SoftwareApplication schema', async () => {
      const props = {
        ...mockProps,
        schemaType: 'SoftwareApplication' as const,
        applicationCategory: 'GameApplication',
        operatingSystem: 'Windows, macOS'
      };

      const schema = await createSchema(props);

      expect(schema['@type']).toBe('SoftwareApplication');
      if (schema['@type'] === 'SoftwareApplication') {
        expect(schema.name).toBe('测试游戏');
        expect(schema.applicationCategory).toBe('GameApplication');
        expect(schema.operatingSystem).toBe('Windows, macOS');
      }
    });

    it('应该生成 Article schema', async () => {
      const articleProps: SchemaProps = {
        title: '测试文章',
        description: '这是一个测试文章的描述',
        image: 'https://example.com/test-article.jpg',
        url: 'https://example.com/articles/test',
        schemaType: 'Article',
        authorName: '张三',
        wordCount: 1500,
        readingTime: 8,
        tags: ['技术', '编程'],
        breadcrumb: [
          { name: '博客', url: '/blog' },
          { name: '技术', url: '/blog/tech' }
        ]
      };

      const schema = await createSchema(articleProps);

      expect(schema['@type']).toBe('Article');
      if (schema['@type'] === 'Article') {
        expect(schema.headline).toBe('测试文章');
        expect(schema.wordCount).toBe(1500);
        expect(schema.timeRequired).toBe('PT8M');
        expect(schema.keywords).toBe('技术, 编程');
      }

      expect(schema.author['@type']).toBe('Person');
      expect(schema.author.name).toBe('张三');
    });

    it('应该生成 WebPage schema', async () => {
      const webPageProps: SchemaProps = {
        title: '关于我们',
        description: '了解我们的团队',
        image: 'https://example.com/about.jpg',
        url: 'https://example.com/about',
        schemaType: 'WebPage',
        breadcrumb: []
      };

      const schema = await createSchema(webPageProps);

      expect(schema['@type']).toBe('WebPage');
      if (schema['@type'] === 'WebPage') {
        expect(schema.name).toBe('关于我们');
      }
    });
  });

  describe('createArticleSchema', () => {
    it('应该生成正确的 Article schema', async () => {
      const articleProps: SchemaProps = {
        title: 'React 18 新特性',
        description: '深入了解 React 18 的新功能',
        image: 'https://example.com/react18.jpg',
        url: 'https://example.com/articles/react-18',
        authorName: '李四',
        datePublished: '2024-01-01',
        wordCount: 2500,
        readingTime: 12,
        tags: ['React', 'JavaScript'],
        breadcrumb: [
          { name: '博客', url: '/blog' }
        ]
      };

      const schema = await createArticleSchema(articleProps);

      expect(schema['@type']).toBe('Article');
      expect(schema.headline).toBe('React 18 新特性');
      expect(schema.author.name).toBe('李四');
      expect(schema.author['@type']).toBe('Person');

      if (schema['@type'] === 'Article') {
        expect(schema.wordCount).toBe(2500);
        expect(schema.timeRequired).toBe('PT12M');
        expect(schema.keywords).toBe('React, JavaScript');
      }
    });
  });

  describe('createBreadcrumbSchema', () => {
    it('应该生成正确的面包屑 schema', () => {
      const breadcrumb: BreadcrumbItem[] = [
        { name: '游戏', url: '/games' },
        { name: '动作游戏', url: '/games/action' }
      ];

      const schema = createBreadcrumbSchema('https://example.com/games/test', breadcrumb);

      expect(schema).toMatchObject({
        '@type': 'BreadcrumbList',
        '@id': 'https://example.com/games/test#breadcrumb'
      });

      expect(schema.itemListElement).toHaveLength(3); // 首页 + 2个面包屑项

      expect(schema.itemListElement[0]).toMatchObject({
        '@type': 'ListItem',
        position: 1,
        name: '首页',
        item: 'https://example.com'
      });

      expect(schema.itemListElement[1]).toMatchObject({
        '@type': 'ListItem',
        position: 2,
        name: '游戏',
        item: 'https://example.com/games'
      });

      expect(schema.itemListElement[2]).toMatchObject({
        '@type': 'ListItem',
        position: 3,
        name: '动作游戏',
        item: 'https://example.com/games/action'
      });
    });

    it('应该处理空的面包屑数组', () => {
      const schema = createBreadcrumbSchema('https://example.com/test', []);

      expect(schema.itemListElement).toHaveLength(1); // 只有首页
      expect(schema.itemListElement[0].name).toBe('首页');
    });
  });

  describe('类型安全测试', () => {
    it('应该要求必需的属性', () => {
      // TypeScript 编译时检查
      const validProps: SchemaProps = {
        title: '标题',
        description: '描述',
        image: 'https://example.com/image.jpg',
        url: 'https://example.com/page',
        breadcrumb: []
      };

      expect(validProps).toBeDefined();
    });

    it('应该允许可选属性', () => {
      const propsWithOptional: SchemaProps = {
        title: '标题',
        description: '描述',
        image: 'https://example.com/image.jpg',
        url: 'https://example.com/page',
        breadcrumb: [],
        datePublished: '2024-01-01',
        dateModified: '2024-01-15',
        applicationCategory: 'GameApplication',
        operatingSystem: 'Windows',
        ratingValue: 4.5,
        ratingCount: 100
      };

      expect(propsWithOptional).toBeDefined();
    });
  });
});
