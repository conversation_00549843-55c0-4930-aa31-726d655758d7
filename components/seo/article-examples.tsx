/**
 * Article Schema 组件使用示例
 * 展示如何在不同类型的文章页面中使用 Article Schema
 */

import React from 'react';
import { ArticleSchema, createArticleSchema } from './index';
import type { SchemaProps } from '@/types/schema';

// 示例 1: 技术博客文章
export function TechBlogPost() {
  return (
    <div>
      <ArticleSchema
        title="React 18 并发特性深度解析"
        description="深入探讨 React 18 的并发渲染、自动批处理、Suspense 改进等核心特性，以及它们如何提升应用性能。"
        image="https://example.com/images/react18-concurrent.jpg"
        url="https://example.com/blog/react-18-concurrent-features"
        authorName="张三"
        datePublished="2024-01-15T10:00:00Z"
        dateModified="2024-01-20T14:30:00Z"
        wordCount={3500}
        readingTime={18}
        tags={["React", "JavaScript", "前端开发", "并发渲染", "性能优化"]}
        ratingValue={4.7}
        ratingCount={156}
        breadcrumb={[
          { name: "博客", url: "/blog" },
          { name: "前端技术", url: "/blog/frontend" },
          { name: "React", url: "/blog/frontend/react" }
        ]}
      />
      
      <article>
        <h1>React 18 并发特性深度解析</h1>
        <div className="article-meta">
          <span>作者：张三</span>
          <span>发布时间：2024年1月15日</span>
          <span>阅读时间：约18分钟</span>
          <span>字数：3500字</span>
        </div>
        <div className="article-tags">
          {["React", "JavaScript", "前端开发", "并发渲染", "性能优化"].map(tag => (
            <span key={tag} className="tag">{tag}</span>
          ))}
        </div>
        {/* 文章内容 */}
      </article>
    </div>
  );
}

// 示例 2: 新闻文章
export function NewsArticle() {
  return (
    <div>
      <ArticleSchema
        title="2024年Web开发技术趋势报告"
        description="基于全球开发者调研数据，分析2024年最受关注的Web开发技术、框架和工具的发展趋势。"
        image="https://example.com/images/web-dev-trends-2024.jpg"
        url="https://example.com/news/web-development-trends-2024"
        authorName="李四"
        datePublished="2024-01-01T09:00:00Z"
        wordCount={2800}
        readingTime={14}
        tags={["Web开发", "技术趋势", "前端框架", "开发工具", "行业报告"]}
        breadcrumb={[
          { name: "新闻", url: "/news" },
          { name: "行业动态", url: "/news/industry" }
        ]}
      />
      
      <article>
        <h1>2024年Web开发技术趋势报告</h1>
        <div className="news-meta">
          <span>记者：李四</span>
          <span>发布时间：2024年1月1日</span>
        </div>
        {/* 新闻内容 */}
      </article>
    </div>
  );
}

// 示例 3: 教程文章
export function TutorialArticle() {
  return (
    <div>
      <ArticleSchema
        title="TypeScript 高级类型系统完全指南"
        description="从基础到高级，全面掌握 TypeScript 的类型系统，包括泛型、条件类型、映射类型等高级特性。"
        image="https://example.com/images/typescript-advanced-types.jpg"
        url="https://example.com/tutorials/typescript-advanced-types"
        authorName="王五"
        datePublished="2024-01-10T14:00:00Z"
        dateModified="2024-01-25T16:30:00Z"
        wordCount={5200}
        readingTime={26}
        tags={["TypeScript", "类型系统", "编程教程", "高级特性", "泛型"]}
        ratingValue={4.9}
        ratingCount={324}
        breadcrumb={[
          { name: "教程", url: "/tutorials" },
          { name: "编程语言", url: "/tutorials/programming" },
          { name: "TypeScript", url: "/tutorials/programming/typescript" }
        ]}
      />
      
      <article>
        <h1>TypeScript 高级类型系统完全指南</h1>
        <div className="tutorial-meta">
          <span>讲师：王五</span>
          <span>难度：高级</span>
          <span>预计学习时间：26分钟</span>
          <div className="rating">
            <span>评分：4.9/5</span>
            <span>（324人评价）</span>
          </div>
        </div>
        {/* 教程内容 */}
      </article>
    </div>
  );
}

// 示例 4: 产品评测文章
export function ProductReview() {
  return (
    <div>
      <ArticleSchema
        title="2024年最佳前端开发工具对比评测"
        description="深度评测当前最流行的前端开发工具，包括 VS Code、WebStorm、Sublime Text 等，帮助开发者选择最适合的工具。"
        image="https://example.com/images/frontend-tools-review.jpg"
        url="https://example.com/reviews/frontend-development-tools-2024"
        authorName="赵六"
        datePublished="2024-01-20T11:00:00Z"
        wordCount={4100}
        readingTime={20}
        tags={["开发工具", "IDE", "编辑器", "前端开发", "产品评测"]}
        ratingValue={4.6}
        ratingCount={89}
        breadcrumb={[
          { name: "评测", url: "/reviews" },
          { name: "开发工具", url: "/reviews/dev-tools" }
        ]}
      />
      
      <article>
        <h1>2024年最佳前端开发工具对比评测</h1>
        <div className="review-meta">
          <span>评测者：赵六</span>
          <span>评测时间：2024年1月20日</span>
          <span>综合评分：4.6/5</span>
        </div>
        {/* 评测内容 */}
      </article>
    </div>
  );
}

// 示例 5: 动态生成 Article Schema
export function DynamicArticlePage({ articleId }: { articleId: string }) {
  const [articleData, setArticleData] = React.useState<SchemaProps | null>(null);

  React.useEffect(() => {
    async function loadArticle() {
      // 模拟从 API 获取文章数据
      const data = await fetchArticleData(articleId);
      
      setArticleData({
        title: data.title,
        description: data.excerpt,
        image: data.featuredImage,
        url: `${window.location.origin}/articles/${articleId}`,
        schemaType: 'Article',
        authorName: data.author.name,
        datePublished: data.publishedAt,
        dateModified: data.updatedAt,
        wordCount: data.wordCount,
        readingTime: Math.ceil(data.wordCount / 200), // 假设每分钟200字
        tags: data.tags,
        ratingValue: data.averageRating,
        ratingCount: data.ratingCount,
        breadcrumb: [
          { name: "文章", url: "/articles" },
          { name: data.category.name, url: `/articles/category/${data.category.slug}` }
        ]
      });
    }

    loadArticle();
  }, [articleId]);

  if (!articleData) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <ArticleSchema {...articleData} />
      <article>
        <h1>{articleData.title}</h1>
        <div className="article-meta">
          <span>作者：{articleData.authorName}</span>
          <span>字数：{articleData.wordCount}</span>
          <span>阅读时间：{articleData.readingTime}分钟</span>
        </div>
        {/* 动态文章内容 */}
      </article>
    </div>
  );
}

// 示例 6: 手动生成 Article Schema 数据
export async function generateArticleSchemaForAPI(articleId: string) {
  const article = await fetchArticleData(articleId);
  
  const schemaData: SchemaProps = {
    title: article.title,
    description: article.excerpt,
    image: article.featuredImage,
    url: `${process.env.NEXT_PUBLIC_WEB_URL}/articles/${articleId}`,
    schemaType: 'Article',
    authorName: article.author.name,
    datePublished: article.publishedAt,
    dateModified: article.updatedAt,
    wordCount: article.wordCount,
    readingTime: Math.ceil(article.wordCount / 200),
    tags: article.tags,
    breadcrumb: [
      { name: "文章", url: "/articles" },
      { name: article.category.name, url: `/articles/category/${article.category.slug}` }
    ]
  };

  // 生成 Article schema
  const articleSchema = await createArticleSchema(schemaData);
  
  return {
    schema: articleSchema,
    metadata: {
      title: article.title,
      description: article.excerpt,
      author: article.author.name,
      publishedAt: article.publishedAt,
      tags: article.tags
    }
  };
}

// 模拟数据获取函数
async function fetchArticleData(articleId: string) {
  // 这里应该是实际的数据库查询或 API 调用
  return {
    title: `文章 ${articleId}`,
    excerpt: `这是文章 ${articleId} 的摘要`,
    featuredImage: `https://example.com/images/article-${articleId}.jpg`,
    publishedAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    wordCount: 2500,
    tags: ['技术', '编程', 'Web开发'],
    averageRating: 4.5,
    ratingCount: 100,
    author: {
      name: '作者姓名'
    },
    category: {
      name: '技术文章',
      slug: 'tech'
    }
  };
}
