/**
 * SEO Schema 组件 - 生成结构化数据用于搜索引擎优化
 *
 * 该组件用于生成符合 schema.org 标准的 JSON-LD 结构化数据，
 * 主要用于游戏应用的 SEO 优化，包括面包屑导航和应用信息。
 *
 * @example
 * ```tsx
 * // 基本使用
 * <Schema
 *   title="我的游戏"
 *   description="这是一个很棒的游戏"
 *   image="https://example.com/game.jpg"
 *   url="https://example.com/game"
 *   breadcrumb={[
 *     { name: "游戏", url: "/games" },
 *     { name: "动作游戏", url: "/games/action" }
 *   ]}
 * />
 *
 * // 包含发布日期
 * <Schema
 *   title="我的游戏"
 *   description="这是一个很棒的游戏"
 *   image="https://example.com/game.jpg"
 *   url="https://example.com/game"
 *   datePublished="2024-01-01"
 *   dateModified="2024-01-15"
 *   breadcrumb={[{ name: "游戏", url: "/games" }]}
 * />
 * ```
 */

import React from 'react';
import type {
  SchemaProps,
  BreadcrumbItem,
  JsonLdSchema,
  BreadcrumbListSchema
} from '@/types/schema';

/**
 * 创建面包屑列表的结构化数据
 * @param url - 当前页面URL
 * @param breadcrumb - 面包屑数组
 * @returns 面包屑列表的 JSON-LD 数据
 */
const createBreadcrumbList = (url: string, breadcrumb: BreadcrumbItem[]): BreadcrumbListSchema => {
  const siteUrl = process.env.NEXT_PUBLIC_WEB_URL;

  return {
    "@type": "BreadcrumbList",
    "@id": `${url}#breadcrumb`,
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "首页",
        "item": siteUrl || ""
      },
      ...breadcrumb.map((item, index) => ({
        '@type': 'ListItem' as const,
        position: index + 2,
        name: item.name,
        item: `${siteUrl}${item.url}`
      }))
    ]
  };
};

/**
 * 创建通用的结构化数据
 * @param props - Schema 属性
 * @returns Promise<JsonLdSchema> 结构化数据
 */
export const createSchema = async (props: SchemaProps): Promise<JsonLdSchema> => {
  const {
    title,
    description,
    image,
    url,
    schemaType = 'SoftwareApplication',
    datePublished,
    dateModified,
    applicationCategory = "GameApplication",
    operatingSystem,
    ratingValue,
    ratingCount,
    authorName,
    wordCount,
    readingTime,
    tags
  } = props;

  // 获取环境变量
  const siteUrl = process.env.NEXT_PUBLIC_WEB_URL;
  const siteName = process.env.NEXT_PUBLIC_PROJECT_NAME;

  // 验证必需的环境变量
  if (!siteUrl || !siteName) {
    console.warn('Schema: Missing required environment variables NEXT_PUBLIC_WEB_URL or NEXT_PUBLIC_PROJECT_NAME');
  }

  // 基础 schema 结构
  const baseSchema = {
    '@context': 'https://schema.org' as const,
    '@id': url,
    description,
    image: {
      '@type': 'ImageObject' as const,
      url: image,
      contentUrl: image
    },
    url: url,
    author: {
      '@type': authorName ? 'Person' as const : 'Organization' as const,
      name: authorName || siteName || 'QQ AI',
      ...(authorName ? {} : {
        url: siteUrl || '',
        logo: {
          '@type': 'ImageObject' as const,
          url: `${siteUrl || ''}/logo.png`
        }
      })
    },
    publisher: {
      '@type': 'Organization' as const,
      name: siteName || 'QQ AI',
      url: siteUrl || '',
      logo: {
        '@type': 'ImageObject' as const,
        url: `${siteUrl || ''}/logo.png`
      }
    },
    mainEntityOfPage: url,
    ...(datePublished && { datePublished }),
    ...(dateModified && { dateModified }),
    ...(ratingValue && ratingCount && {
      aggregateRating: {
        '@type': 'AggregateRating' as const,
        ratingValue,
        ratingCount
      }
    })
  };

  // 根据 schema 类型创建特定的 schema
  switch (schemaType) {
    case 'SoftwareApplication':
      return {
        ...baseSchema,
        '@type': 'SoftwareApplication' as const,
        name: title,
        applicationCategory,
        ...(operatingSystem && { operatingSystem })
      };

    case 'Article':
      return {
        ...baseSchema,
        '@type': 'Article' as const,
        headline: title,
        ...(wordCount && { wordCount }),
        ...(readingTime && { timeRequired: `PT${readingTime}M` }),
        ...(tags && tags.length > 0 && { keywords: tags.join(', ') })
      };

    case 'WebPage':
      return {
        ...baseSchema,
        '@type': 'WebPage' as const,
        name: title
      };

    default:
      throw new Error(`Unsupported schema type: ${schemaType}`);
  }
};

/**
 * 创建游戏应用的结构化数据 (向后兼容)
 * @param props - Schema 属性
 * @returns Promise<JsonLdSchema> 游戏应用的 JSON-LD 数据
 * @deprecated 请使用 createSchema 替代
 */
export const createGameSchema = async (props: SchemaProps): Promise<JsonLdSchema> => {
  return createSchema({ ...props, schemaType: 'SoftwareApplication' });
};

/**
 * 创建文章的结构化数据
 * @param props - Schema 属性
 * @returns Promise<JsonLdSchema> 文章的 JSON-LD 数据
 */
export const createArticleSchema = async (props: SchemaProps): Promise<JsonLdSchema> => {
  return createSchema({ ...props, schemaType: 'Article' });
};

/**
 * 创建网页的结构化数据
 * @param props - Schema 属性
 * @returns Promise<JsonLdSchema> 网页的 JSON-LD 数据
 */
export const createWebPageSchema = async (props: SchemaProps): Promise<JsonLdSchema> => {
  return createSchema({ ...props, schemaType: 'WebPage' });
};


/**
 * 创建面包屑列表的结构化数据（单独导出供其他组件使用）
 * @param url - 当前页面URL
 * @param breadcrumb - 面包屑数组
 * @returns 面包屑列表的 JSON-LD 数据
 */
export const createBreadcrumbSchema = (url: string, breadcrumb: BreadcrumbItem[]): BreadcrumbListSchema => {
  return createBreadcrumbList(url, breadcrumb);
};

/**
 * Schema 组件 - 渲染 JSON-LD 结构化数据
 *
 * 这是主要的 Schema 组件，用于在页面中插入结构化数据。
 * 支持多种类型的 Schema：SoftwareApplication、Article、WebPage。
 *
 * @param props - Schema 组件属性
 * @returns JSX.Element 包含 JSON-LD 数据的 script 标签
 */
export default async function Schema(props: SchemaProps): Promise<JSX.Element> {
  try {
    // 生成主要的 schema
    const mainSchema = await createSchema(props);

    // 生成面包屑 schema
    const breadcrumbSchema = createBreadcrumbList(props.url, props.breadcrumb);

    // 合并两个 schema 到一个数组中
    const combinedSchema = [mainSchema, breadcrumbSchema];

    return (
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(combinedSchema, null, 0)
        }}
      />
    );
  } catch (error) {
    console.error('Schema generation error:', error);
    // 在生产环境中返回空的 script 标签，避免页面崩溃
    return <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: '{}' }} />;
  }
}

/**
 * 简化版 Schema 组件 - 用于基本页面
 *
 * @example
 * ```tsx
 * <SimpleSchema
 *   title="页面标题"
 *   description="页面描述"
 *   image="/images/page.jpg"
 *   url="/current-page"
 * />
 * ```
 */
export function SimpleSchema({
  title,
  description,
  image,
  url,
  datePublished,
  dateModified,
  schemaType = 'WebPage'
}: Omit<SchemaProps, 'breadcrumb'> & { breadcrumb?: BreadcrumbItem[] }) {
  const defaultBreadcrumb: BreadcrumbItem[] = [];

  return (
    <Schema
      title={title}
      description={description}
      image={image}
      url={url}
      schemaType={schemaType}
      datePublished={datePublished}
      dateModified={dateModified}
      breadcrumb={defaultBreadcrumb}
    />
  );
}

/**
 * 文章 Schema 组件 - 专门用于博客文章和新闻
 *
 * @example
 * ```tsx
 * <ArticleSchema
 *   title="文章标题"
 *   description="文章摘要"
 *   image="/images/article.jpg"
 *   url="/articles/my-article"
 *   authorName="作者姓名"
 *   datePublished="2024-01-01T10:00:00Z"
 *   wordCount={1500}
 *   readingTime={8}
 *   tags={["技术", "编程", "React"]}
 *   breadcrumb={[
 *     { name: "博客", url: "/blog" },
 *     { name: "技术", url: "/blog/tech" }
 *   ]}
 * />
 * ```
 */
export function ArticleSchema(props: SchemaProps) {
  return <Schema {...props} schemaType="Article" />;
}

/**
 * 游戏应用 Schema 组件 - 专门用于游戏和应用
 *
 * @example
 * ```tsx
 * <GameSchema
 *   title="超级马里奥兄弟"
 *   description="经典的平台跳跃游戏"
 *   image="/images/mario.jpg"
 *   url="/games/mario"
 *   applicationCategory="GameApplication"
 *   operatingSystem="Nintendo Switch, PC"
 *   ratingValue={4.8}
 *   ratingCount={1500}
 *   breadcrumb={[
 *     { name: "游戏", url: "/games" },
 *     { name: "平台游戏", url: "/games/platform" }
 *   ]}
 * />
 * ```
 */
export function GameSchema(props: SchemaProps) {
  return <Schema {...props} schemaType="SoftwareApplication" />;
}

/**
 * 网页 Schema 组件 - 用于一般网页
 *
 * @example
 * ```tsx
 * <WebPageSchema
 *   title="关于我们"
 *   description="了解我们的团队和使命"
 *   image="/images/about.jpg"
 *   url="/about"
 *   breadcrumb={[]}
 * />
 * ```
 */
export function WebPageSchema(props: SchemaProps) {
  return <Schema {...props} schemaType="WebPage" />;
}
