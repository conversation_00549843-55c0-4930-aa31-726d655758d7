
import Script from 'next/script'


export default function ClarityScript() {
    const isProduction = process.env.NODE_ENV === 'production';
    const CLARITY_ID = process.env.NEXT_PUBLIC_CLARITY_ID;
    if (!isProduction || !CLARITY_ID) {
      return null;
    }
  
    return (


      //pzifbndtl7

<Script id="microsoft-clarity" strategy="afterInteractive">
                    {`
    (function(c, l, a, r, i, t, y) {
      c[a] = c[a] || function() {
        (c[a].q = c[a].q || []).push(arguments)
      };
      t = l.createElement(r);
      t.async = 1;
      t.src = "https://www.clarity.ms/tag/" + i;
      y = l.getElementsByTagName(r)[0];
      y.parentNode.insertBefore(t, y);
    })(window, document, "clarity", "script", "${CLARITY_ID}");
  `}
                </Script> );
}
  
