import { Button } from "@/components/ui/button";
import { Button as ButtonType } from "@/types/blocks/base";
import Icon from "@/components/icon";
import { Link } from "@/i18n/routing";

export default function Toolbar({ items }: { items?: ButtonType[] }) {
  return (
    <div className="flex space-x-4 mb-8">
      {items?.map((item, idx) => {
        if (item.url) {
          return (
            <Link key={idx} href={item.url as any}>
              <Button
                variant={item.variant}
                size="sm"
                className={item.className}
              >
                {item.icon && <Icon name={item.icon} className="w-4 h-4 mr-1" />}
                {item.title}
              </Button>
            </Link>
          );
        } else {
          return (
            <Button
              key={idx}
              variant={item.variant}
              size="sm"
              className={item.className}
            >
              {item.icon && <Icon name={item.icon} className="w-4 h-4 mr-1" />}
              {item.title}
            </Button>
          );
        }
      })}
    </div>
  );
}
