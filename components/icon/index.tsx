"use client";

import { memo } from "react";
// 从我们新的中央图标库导入 ICONS 对象和 IconName 类型
import { ICONS, IconName } from "./icons";

// 定义组件的 props 接口
interface IconProps {
  // 使用我们的 IconName 类型，当您在代码中写 <Icon name="..." /> 时，
  // 编辑器会自动提示所有可用的图标名称！
  name: IconName | string;
  className?: string;
  onClick?: () => void;
}

const Icon: React.FC<IconProps> = ({ name, className, onClick, ...props }) => {
  // 直接在 ICONS 映射表中查找图标
  // 不再需要任何前缀匹配或复杂的逻辑
  const IconComponent = ICONS[name as IconName];

  // 如果在我们的库中找不到图标，给出有用的开发提示并静默失败
  if (!IconComponent) {
    if (process.env.NODE_ENV === "development") {
      console.warn(
        `[Icon Component] Icon "${name}" was not found. 
        Please make sure it's imported and exported in 'src/components/icons.ts'.`
      );
    }
    return null;
  }

  // 渲染找到的图标组件，并传递所有 props
  return (
    <IconComponent
      className={className}
      onClick={onClick}
      style={{ cursor: onClick ? "pointer" : "default" }}
      {...props}
    />
  );
};

// 使用 React.memo 进行性能优化，防止不必要的重渲染
export default memo(Icon);