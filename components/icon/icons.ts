// src/components/icons.ts

// 1. 从各自正确的库中按需导入您需要的图标
// ======================================================

// 从 'react-icons/ri' (Remix Icons) 导入
import {
  RiChatSmile3Line,
  RiImageLine,
  RiVideoLine,
  RiMicLine,
  RiMoneyDollarCircleLine,
  RiArrowRightUpLine,
  RiFlashlightFill,
  RiEyeLine,
  RiCpuLine,
  RiUserSmileLine,
  RiFlashlightLine,
  RiStarLine,
  RiPaletteLine,
  RiRocketLine,
  RiVoiceprintLine,
  RiExchangeLine,
  RiTwitterXFill,
  RiGithubFill,
  RiDiscordFill,
  RiMailLine,
} from "react-icons/ri";

// 从 'react-icons/fa' (Font Awesome) 导入
import { FaRegHeart } from "react-icons/fa";

// 从 'react-icons/go' (Github Octicons) 导入
import { GoThumbsup, GoArrowUpRight } from "react-icons/go";

// 2. 将所有图标组件聚合到一个导出的对象中
// KEY 就是您在数据中使用的图标名称，VALUE 就是导入的组件本身
// ======================================================
export const ICONS = {
  // Remix Icons
  RiChatSmile3Line,
  RiImageLine,
  RiVideoLine,
  RiMicLine,
  RiMoneyDollarCircleLine,
  RiArrowRightUpLine,
  RiFlashlightFill,
  RiEyeLine,
  RiCpuLine,
  RiUserSmileLine,
  RiFlashlightLine,
  RiStarLine,
  RiPaletteLine,
  RiRocketLine,
  RiVoiceprintLine,
  RiExchangeLine,
  RiTwitterXFill,
  RiGithubFill,
  RiDiscordFill,
  RiMailLine,

  // Font Awesome Icons
  FaRegHeart,

  // Github Octicons
  GoThumbsup,
  GoArrowUpRight,

  // 未来任何新的图标，都在上面 import，然后在这里添加一行即可！
};

// 3. (强烈推荐) 创建一个类型，以获得代码提示和类型安全
// ======================================================
export type IconName = keyof typeof ICONS;