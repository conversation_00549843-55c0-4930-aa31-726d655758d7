"use client";

import { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Brain,
  Image,
  Video,
  AlertCircle,
  CheckCircle,
  Maximize,
  Minimize,
  Loader2,
  Eye,
  Download,
  MessageSquare,
  Coins
} from "lucide-react";
import { GeneratorMain } from "@/components/ai-dashboard/genrator-main";
// import { AIFullMain } from "@/components/ai-dashboard/ai-full-main";
import { useFullscreen } from "@/hooks/use-fullscreen";
import { useScreenSize } from "@/hooks/use-media-query";
import { useAppContext } from "@/contexts/app";
import { toast } from "sonner";

interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export function WorkspaceLayout() {
  // 从环境变量获取默认模型类型，如果没有设置则默认为image
  const defaultModelType = process.env.NEXT_PUBLIC_DEFAULT_MODEL_TYPE || "image";
  const [activeTab, setActiveTab] = useState(defaultModelType);
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // 当切换tab时清空结果
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setGenerationResult(null);
    setIsGenerating(false);
  };
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const screenSize = useScreenSize();
  const moduleRef = useRef<HTMLDivElement>(null);

  // 全屏时的滚动控制
  useEffect(() => {
    if (isFullscreen) {
      // 禁用 body 滚动
      document.body.classList.add('overflow-hidden');
      document.documentElement.classList.add('overflow-hidden');
    } else {
      // 恢复 body 滚动
      document.body.classList.remove('overflow-hidden');
      document.documentElement.classList.remove('overflow-hidden');
    }

    // 组件卸载时清理
    return () => {
      document.body.classList.remove('overflow-hidden');
      document.documentElement.classList.remove('overflow-hidden');
    };
  }, [isFullscreen]);






  // 权限控制：只有登录用户才能看到此组件
  // if (!user) {
  //   return null;
  // }

  const handleFullscreenToggle = () => {
    // 使用CSS全屏，让AIModelModule组件占满浏览器窗口
    console.log('全屏按钮被点击', moduleRef.current);
    if (moduleRef.current) {
      console.log('元素存在，调用toggleFullscreen');
      toggleFullscreen(moduleRef.current);
    } else {
      console.log('元素不存在');
    }
  };

  // 获取布局类名
  const getLayoutClass = () => {
    if (isFullscreen && screenSize === 'desktop') {
      return 'grid grid-cols-10 gap-6 flex-1 min-h-0'; // 1:3:6 = 1fr:3fr:6fr
    } else if (screenSize === 'desktop' || screenSize === 'tablet') {
      return 'grid grid-cols-5 gap-6'; // 2:3 = 2fr:3fr
    } else {
      return 'flex flex-col gap-4'; // 移动端垂直布局
    }
  };

  // 渲染模型类型选择器（垂直标签页）
  const renderModelTypeSelector = () => {
    const modelTypes = [
      { value: 'text', label: '文本生成', icon: MessageSquare, color: 'from-blue-500 to-purple-500' },
      { value: 'image', label: '图片生成', icon: Image, color: 'from-green-500 to-teal-500' },
      { value: 'video', label: '视频生成', icon: Video, color: 'from-orange-500 to-red-500' }
    ];

    return (
      <div className="space-y-3">
        {modelTypes.map(({ value, label, icon: Icon, color }) => (
          <Button
            key={value}
            variant={activeTab === value ? "default" : "ghost"}
            className={`w-full justify-start gap-3 transition-all duration-200 ${
              isFullscreen && screenSize === 'desktop'
                ? 'flex-col h-20 text-xs p-3'
                : 'flex-row h-12 px-4'
            } ${
              activeTab === value
                ? `bg-gradient-to-r ${color} text-white shadow-lg`
                : 'hover:bg-accent/50'
            }`}
            onClick={() => handleTabChange(value)}
          >
            <div className={`p-2 rounded-lg ${activeTab === value ? 'bg-white/20' : 'bg-accent/20'}`}>
              <Icon className="w-4 h-4" />
            </div>
            {(screenSize !== 'desktop' || !isFullscreen) && <span className="font-medium">{label}</span>}
            {isFullscreen && screenSize === 'desktop' && (
              <span className="text-xs font-medium text-center leading-tight">{label.replace('生成', '')}</span>
            )}
          </Button>
        ))}
      </div>
    );
  };

  // 渲染结果区域
  const renderResult = () => {
    if (!generationResult) {
      return (
        <div className="text-center py-6">
          <div className="p-6 rounded-2xl bg-gradient-to-br from-muted/30 to-muted/10 border border-border/30 max-w-md mx-auto">
            <div className="p-4 rounded-xl bg-gradient-to-r from-primary to-accent mb-4 w-fit mx-auto">
              <Brain className="w-8 h-8 text-primary-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">准备开始创作</h3>
            <p className="text-muted-foreground text-sm">
              选择AI模型并输入提示词，开始您的创意之旅
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* 生成状态 */}
        <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30">
          <div className={`p-2 rounded-lg ${
            generationResult.status === 'success' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
            generationResult.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-rose-500' :
            'bg-gradient-to-r from-blue-500 to-purple-500'
          }`}>
            {generationResult.status === 'success' && <CheckCircle className="w-5 h-5 text-white" />}
            {generationResult.status === 'failed' && <AlertCircle className="w-5 h-5 text-white" />}
            {(generationResult.status === 'pending' || generationResult.status === 'running') && <Loader2 className="w-5 h-5 animate-spin text-white" />}
          </div>
          <div>
            <span className="font-semibold text-foreground">
              {generationResult.status === 'success' && '生成完成'}
              {generationResult.status === 'failed' && '生成失败'}
              {(generationResult.status === 'pending' || generationResult.status === 'running') && '生成中...'}
            </span>
            {generationResult.progress !== undefined && (
              <div className="text-sm text-muted-foreground">进度: {generationResult.progress}%</div>
            )}
          </div>
        </div>

        {/* 结果内容 */}
        {generationResult.status === 'success' && generationResult.result && (
          <div className="space-y-4">
            {/* 文本结果 */}
            {generationResult.result.text && (
              <div className="p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30 backdrop-blur-sm">
                <pre className="whitespace-pre-wrap text-sm leading-relaxed text-foreground">{generationResult.result.text}</pre>
              </div>
            )}

            {/* 图像结果 */}
            {generationResult.result.images && (
              <div className="grid grid-cols-1 gap-4">
                {generationResult.result.images.map((image, index) => (
                  <div key={index} className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
                    <img
                      src={image.url}
                      alt={`Generated image ${index + 1}`}
                      className="w-full rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200"
                    />
                    <div className="flex gap-3">
                      <Button size="sm" variant="outline" asChild className="flex-1">
                        <a href={image.url} target="_blank" rel="noopener noreferrer">
                          <Eye className="w-4 h-4 mr-2" />
                          查看原图
                        </a>
                      </Button>
                      <Button size="sm" variant="default" asChild className="flex-1">
                        <a href={image.url} download>
                          <Download className="w-4 h-4 mr-2" />
                          下载图片
                        </a>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 视频结果 */}
            {generationResult.result.video && (
              <div className="space-y-2">
                <video
                  src={generationResult.result.video.url}
                  controls
                  className="w-full rounded-lg shadow-md"
                />
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <a href={generationResult.result.video.url} target="_blank" rel="noopener noreferrer">
                      <Eye className="w-4 h-4 mr-1" />
                      查看
                    </a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href={generationResult.result.video.url} download>
                      <Download className="w-4 h-4 mr-1" />
                      下载
                    </a>
                  </Button>
                </div>
              </div>
            )}

            {/* 使用统计 */}
            {generationResult.usage && (
              <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
                <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500">
                  <Coins className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium text-foreground">
                  本次生成消耗 <span className="font-bold text-yellow-600">{generationResult.usage.credits_consumed}</span> 积分
                </span>
              </div>
            )}
          </div>
        )}

        {/* 错误信息 */}
        {generationResult.status === 'failed' && generationResult.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              生成失败: {generationResult.error.detail}
            </AlertDescription>
          </Alert>
        )}

        {/* 进度显示 */}
        {(generationResult.status === 'pending' || generationResult.status === 'running') && (
          <div className="text-center py-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              正在生成中，请稍候...
              {generationResult.progress !== undefined && ` (${generationResult.progress}%)`}
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      ref={moduleRef}
      className={`
        ${isFullscreen
          ? "fixed inset-0 flex flex-col"
          : "container mx-auto max-w-6xl"
        }
        transition-all duration-300 ease-in-out
      `}
      style={isFullscreen ? {
        zIndex: 40,
        background: 'var(--background)'
      } : undefined}
    >
      {/* 全屏模式下的顶部工具栏 */}
      {isFullscreen && (
        <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-md border-b border-border/50 px-6 py-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-2 rounded-xl bg-gradient-to-r from-primary to-accent">
                <Brain className="w-6 h-6 text-primary-foreground" />
              </div>
              <h1 className="text-xl font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                AI模型工作台 - 全屏模式
              </h1>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleFullscreenToggle}
              className="flex items-center gap-2"
            >
              <Minimize className="w-4 h-4" />
              退出全屏
            </Button>
          </div>
        </div>
      )}

      {/* 常规模式下的头部区域 */}
      {!isFullscreen && (
        <div className="px-6 py-12 bg-gradient-to-br from-background/50 via-background to-accent/5 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="p-3 rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg">
                <Brain className="w-8 h-8 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  AI模型工作台
                </h1>
                <p className="text-muted-foreground mt-1">强大的AI生成工具，支持文本、图像和视频创作</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="lg"
              onClick={handleFullscreenToggle}
              className="flex items-center gap-2"
            >
              <Maximize className="w-5 h-5" />
              全屏模式
            </Button>
          </div>
        </div>
      )}

      {/* 主要内容区域 - 响应式布局 */}
      <div className={isFullscreen ? "px-6 pb-6 pt-4 flex-1 min-h-0" : "px-6 pb-12"}>
        <div className={getLayoutClass()}>
          {/* 左栏：模型类型选择器 */}
          {(isFullscreen && screenSize === 'desktop') && (
            <div className="col-span-1 space-y-4 p-4 bg-gradient-to-b from-muted/30 to-muted/10 rounded-2xl border border-border/30">
              <h3 className="text-sm font-semibold text-foreground mb-4 flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent"></div>
                模型类型
              </h3>
              {renderModelTypeSelector()}
            </div>
          )}

          {/* 中栏：模型选择和参数设置 */}
          <div className={`${isFullscreen && screenSize === 'desktop'
            ? 'col-span-3'
            : screenSize === 'desktop' || screenSize === 'tablet'
              ? 'col-span-2'
              : 'w-full'
            } space-y-4`}>
            {/* 移动端和非全屏桌面端显示水平标签页 */}
            {!(isFullscreen && screenSize === 'desktop') && (
              <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
                <TabsList className="grid w-full grid-cols-3 mb-6 bg-gradient-to-r from-muted/50 to-muted/30">
                  <TabsTrigger value="text" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white">
                    <MessageSquare className="w-4 h-4" />
                    文本
                  </TabsTrigger>
                  <TabsTrigger value="image" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-teal-500 data-[state=active]:text-white">
                    <Image className="w-4 h-4" />
                    图片
                  </TabsTrigger>
                  <TabsTrigger value="video" className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white">
                    <Video className="w-4 h-4" />
                    视频
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            )}

            {/* AI生成器组件 */}
            <GeneratorMain
              modelType={activeTab}
              onResultChange={setGenerationResult}
              onGeneratingChange={setIsGenerating}
            />
          </div>

          {/* 右栏：结果展示 */}
          <div className={`${isFullscreen && screenSize === 'desktop'
            ? 'col-span-6'
            : screenSize === 'desktop' || screenSize === 'tablet'
              ? 'col-span-3'
              : 'w-full'
            }`}>
            <Card className="h-full bg-gradient-to-br from-card via-card to-accent/5">
              <CardHeader className="border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10">
                <CardTitle className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    activeTab === 'text' ? 'bg-gradient-to-r from-blue-500 to-purple-500' :
                    activeTab === 'image' ? 'bg-gradient-to-r from-green-500 to-teal-500' :
                    'bg-gradient-to-r from-orange-500 to-red-500'
                  }`}>
                    {activeTab === 'text' && <MessageSquare className="w-5 h-5 text-white" />}
                    {activeTab === 'image' && <Image className="w-5 h-5 text-white" />}
                    {activeTab === 'video' && <Video className="w-5 h-5 text-white" />}
                  </div>
                  <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                    生成结果
                  </span>
                </CardTitle>
                <CardDescription className="text-muted-foreground/80">
                  {activeTab === 'text' && '文本生成的结果将在这里显示，支持复制和导出'}
                  {activeTab === 'image' && '图像生成的结果将在这里显示，支持预览和下载'}
                  {activeTab === 'video' && '视频生成的结果将在这里显示，支持播放和下载'}
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isFullscreen ? 'overflow-y-auto' : ''} p-6`} style={isFullscreen ? { maxHeight: 'calc(100vh - 220px)' } : {}}>
                {renderResult()}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

    </div>
  );
}
