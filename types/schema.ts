/**
 * Schema 相关类型定义
 * 用于 SEO 结构化数据的类型安全
 */

/**
 * 面包屑导航项接口
 */
export interface BreadcrumbItem {
  /** 面包屑项目名称 */
  name: string;
  /** 面包屑项目URL路径 */
  url: string;
}

/**
 * 游戏应用 Schema 属性接口
 * @deprecated 请使用 SchemaProps 替代
 */
export interface GameSchemaProps {
  name: string;
  title: string;
  description: string;
  image?: string;
  url?: string;
  ratingValue?: string;
  ratingCount?: string;
  sameAs?: string[];
  author?: string;
  applicationCategory?: string;
  offers?: string;
  operatingSystem?: string;
}

/**
 * Schema 类型枚举
 */
export type SchemaType = 'SoftwareApplication' | 'Article' | 'WebPage';

/**
 * 新的 Schema 组件属性接口
 * 用于替代 GameSchemaProps，提供更好的类型安全和文档
 */
export interface SchemaProps {
  /** 页面/应用标题 */
  title: string;
  /** 页面/应用描述 */
  description: string;
  /** 主要图片URL */
  image: string;
  /** 页面完整URL */
  url: string;
  /** Schema 类型，默认为 "SoftwareApplication" */
  schemaType?: SchemaType;
  /** 发布日期 (ISO 8601 格式，如: 2024-01-01 或 2024-01-01T10:00:00Z) */
  datePublished?: string;
  /** 最后修改日期 (ISO 8601 格式) */
  dateModified?: string;
  /** 面包屑导航数组 */
  breadcrumb: BreadcrumbItem[];
  /** 应用类别，默认为 "GameApplication" (仅用于 SoftwareApplication) */
  applicationCategory?: string;
  /** 操作系统要求 (仅用于 SoftwareApplication) */
  operatingSystem?: string;
  /** 评分值 (1-5) */
  ratingValue?: number;
  /** 评分数量 */
  ratingCount?: number;
  /** 作者姓名 (用于 Article) */
  authorName?: string;
  /** 文章正文字数 (用于 Article) */
  wordCount?: number;
  /** 阅读时间（分钟）(用于 Article) */
  readingTime?: number;
  /** 文章标签 (用于 Article) */
  tags?: string[];
}

/**
 * 基础 JSON-LD Schema 数据结构接口
 */
export interface BaseJsonLdSchema {
  '@context': string;
  '@type': string;
  '@id'?: string;
  name?: string;
  headline?: string;
  description: string;
  image: {
    '@type': string;
    url: string;
    contentUrl: string;
  };
  url: string;
  author: {
    '@type': string;
    name: string;
    url?: string;
    logo?: {
      '@type': string;
      url: string;
    };
  };
  publisher: {
    '@type': string;
    name: string;
    url: string;
    logo: {
      '@type': string;
      url: string;
    };
  };
  mainEntityOfPage: string;
  datePublished?: string;
  dateModified?: string;
  aggregateRating?: {
    '@type': string;
    ratingValue: number;
    ratingCount: number;
  };
}

/**
 * SoftwareApplication Schema 接口
 */
export interface SoftwareApplicationSchema extends BaseJsonLdSchema {
  '@type': 'SoftwareApplication';
  name: string;
  applicationCategory: string;
  operatingSystem?: string;
}

/**
 * Article Schema 接口
 */
export interface ArticleSchema extends BaseJsonLdSchema {
  '@type': 'Article';
  headline: string;
  wordCount?: number;
  timeRequired?: string;
  keywords?: string;
  articleSection?: string;
}

/**
 * WebPage Schema 接口
 */
export interface WebPageSchema extends BaseJsonLdSchema {
  '@type': 'WebPage';
  name: string;
}

/**
 * 联合类型：所有支持的 Schema 类型
 */
export type JsonLdSchema = SoftwareApplicationSchema | ArticleSchema | WebPageSchema;

/**
 * 面包屑列表数据结构接口
 */
export interface BreadcrumbListSchema {
  '@type': string;
  '@id': string;
  itemListElement: Array<{
    '@type': string;
    position: number;
    name: string;
    item: string;
  }>;
}