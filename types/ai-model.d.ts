export interface AIModel {
  id?: number;
  model_id: string;
  model_name: string;
  model_type: 'text' | 'image' | 'video' | 'multimodal';
  provider: 'grsai' | 'openai' | 'anthropic';
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: 'tokens' | 'images' | 'videos';
  is_active: boolean;
  description?: string;
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AIModelUsage {
  id?: number;
  user_uuid: string;
  model_id: string;
  request_id: string;
  input_size?: number;
  output_size?: number;
  credits_consumed: number;
  status: 'pending' | 'success' | 'failed' | 'cancelled';
  error_reason?: 'error' | 'output_moderation' | 'input_moderation';
  error_detail?: string;
  request_params?: any;
  response_data?: any;
  started_at?: string;
  completed_at?: string;
  created_at?: string;
}

export interface ModelUsageStats {
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  total_usage_count: number;
  unique_users: number;
  total_credits_consumed: number;
  avg_credits_per_use: number;
  success_count: number;
  failed_count: number;
  success_rate: number;
}

export interface UserCreditsUsageStats {
  user_uuid: string;
  model_id: string;
  model_name: string;
  model_type: string;
  usage_count: number;
  total_credits_consumed: number;
  avg_credits_per_use: number;
  success_count: number;
  failed_count: number;
  last_used_at: string;
}

// GRSAI API 请求和响应类型
export interface GRSAITextRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

export interface GRSAIImageRequest {
  model: string;
  prompt: string;
  size?: string;
  variants?: number;
  urls?: string[];
  webHook?: string;
  shutProgress?: boolean;
  cdn?: 'global' | 'zh';
}

export interface GRSAIFluxRequest {
  model: string;
  prompt: string;
  urls?: string[];
  seed?: number;
  aspectRatio?: string;
  webHook?: string;
  shutProgress?: boolean;
  cdn?: 'global' | 'zh';
}

export interface GRSAIVideoRequest {
  model: string;
  prompt: string;
  firstFrameUrl?: string;
  webHook?: string;
  shutProgress?: boolean;
  cdn?: 'global' | 'zh';
}

export interface GRSAITextResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message?: {
      role: string;
      content: string;
      refusal?: string;
      annotations?: any[];
    };
    delta?: {
      role?: string;
      content?: string;
      refusal?: string;
      annotations?: any[];
    };
    finish_reason: string;
  }>;
}

export interface GRSAIImageResponse {
  id: string;
  task_id?: string;
  url: string;
  width: number;
  height: number;
  progress: number;
  results?: Array<{
    url: string;
    width: number;
    height: number;
  }>;
  status: 'running' | 'succeeded' | 'failed';
  failure_reason?: 'output_moderation' | 'input_moderation' | 'error';
  error?: string;
}

export interface GRSAIVideoResponse {
  id: string;
  url: string;
  progress: number;
  status: 'running' | 'succeeded' | 'failed';
  failure_reason?: 'output_moderation' | 'input_moderation' | 'error';
  error?: string;
}

export interface GRSAIFluxResponse {
  id: string;
  url: string;
  seed: number;
  progress: number;
  status: 'running' | 'succeeded' | 'failed';
  failure_reason?: 'output_moderation' | 'input_moderation' | 'error';
  error?: string;
}

export interface GRSAIResultResponse {
  code: number;
  data: GRSAIImageResponse | GRSAIVideoResponse | GRSAIFluxResponse;
  msg: string;
}

export interface GRSAIWebhookResponse {
  code: number;
  msg: string;
  data: {
    id: string;
  };
}

// 统一的AI请求接口
export interface AIRequest {
  model: string;
  type: 'text' | 'image' | 'video';
  prompt: string;
  options?: {
    // 文本生成选项
    messages?: Array<{ role: string; content: string; image_url?: string }>;
    stream?: boolean;
    temperature?: number;
    max_tokens?: number;

    // 图像生成选项
    size?: string;
    aspectRatio?: string;
    variants?: number;
    referenceImages?: string[];
    seed?: number;

    // 视频生成选项
    firstFrameUrl?: string;

    // 图片上传选项
    uploadedImages?: string[];

    // 通用选项
    cdn?: 'global' | 'zh';
    webhook?: string;
  };
}

export interface AIResponse {
  id: string;
  type: 'text' | 'image' | 'video';
  status: 'pending' | 'running' | 'success' | 'failed';
  progress?: number;
  result?: {
    // 文本结果
    text?: string;
    
    // 图像结果
    images?: Array<{
      url: string;
      width: number;
      height: number;
    }>;
    
    // 视频结果
    video?: {
      url: string;
      duration?: number;
    };
  };
  error?: {
    reason: 'error' | 'output_moderation' | 'input_moderation';
    detail: string;
  };
  usage?: {
    input_tokens?: number;
    output_tokens?: number;
    total_tokens?: number;
    credits_consumed: number;
  };
}
