import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

/**
 * 从URL获取文件扩展名
 */
function getFileExtensionFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const extension = pathname.split('.').pop();
    return extension || 'bin';
  } catch (e) {
    return 'bin';
  }
}

/**
 * 根据扩展名获取MIME类型
 */
function getMimeTypeFromExtension(extension: string): string {
  const mimeTypes: Record<string, string> = {
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'webp': 'image/webp',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'svg': 'image/svg+xml',
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'webm': 'video/webm',
    'mkv': 'video/x-matroska'
  };
  
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

/**
 * 转存GRSAI文件到我们的R2存储
 */
export async function transferGRSAIFile(
  sourceUrl: string,
  modelType: string = 'image',
  taskId?: string
): Promise<{
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}> {
  try {
    console.log(`[File Transfer] Starting transfer from: ${sourceUrl}`);

    // 使用任务ID作为文件名，如果没有则生成新的UUID
    const fileId = taskId || getUuid();
    const extension = getFileExtensionFromUrl(sourceUrl);
    const contentType = getMimeTypeFromExtension(extension);

    // 构建存储路径，包含bucket名称
    const bucket = process.env.STORAGE_BUCKET || 'shipany-test';
    const key = `ai-generated/${modelType}/${fileId}.${extension}`;

    console.log(`[File Transfer] Target bucket: ${bucket}, key: ${key}, Content-Type: ${contentType}`);

    // 使用存储服务的downloadAndUpload方法
    const storage = newStorage();
    const result = await storage.downloadAndUpload({
      url: sourceUrl,
      key: key,
      bucket: bucket,
      contentType: contentType,
      disposition: 'inline'
    });

    console.log(`[File Transfer] Transfer successful:`, result);

    return {
      success: true,
      url: result.url,
      key: result.key
    };
  } catch (error) {
    console.error(`[File Transfer] Transfer failed:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 批量转存多个文件
 */
export async function transferMultipleFiles(
  urls: string[],
  modelType: string = 'image',
  baseTaskId?: string
): Promise<Array<{
  originalUrl: string;
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}>> {
  const results = [];

  for (let i = 0; i < urls.length; i++) {
    const url = urls[i];
    // 如果有多个文件，在任务ID后面加上索引
    const taskId = baseTaskId ? (urls.length > 1 ? `${baseTaskId}_${i}` : baseTaskId) : undefined;
    const result = await transferGRSAIFile(url, modelType, taskId);
    results.push({
      originalUrl: url,
      ...result
    });
  }

  return results;
}

/**
 * 从GRSAI响应数据中提取所有需要转存的URL
 */
export function extractUrlsFromGRSAIResponse(responseData: any): string[] {
  const urls: string[] = [];
  
  // 主URL
  if (responseData.url) {
    urls.push(responseData.url);
  }
  
  // results数组中的URL
  if (responseData.results && Array.isArray(responseData.results)) {
    responseData.results.forEach((result: any) => {
      if (result.url) {
        urls.push(result.url);
      }
    });
  }
  
  // data.url（某些响应格式）
  if (responseData.data && responseData.data.url) {
    urls.push(responseData.data.url);
  }
  
  return urls;
}

/**
 * 更新响应数据中的URL为我们的存储URL
 */
export function updateResponseDataUrls(
  responseData: any,
  urlMapping: Record<string, string>
): any {
  const updatedData = JSON.parse(JSON.stringify(responseData));
  
  // 更新主URL
  if (updatedData.url && urlMapping[updatedData.url]) {
    updatedData.url = urlMapping[updatedData.url];
  }
  
  // 更新results数组中的URL
  if (updatedData.results && Array.isArray(updatedData.results)) {
    updatedData.results.forEach((result: any) => {
      if (result.url && urlMapping[result.url]) {
        result.url = urlMapping[result.url];
      }
    });
  }
  
  // 更新data.url
  if (updatedData.data && updatedData.data.url && urlMapping[updatedData.data.url]) {
    updatedData.data.url = urlMapping[updatedData.data.url];
  }
  
  return updatedData;
}
