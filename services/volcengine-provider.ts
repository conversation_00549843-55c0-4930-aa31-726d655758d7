import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";

/**
 * 火山引擎语音服务配置
 */
const VOLCENGINE_CONFIG = {
  baseURL: 'https://openspeech.bytedance.com',
  timeout: 60000,
  retryAttempts: 3,
  retryDelay: 1000
};

/**
 * 火山引擎TTS请求接口
 */
export interface VolcengineTTSRequest {
  text: string;
  voice_type?: string;
  encoding?: string;
  rate?: number;
  speed_ratio?: number;
  volume_ratio?: number;
  pitch_ratio?: number;
  emotion?: string;
  language?: string;
}

/**
 * 火山引擎TTS响应接口
 */
export interface VolcengineTTSResponse {
  reqid: string;
  code: number;
  message: string;
  data?: string; // base64编码的音频数据
  addition?: {
    duration: string;
    frontend: string;
  };
}

/**
 * 火山引擎异步TTS请求接口
 */
export interface VolcengineAsyncTTSRequest {
  text: string;
  voice_type?: string;
  format?: string;
  sample_rate?: number;
  volume?: number;
  speed?: number;
  pitch?: number;
  enable_subtitle?: number;
  callback_url?: string;
}

/**
 * 火山引擎异步TTS响应接口
 */
export interface VolcengineAsyncTTSResponse {
  code: number;
  message: string;
  task_id?: string;
  status?: string;
  audio_url?: string;
  subtitle_url?: string;
  duration?: number;
  file_size?: number;
}

/**
 * 火山引擎ASR请求接口
 */
export interface VolcengineASRRequest {
  url: string;
  language?: string;
  use_itn?: boolean;
  use_capitalize?: boolean;
  max_lines?: number;
  callback_url?: string;
}

/**
 * 火山引擎ASR响应接口
 */
export interface VolcengineASRResponse {
  code: number;
  message: string;
  id?: string;
  status?: string;
  result?: {
    text: string;
    utterances: Array<{
      text: string;
      start_time: number;
      end_time: number;
      words?: Array<{
        text: string;
        start_time: number;
        end_time: number;
      }>;
    }>;
  };
}

/**
 * 火山引擎声音复刻请求接口
 */
export interface VolcengineVoiceCloneRequest {
  speaker_id: string;
  audio_bytes: string;
  audio_format: string;
  text?: string;
  language?: number;
  model_type?: number;
}

/**
 * 火山引擎声音复刻响应接口
 */
export interface VolcengineVoiceCloneResponse {
  BaseResp: {
    StatusCode: number;
    StatusMessage: string;
  };
  speaker_id?: string;
  status?: number;
  create_time?: number;
  version?: string;
  demo_audio?: string;
}

/**
 * 火山引擎语音服务提供商
 */
export class VolcengineProvider {
  private apiKey: string;
  private appId: string;
  private baseURL: string;

  constructor(apiKey?: string, appId?: string) {
    this.apiKey = apiKey || process.env.VOLCENGINE_ACCESS_TOKEN || '';
    this.appId = appId || process.env.VOLCENGINE_APP_ID || '';
    this.baseURL = VOLCENGINE_CONFIG.baseURL;
    
    if (!this.apiKey || !this.appId) {
      throw new Error('Volcengine API key and App ID are required');
    }
  }

  /**
   * 短文本语音合成
   */
  async synthesizeText(request: VolcengineTTSRequest): Promise<VolcengineTTSResponse> {
    const reqid = getUuid();

    const payload = {
      app: {
        appid: this.appId,
        token: "access_token", // 根据文档，这里应该是固定字符串
        cluster: "volcano_tts"
      },
      user: {
        uid: "web_user"
      },
      audio: {
        voice_type: request.voice_type || "BV700_streaming",
        encoding: request.encoding || "mp3",
        rate: request.rate || 24000,
        speed_ratio: request.speed_ratio || 1.0,
        volume_ratio: request.volume_ratio || 1.0,
        pitch_ratio: request.pitch_ratio || 1.0,
        emotion: request.emotion,
        language: request.language || "cn"
      },
      request: {
        reqid: reqid,
        text: request.text,
        text_type: "plain",
        operation: "query"
      }
    };

    const response = await this.makeRequest('/api/v1/tts', 'POST', payload);
    return response as VolcengineTTSResponse;
  }

  /**
   * 长文本异步语音合成 - 提交任务
   */
  async submitAsyncTTS(request: VolcengineAsyncTTSRequest): Promise<VolcengineAsyncTTSResponse> {
    const reqid = getUuid();
    
    const payload = {
      appid: this.appId,
      reqid: reqid,
      text: request.text,
      format: request.format || "mp3",
      voice_type: request.voice_type || "BV701_streaming",
      sample_rate: request.sample_rate || 24000,
      volume: request.volume || 1.0,
      speed: request.speed || 1.0,
      pitch: request.pitch || 1.0,
      enable_subtitle: request.enable_subtitle || 0,
      callback_url: request.callback_url
    };

    const response = await this.makeRequest('/api/v1/tts_async/submit', 'POST', payload, {
      'Resource-Id': 'volc.tts_async.default'
    });
    return response as VolcengineAsyncTTSResponse;
  }

  /**
   * 查询异步TTS结果
   */
  async queryAsyncTTS(taskId: string): Promise<VolcengineAsyncTTSResponse> {
    const url = `/api/v1/tts_async/query?appid=${this.appId}&task_id=${taskId}`;
    
    const response = await this.makeRequest(url, 'GET', null, {
      'Resource-Id': 'volc.tts_async.default'
    });
    return response as VolcengineAsyncTTSResponse;
  }

  /**
   * 大模型语音识别 - 提交任务
   */
  async submitASRBigModel(request: VolcengineASRRequest): Promise<VolcengineASRResponse> {
    const reqid = getUuid();
    
    const payload = {
      appid: this.appId,
      reqid: reqid,
      url: request.url,
      language: request.language || "zh",
      use_itn: request.use_itn !== false,
      use_capitalize: request.use_capitalize !== false,
      max_lines: request.max_lines || 1,
      callback_url: request.callback_url
    };

    const response = await this.makeRequest('/api/v3/auc/bigmodel/submit', 'POST', payload, {
      'Resource-Id': 'volc.bigasr.sauc'
    });
    return response as VolcengineASRResponse;
  }

  /**
   * 查询大模型ASR结果
   */
  async queryASRBigModel(taskId: string): Promise<VolcengineASRResponse> {
    const payload = {
      appid: this.appId,
      id: taskId
    };

    const response = await this.makeRequest('/api/v3/auc/bigmodel/query', 'POST', payload, {
      'Resource-Id': 'volc.bigasr.sauc'
    });
    return response as VolcengineASRResponse;
  }

  /**
   * 标准语音识别 - 提交任务
   */
  async submitASRStandard(request: VolcengineASRRequest, version: 'fast' | 'standard' = 'fast'): Promise<VolcengineASRResponse> {
    const reqid = getUuid();
    const cluster = version === 'standard' ? 'volcano_asr_standard' : 'volcano_asr';
    
    const payload = {
      appid: this.appId,
      reqid: reqid,
      cluster: cluster,
      url: request.url,
      language: request.language || "zh-CN",
      use_itn: request.use_itn !== false,
      use_capitalize: request.use_capitalize !== false,
      callback_url: request.callback_url
    };

    const response = await this.makeRequest('/api/v1/auc/submit', 'POST', payload, {
      'Resource-Id': 'volc.auc.sauc'
    });
    return response as VolcengineASRResponse;
  }

  /**
   * 查询标准ASR结果
   */
  async queryASRStandard(taskId: string): Promise<VolcengineASRResponse> {
    const payload = {
      appid: this.appId,
      id: taskId
    };

    const response = await this.makeRequest('/api/v1/auc/query', 'POST', payload, {
      'Resource-Id': 'volc.auc.sauc'
    });
    return response as VolcengineASRResponse;
  }

  /**
   * 声音复刻 - 上传音频训练
   */
  async uploadVoiceClone(request: VolcengineVoiceCloneRequest): Promise<VolcengineVoiceCloneResponse> {
    const payload = {
      appid: this.appId,
      speaker_id: request.speaker_id,
      audios: [{
        audio_bytes: request.audio_bytes,
        audio_format: request.audio_format,
        text: request.text
      }],
      source: 2,
      language: request.language || 0,
      model_type: request.model_type || 1
    };

    const response = await this.makeRequest('/api/v1/mega_tts/audio/upload', 'POST', payload, {
      'Resource-Id': 'volc.megatts.voiceclone'
    });
    return response as VolcengineVoiceCloneResponse;
  }

  /**
   * 查询声音复刻状态
   */
  async queryVoiceCloneStatus(speakerId: string): Promise<VolcengineVoiceCloneResponse> {
    const payload = {
      appid: this.appId,
      speaker_id: speakerId
    };

    const response = await this.makeRequest('/api/v1/mega_tts/status', 'POST', payload, {
      'Resource-Id': 'volc.megatts.voiceclone'
    });
    return response as VolcengineVoiceCloneResponse;
  }

  /**
   * 通用请求方法
   */
  private async makeRequest(
    endpoint: string,
    method: 'GET' | 'POST' = 'POST',
    data?: any,
    additionalHeaders?: Record<string, string>
  ): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: Record<string, string> = {
      'Authorization': `Bearer;${this.apiKey}`,
      'Content-Type': 'application/json',
      ...additionalHeaders
    };

    const config: RequestInit = {
      method,
      headers,
      signal: AbortSignal.timeout(VOLCENGINE_CONFIG.timeout)
    };

    if (method === 'POST' && data) {
      config.body = JSON.stringify(data);
    }

    // 打印完整的请求信息供调试
    console.log('=== Volcengine API Request Debug Info ===');
    console.log('URL:', url);
    console.log('Method:', method);
    console.log('Headers:', JSON.stringify(headers, null, 2));
    console.log('Body:', config.body);
    console.log('==========================================');

    let lastError: Error = new Error('Unknown error');

    for (let attempt = 1; attempt <= VOLCENGINE_CONFIG.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, config);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('=== Volcengine API Response ===');
        console.log('Response:', JSON.stringify(result, null, 2));
        console.log('===============================');
        return result;
      } catch (error) {
        lastError = error as Error;
        console.log(`=== Attempt ${attempt} failed ===`);
        console.log('Error:', lastError.message);
        console.log('=============================');

        if (attempt < VOLCENGINE_CONFIG.retryAttempts) {
          await new Promise(resolve =>
            setTimeout(resolve, VOLCENGINE_CONFIG.retryDelay * attempt)
          );
        }
      }
    }

    throw new Error(`Volcengine API request failed after ${VOLCENGINE_CONFIG.retryAttempts} attempts: ${lastError.message}`);
  }
}

/**
 * 等待异步任务完成
 */
export async function waitForAsyncTTSCompletion(
  provider: VolcengineProvider,
  taskId: string,
  maxWaitTime = 300000
): Promise<VolcengineAsyncTTSResponse> {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    const result = await provider.queryAsyncTTS(taskId);

    if (result.status === 'success' || result.status === 'failed') {
      return result;
    }

    // 等待5秒后重试
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  throw new Error('Task timeout');
}

/**
 * 等待ASR任务完成
 */
export async function waitForASRCompletion(
  provider: VolcengineProvider,
  taskId: string,
  modelType: 'bigmodel' | 'standard' | 'fast' = 'fast',
  maxWaitTime = 600000
): Promise<VolcengineASRResponse> {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    let result;

    if (modelType === 'bigmodel') {
      result = await provider.queryASRBigModel(taskId);
    } else {
      result = await provider.queryASRStandard(taskId);
    }

    if (result.status === 'success' || result.status === 'failed') {
      return result;
    }

    // 等待3秒后重试
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  throw new Error('Task timeout');
}

/**
 * 等待声音复刻训练完成
 */
export async function waitForVoiceCloneCompletion(
  provider: VolcengineProvider,
  speakerId: string,
  maxWaitTime = 3600000
): Promise<VolcengineVoiceCloneResponse> {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    const result = await provider.queryVoiceCloneStatus(speakerId);

    if (result.status === 2 || result.status === 4) {
      return result; // 训练成功
    } else if (result.status === 3) {
      throw new Error('Voice clone training failed');
    }

    // 等待30秒后重试
    await new Promise(resolve => setTimeout(resolve, 30000));
  }

  throw new Error('Training timeout');
}

/**
 * 文件转base64工具函数
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = (reader.result as string).split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * 验证音频文件格式
 */
export function validateAudioFile(file: File): string[] {
  const errors: string[] = [];

  // 检查文件大小 (10MB限制)
  if (file.size > 10 * 1024 * 1024) {
    errors.push('音频文件不能超过10MB');
  }

  // 检查文件格式
  const allowedFormats = ['wav', 'mp3', 'ogg', 'm4a', 'aac', 'pcm'];
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (!extension || !allowedFormats.includes(extension)) {
    errors.push('不支持的音频格式');
  }

  return errors;
}

/**
 * 验证音频URL格式
 */
export function validateAudioUrl(url: string): string[] {
  const errors: string[] = [];

  // 检查URL格式
  try {
    new URL(url);
  } catch {
    errors.push('无效的音频URL');
  }

  // 检查文件扩展名
  const supportedFormats = ['wav', 'mp3', 'm4a', 'aac', 'ogg', 'flac'];
  const extension = url.split('.').pop()?.toLowerCase();
  if (!extension || !supportedFormats.includes(extension)) {
    errors.push('不支持的音频格式');
  }

  return errors;
}

/**
 * 创建火山引擎服务实例
 */
export function createVolcengineProvider(apiKey?: string, appId?: string): VolcengineProvider {
  return new VolcengineProvider(apiKey, appId);
}
