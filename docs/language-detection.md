# 语言检测和切换组件

这是一个智能的语言检测和切换组件，能够自动检测用户的浏览器语言设置，并在适当的时候提示用户切换到更合适的语言。

## 功能特性

- 🌐 **自动检测浏览器语言**：使用 `navigator.language` 和 `navigator.languages` 检测用户的语言偏好
- 🎯 **智能语言映射**：将浏览器语言代码（如 zh-CN, en-US）映射到应用支持的语言
- 💾 **用户偏好记忆**：保存用户的选择，避免重复询问
- 🚫 **防打扰机制**：用户拒绝后不再重复提示
- 🌍 **国际化支持**：弹框文本支持多语言
- 🎨 **可自定义**：支持自定义样式和行为
- 🧪 **完整测试**：包含单元测试和集成测试

## 快速开始

### 基本使用

最简单的使用方式是在应用的根布局中添加语言检测器：

```tsx
import LanguageDetector from "@/components/language/detector";

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <LanguageDetector />
      </body>
    </html>
  );
}
```

### 高级配置

```tsx
import LanguageDetector from "@/components/language/detector";

export default function App() {
  const handleLanguageSwitch = (from: string, to: string) => {
    // 自定义语言切换逻辑
    console.log(`Switching from ${from} to ${to}`);
    // 执行自定义的路由跳转或其他操作
  };

  return (
    <div>
      <LanguageDetector
        debug={false}                    // 是否启用调试模式
        detectionDelay={1000}           // 检测延迟（毫秒）
        useI18n={true}                  // 是否使用国际化文本
        onLanguageSwitch={handleLanguageSwitch} // 自定义切换处理器
      />
    </div>
  );
}
```

## 组件 API

### LanguageDetector

主要的语言检测组件。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `debug` | `boolean` | `false` | 是否启用调试模式，显示检测信息 |
| `detectionDelay` | `number` | `1000` | 检测延迟时间（毫秒），确保页面完全加载 |
| `useI18n` | `boolean` | `true` | 是否使用国际化文本 |
| `onLanguageSwitch` | `(from: string, to: string) => void` | - | 自定义语言切换处理器 |

### 简化组件

```tsx
import { SimpleLanguageDetector, DebugLanguageDetector } from "@/components/language/detector";

// 使用默认配置
<SimpleLanguageDetector />

// 调试模式
<DebugLanguageDetector />
```

## 工具函数

### 语言检测工具

```tsx
import {
  getBrowserLanguage,
  getSupportedBrowserLanguage,
  shouldSuggestLanguageSwitch,
  getLanguageDisplayName,
} from "@/lib/language-detection";

// 获取浏览器语言
const browserLang = getBrowserLanguage(); // "zh-CN"

// 获取应用支持的浏览器语言
const supportedLang = getSupportedBrowserLanguage(); // "zh"

// 检查是否应该建议切换
const suggested = shouldSuggestLanguageSwitch("en"); // "zh" 或 null

// 获取语言显示名称
const displayName = getLanguageDisplayName("zh"); // "中文"
```

### 语言偏好管理

```tsx
import {
  saveLanguagePreference,
  getLanguagePreference,
  shouldShowLanguageSuggestion,
  saveLanguageSwitchAccepted,
  saveLanguageSwitchDeclined,
} from "@/lib/language-preference";

// 保存用户偏好
saveLanguageSwitchAccepted("en", "zh", "zh");

// 检查是否应该显示建议
const shouldShow = shouldShowLanguageSuggestion("en", "zh");

// 获取用户偏好
const preference = getLanguagePreference();
```

## 国际化配置

在 `i18n/messages/en.json` 和 `i18n/messages/zh.json` 中添加以下文本：

```json
{
  "language_switch": {
    "title": "Switch Language?",
    "description": "We detected that your browser language is {suggestedLanguage}. Would you like to switch from {currentLanguage} to {suggestedLanguage}?",
    "switch_button": "Switch to {suggestedLanguage}",
    "cancel_button": "Keep {currentLanguage}"
  }
}
```

## 支持的语言

当前支持的语言映射：

| 浏览器语言 | 应用语言 | 显示名称 |
|------------|----------|----------|
| `en`, `en-US`, `en-GB`, `en-CA`, `en-AU` | `en` | English |
| `zh`, `zh-CN`, `zh-TW`, `zh-HK`, `zh-MO`, `zh-SG` | `zh` | 中文 |

## 存储机制

组件使用 localStorage 存储用户偏好：

- `LANGUAGE_PREFERENCE`：用户的语言偏好设置
- `LANGUAGE_SWITCH_ASKED`：语言切换询问记录

数据会自动过期：
- 语言偏好：30天
- 询问记录：7天

## 最佳实践

### 1. 合适的检测时机

```tsx
// 在页面完全加载后进行检测
<LanguageDetector detectionDelay={1000} />
```

### 2. 自定义切换逻辑

```tsx
const handleLanguageSwitch = (from: string, to: string) => {
  // 可以添加分析追踪
  analytics.track('language_switched', { from, to });
  
  // 执行路由跳转
  router.push(pathname.replace(`/${from}`, `/${to}`));
};
```

### 3. 调试和测试

```tsx
// 开发环境启用调试
<LanguageDetector debug={process.env.NODE_ENV === 'development'} />
```

## 测试

运行测试：

```bash
# 单元测试
npm test __tests__/language-detection.test.ts

# 集成测试
npm test __tests__/language-detector-integration.test.tsx

# 所有测试
npm test
```

## 故障排除

### 常见问题

1. **弹框不显示**
   - 检查浏览器语言是否与当前页面语言不同
   - 确认浏览器语言是否被应用支持
   - 检查用户是否之前已经做过选择

2. **语言切换不生效**
   - 确认 `onLanguageSwitch` 处理器是否正确实现
   - 检查路由配置是否正确

3. **测试环境问题**
   - 确保 mock 了 `navigator` 对象
   - 确保 mock 了 `localStorage`

### 调试模式

启用调试模式查看详细信息：

```tsx
<LanguageDetector debug={true} />
```

这会在页面右下角显示检测信息，包括：
- 当前语言
- 建议语言
- 是否显示弹框
- 浏览器语言信息

## 更新日志

### v1.0.0
- 初始版本
- 支持基本的语言检测和切换
- 包含完整的测试套件
- 支持国际化文本
