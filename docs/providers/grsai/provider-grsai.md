
API 文档
基础配置
Host(海外)
复制
https://api.grsai.com
Host(国内直连)
复制
https://grsai.dakka.com.cn
使用方式Host+接口，例如:
复制
https://grsai.dakka.com.cn/v1/video/veo
Veo3视频接口
POST
/v1/video/veo
Veo3视频接口
复制
/v1/video/veo
请求方式
POST
响应方式
stream 或 回调接口
请求头 Headers
复制
{
  "Content-Type": "application/json",
  "Authorization": "Bearer apikey"
}
请求参数 (JSON)
复制
{
  "model": "veo3-fast",
  "prompt": "提示词",
  "firstFrameUrl": "https://example.com/firstFrame.png",
  "webHook": "https://example.com/callback",
  "shutProgress": false,
  "cdn": "global"
}
参数说明
model（必填）
类型: string
示例: "veo3-fast"
描述:
支持模型:
veo3-fast
veo3-pro
prompt（必填）
类型: string
示例: "A cute cat playing on the grass"
描述:
提示词，只支持英文
firstFrameUrl（选填）
类型: string
示例: "https://example.com/firstFrame.png"
描述:
首帧图片URL
webHook（选填）
类型: string
示例: "https://your-webhook-url.com/callback"
描述:
进度与结果的回调链接
接口默认以Stream流式响应进行回复，如果填了webHook，进度与结果则以Post请求回调地址的方式进行回复
请求头: Content-Type: application/json
-------
如果不使用回调，而使用轮询result接口方式获取结果，需要接口立即返回一个id
则webHook参数填"-1"，那么会立即返回一个id
shutProgress（选填）
类型: boolean
示例: false
描述:
关闭进度回复，直接回复最终结果,建议搭配webHook使用
默认false
cdn（选填）
类型: string
示例: "global"
描述:
返回的图片链接地址地区, 可选: "global"、"zh"
global(全球)
zh(国内)
默认global
webHook结果
(请求后该结果会返回一个id，用于对应回调数据)
(使用流式响应请跳过该步骤)
复制
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": "id"
  }
}
webHook结果参数说明
(使用流式响应请跳过该步骤)
code
类型: number
示例: 0
描述:
状态码：0为成功
msg
类型: string
示例: "success"
描述:
状态信息
data
类型: object
描述:
数据
data.id
类型: object
示例: "f44bcf50-f2d0-4c26-a467-26f2014a771b"
描述:
程序任务id，对应回调数据
响应参数 (JSON)
(流式响应与webHook响应的参数)
复制
{
  "id": "xxxxx",
  "url": "https://example.com/example.mp4",
  "progress": 100,
  "status": "succeeded",
  "failure_reason": "",
  "error": ""
}
响应参数说明
id
类型: string
示例: "f44bcf50-f2d0-4c26-a467-26f2014a771b"
描述:
Id (webHook回调可以用该id来对应数据)
url
类型: string
示例: "https://example.com/example.mp4"
描述:
结果视频的URL（有效期为2小时）
progress
类型: number
示例: 100
描述:
任务进度,0~100
status
类型: string
示例: "succeeded"
描述:
任务状态
"running": 进行中
"succeeded": 成功
"failed": 失败
failure_reason
类型: string
示例: "error"
描述:
失败原因
"output_moderation": 输出违规
"input_moderation": 输入违规
"error": 其他错误
------
当生成失败时，会返还积分
error
类型: string
示例: "Invalid input parameters"
描述:
失败详细信息
另外提供一个接口用于单独获取结果的接口 (如有单独获取结果的需求，可以使用该接口)
POST
/v1/draw/result
另外提供一个接口用于单独获取结果的接口
(如有单独获取结果的需求，可以使用该接口)
复制
/v1/draw/result
请求方式
POST
请求参数
复制
{
  "id": "xxxxx"
}
响应结果
复制
{
  "code": 0,
  "data": {
    "id": "xxxxx",
    "url": "https://example.com/example.mp4",
    "progress": 100,
    "status": "succeeded",
    "failure_reason": "",
    "error": ""
  },
  "msg": "success"
}
响应参数说明
code
类型: number
示例: 0
描述:
状态码：0成功, -22任务不存在
msg
类型: string
示例: "success"
描述:
状态信息
data
类型: object
描述:
视频结果，请参考上方的视频结果的数据格式





API 文档
基础配置
Host(海外)
复制
https://api.grsai.com
Host(国内直连)
复制
https://grsai.dakka.com.cn
使用方式Host+接口，例如:
复制
https://grsai.dakka.com.cn/v1/draw/completions
绘画接口
POST
/v1/draw/completions
绘画接口
复制
/v1/draw/completions
请求方式
POST
响应方式
stream 或 回调接口
请求头 Headers
复制
{
  "Content-Type": "application/json",
  "Authorization": "Bearer apikey"
}
请求参数 (JSON)
复制
{
  "model": "sora-image",
  "prompt": "描述您想要生成的图像内容的提示词",
  "size": "1:1",
  "variants": 1,
  "urls": [
    "https://example.com/example1.png",
    "https://example.com/example2.png"
  ],
  "webHook": "https://example.com/callback",
  "shutProgress": false,
  "cdn": "global"
}
参数说明
model（必填）
类型: string
示例: "sora-image"
描述:
支持模型:
sora-image
gpt-4o-image
prompt（必填）
类型: string
示例: "一只可爱的猫咪在草地上玩耍"
描述:
提示词
size（选填）
类型: string
示例: "1:1"
描述:
输出图像比例，可选："auto"、"1:1"、"3:2"、"2:3"
variants（选填）
类型: number
示例: 1
描述:
批量生成图片,可填参数: 1,2
默认1
该参数随着官方政策而调整
每增加一张图额外消耗100积分
urls（选填）
类型: string[]
示例: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
描述:
参考图片的URL，支持多张图片
webHook（选填）
类型: string
示例: "https://your-webhook-url.com/callback"
描述:
进度与结果的回调链接
接口默认以Stream流式响应进行回复
如果填了webHook，进度与结果则以Post请求回调地址的方式进行回复
请求头: Content-Type: application/json
-------
如果不使用回调，而使用轮询result接口方式获取结果，需要接口立即返回一个id
则webHook参数填"-1"，那么会立即返回一个id
shutProgress（选填）
类型: boolean
示例: false
描述:
关闭进度回复，直接回复最终结果,建议搭配webHook使用
默认false
cdn（选填）
类型: string
示例: "global"
描述:
返回的图片链接地址地区, 可选: "global"、"zh"
global(全球)
zh(国内)
默认global
webHook结果
(请求后该结果会返回一个id，用于对应回调数据)
(使用流式响应请跳过该步骤)
复制
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": "id"
  }
}
webHook结果参数说明
(使用流式响应请跳过该步骤)
code
类型: number
示例: 0
描述:
状态码：0为成功
msg
类型: string
示例: "success"
描述:
状态信息
data
类型: object
示例: {}
描述:
数据
data.id
类型: object
示例: "xxxxxx"
描述:
程序任务id，对应回调数据
响应参数 (JSON)
(流式响应与webHook响应的参数)
复制
{
  "id": "xxxxx",
  "task_id": "xxxxx",
  "url": "https://example.com/example.png",
  "width": 1024,
  "height": 1024,
  "progress": 100,
  "results": [
    {
      "url": "https://example.com/example.png",
      "width": 1024,
      "height": 1024
    }
  ],
  "status": "succeeded",
  "failure_reason": "",
  "error": ""
}
响应参数说明
id
类型: string
示例: "task_123456"
描述:
Id (webHook回调可以用该id来对应数据)
task_id
类型: string
示例: "task_123456"
描述:
该参数已废弃
url
类型: string
示例: "https://example.com/generated-image.jpg"
描述:
(旧参数，不会废弃，默认为results中第一个结果的url)
结果图片的URL（有效期为2小时）
width
类型: number
示例: 1024
描述:
(旧参数，不会废弃，默认为results中第一个结果的width)
结果图片的宽度
height
类型: number
示例: 1024
描述:
(旧参数，不会废弃，默认为results中第一个结果的height)
结果图片的高度
progress
类型: number
示例: 100
描述:
任务进度,0~100
results
类型: object[]
描述:
图片生成结果
url: 图片链接（有效期为2小时）
width: 图片宽度
height: 图片高度
status
类型: string
示例: "succeeded"
描述:
任务状态
"running": 进行中
"succeeded": 成功
"failed": 失败
failure_reason
类型: string
示例: "error"
描述:
失败原因
"output_moderation": 输出违规
"input_moderation": 输入违规
"error": 其他错误
------
当触发"error"时，会返还积分
触发"output_moderation"与"input_moderation"时，不会返还积分。
提示：当触发"error"时，可尝试重新提交任务来确保系统稳定性。
error
类型: string
示例: "Invalid input parameters"
描述:
失败详细信息
另外提供一个接口用于单独获取结果的接口 (如有单独获取结果的需求，可以使用该接口)
POST
/v1/draw/result
另外提供一个接口用于单独获取结果的接口
(如有单独获取结果的需求，可以使用该接口)
复制
/v1/draw/result
Method
POST
请求参数
复制
{
  "id": "xxxxx"
}
响应结果
复制
{
  "code": 0,
  "data": {
    "id": "xxx",
    "task_id": "xxx",
    "url": "https://example.com/example.png",
    "width": 1024,
    "height": 1536,
    "progress": 100,
    "status": "succeeded",
    "failure_reason": "",
    "error": "",
    "results": [
      {
        "url": "https://example.com/example.png",
        "width": 1024,
        "height": 1536
      }
    ]
  },
  "msg": "success"
}
响应参数说明
code
类型: number
示例: 0
描述:
状态码：0成功, -22任务不存在
msg
类型: string
示例: "success"
描述:
状态信息
data
类型: object
描述:
绘画结果，请参考上方的绘画结果的数据格式



API 文档
基础配置
说明
接口参数与响应格式与openai api一致
接口地址更换为
https://api.grsai.com
原接口
https://api.openai.com/v1/chat/completions
更换后
https://api.grsai.com/v1/chat/completions
openai api文档
复制
https://platform.openai.com/docs/api-reference/chat/create?lang=curl
支持模型
gemini-2.5-pro
gemini-2.5-flash
gemini-2.5-flash-lite
gpt-4o-image
gpt-4o-all
o4-mini-all
gpt-4o-mini
示例
以下是简单的示例
请求头 Headers
复制
{
  "Content-Type": "application/json",
  "Authorization": "Bearer apikey"
}
请求参数 (JSON)
复制
{
  "model": "gpt-4o-mini",
  "stream": true,
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello!"
    }
  ]
}
参数说明
model（必填）
类型: string
示例: "gpt-4o-mini"
描述:
模型 "gpt-4o-mini"
stream（必填）
类型: boolean
示例: true
描述:
是否流式响应
messages（必填）
类型: array
示例: [{"role": "user", "content": "你好"}]
描述:
对话上下文信息
非流式响应 (JSON)
复制
{
  "id": "xxxx",
  "object": "chat.completion",
  "created": 1741569952,
  "model": "gpt-4o-mini",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I assist you today?",
        "refusal": null,
        "annotations": []
      },
      "finish_reason": "stop"
    }
  ]
}
流式响应 (JSON)
复制
{
  "id": "xxxx",
  "object": "chat.completion",
  "created": 1741569952,
  "model": "gpt-4o-mini",
  "choices": [
    {
      "index": 0,
      "delta": {
        "role": "assistant",
        "content": "Hello! How can I assist you today?",
        "refusal": null,
        "annotations": []
      },
      "finish_reason": "stop"
    }
  ]
}
全部展开



API 文档
基础配置
Host(海外)
复制
https://api.grsai.com
Host(国内直连)
复制
https://grsai.dakka.com.cn
使用方式Host+接口，例如:
复制
https://grsai.dakka.com.cn/v1/draw/flux
Flux绘画接口
POST
/v1/draw/flux
Flux绘画接口
复制
/v1/draw/flux
请求方式
POST
响应方式
stream 或 回调接口
请求头 Headers
复制
{
  "Content-Type": "application/json",
  "Authorization": "Bearer apikey"
}
请求参数 (JSON)
复制
{
  "model": "flux-kontext-max",
  "urls": [
    "https://example.com/example.png"
  ],
  "prompt": "提示词",
  "seed": 21324124,
  "aspectRatio": "1:1",
  "webHook": "https://example.com/callback",
  "shutProgress": false,
  "cdn": "global"
}
参数说明
model（必填）
类型: string
示例: "flux-kontext-max"
描述:
支持模型:
flux-pro-1.1
flux-pro-1.1-ultra
flux-kontext-pro
flux-kontext-max
prompt（必填）
类型: string
示例: "一只可爱的猫咪在草地上玩耍"
描述:
提示词
urls（选填）
类型: string
示例: ["https://example.com/example.png"]
描述:
参考图片的URL
该参数支持模型:
flux-kontext-pro
flux-kontext-max
seed（选填）
类型: number
示例: 123456
描述:
随机种子,填0则随机
aspectRatio（选填）
类型: string
示例: 1:1
描述:
输出图像比例, 支持常用的比例, 以下举例一部分:
1:1, 16:9, 9:16, 4:3, 1:2, 21:9, 2:3, 3:2等等...
---------
除了设定比例外
还可以根据参考图自动匹配比例
填写参数: auto 即可
webHook（选填）
类型: string
示例: "https://your-webhook-url.com/callback"
描述:
进度与结果的回调链接
接口默认以Stream流式响应进行回复
如果填了webHook，进度与结果则以Post请求回调地址的方式进行回复
请求头: Content-Type: application/json
-------
如果不使用回调，而使用轮询result接口方式获取结果，需要接口立即返回一个id
则webHook参数填"-1"，那么会立即返回一个id
shutProgress（选填）
类型: boolean
示例: false
描述:
关闭进度回复，直接回复最终结果,建议搭配webHook使用
默认false
cdn（选填）
类型: string
示例: "global"
描述:
返回的图片链接地址地区, 可选: "global"、"zh"
global(全球)
zh(国内)
默认global
webHook结果
(请求后该结果会返回一个id，用于对应回调数据)
(使用流式响应请跳过该步骤)
复制
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": "id"
  }
}
webHook结果参数说明
(使用流式响应请跳过该步骤)
code
类型: number
示例: 0
描述:
状态码：0为成功
msg
类型: string
示例: "success"
描述:
状态信息
data
类型: object
描述:
数据
data.id
类型: object
示例: "f44bcf50-f2d0-4c26-a467-26f2014a771b"
描述:
程序任务id，对应回调数据
响应参数 (JSON)
(流式响应与webHook响应的参数)
复制
{
  "id": "xxxxx",
  "url": "https://example.com/example.png",
  "seed": 322143556,
  "progress": 100,
  "status": "succeeded",
  "failure_reason": "",
  "error": ""
}
响应参数说明
id
类型: string
示例: "f44bcf50-f2d0-4c26-a467-26f2014a771b"
描述:
Id (webHook回调可以用该id来对应数据)
url
类型: string
示例: "https://example.com/generated-image.jpg"
描述:
结果图片的URL（有效期为2小时）
seed
类型: number
示例: 123456
描述:
随机种子
progress
类型: number
示例: 100
描述:
任务进度,0~100
status
类型: string
示例: "succeeded"
描述:
任务状态
"running": 进行中
"succeeded": 成功
"failed": 失败
failure_reason
类型: string
示例: "error"
描述:
失败原因
"output_moderation": 输出违规
"input_moderation": 输入违规
"error": 其他错误
------
当触发"error"时，会返还积分
触发"output_moderation"与"input_moderation"时，不会返还积分。
提示：当触发"error"时，可尝试重新提交任务来确保系统稳定性。
error
类型: string
示例: "Invalid input parameters"
描述:
失败详细信息
另外提供一个接口用于单独获取结果的接口 (如有单独获取结果的需求，可以使用该接口)
POST
/v1/draw/result
另外提供一个接口用于单独获取结果的接口
(如有单独获取结果的需求，可以使用该接口)
复制
/v1/draw/result
请求方式
POST
请求参数
复制
{
  "id": "xxxxx"
}
响应结果
复制
{
  "code": 0,
  "data": {
    "id": "xxxxx",
    "url": "https://example.com/example.png",
    "seed": 322143556,
    "progress": 100,
    "status": "succeeded",
    "failure_reason": "",
    "error": ""
  },
  "msg": "success"
}
响应参数说明
code
类型: number
示例: 0
描述:
状态码：0成功, -22任务不存在
msg
类型: string
示例: "success"
描述:
状态信息
data
类型: object
描述:
绘画结果，请参考上方的绘画结果的数据格式