<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/79823 
 saved date: Mon Jul 21 2025 16:42:48 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>参数基本说明--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=参数基本说明--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=参数基本说明--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 sidebarbot-Qz1r"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-13><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-11><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-10><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>音频生成</span><span class=divider-DURY>/</span><span class=item-Dz0C>语音合成</span><span class=divider-DURY>/</span><span class=item-Dz0C>在线语音合成API</span><span class=divider-DURY>/</span><span class=item-Dz0C>参数基本说明</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>参数基本说明</div><div class=info-TbRN><span>最近更新时间：2025.07.03 13:06:47</span><span>首次发布时间：2021.12.20 14:44:12</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><p><span id=请求参数></span><div id=请求参数 class="md-h1 heading-h1">请求参数</div><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>含义</p><th><p>层级</p><th><p>格式</p><th><p>必需</p><th><p>备注</p><tbody><tr><td><p>app</p><td><p>应用相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>appid</p><td><p>应用标识</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>需要申请，具体见<a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ1</a></p><tr><td><p>token</p><td><p>应用令牌</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>可传入任意非空值</p><tr><td><p>cluster</p><td><p>业务集群</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p><strong>标准音色、复刻等均不相同，具体见</strong><a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ1</a></p><tr><td><p>user</p><td><p>用户相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>uid</p><td><p>用户标识</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>可传入任意非空值，传入值可以通过服务端日志追溯</p><tr><td><p>audio</p><td><p>音频相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>voice_type</p><td><p>音色类型</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p><a href=https://www.volcengine.com/docs/6561/79824 rel=noreferrer>发音人参数列表</a>，<strong>复刻音色使用声音ID(speaker id)</strong></p><tr><td><p>rate</p><td><p>音频采样率</p><td><p>2</p><td><p>int</p><td><td><p>默认为 24000，可选8000，16000</p><tr><td><p>encoding</p><td><p>音频编码格式</p><td><p>2</p><td><p>string</p><td><td><p>wav / pcm / ogg_opus / mp3，默认为 pcm<br>
注意：wav 不支持流式</p><tr><td><p>compression_rate</p><td><p>opus格式时编码压缩比</p><td><p>2</p><td><p>int</p><td><td><p>[1, 20]，默认为 1</p><tr><td><p>speed_ratio</p><td><p>语速</p><td><p>2</p><td><p>float</p><td><td><p>[0.2,3]，默认为1，通常保留一位小数即可</p><tr><td><p>volume_ratio</p><td><p>音量</p><td><p>2</p><td><p>float</p><td><td><p>0.1, 3]，默认为1，通常保留一位小数即可</p><tr><td><p>pitch_ratio</p><td><p>音高</p><td><p>2</p><td><p>float</p><td><td><p>[0.1, 3]，默认为1，通常保留一位小数即可</p><tr><td><p>emotion</p><td><p>情感/风格</p><td><p>2</p><td><p>string</p><td><td><p><a href=https://www.volcengine.com/docs/6561/79824 rel=noreferrer>发音人参数列表</a></p><tr><td><p>language</p><td><p>语言类型</p><td><p>2</p><td><p>string</p><td><td><p><a href=https://www.volcengine.com/docs/6561/79824 rel=noreferrer>发音人参数列表</a></p><tr><td><p>request</p><td><p>请求相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>reqid</p><td><p>请求标识</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>需要保证每次调用传入值唯一，建议使用 UUID</p><tr><td><p>text</p><td><p>文本</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>合成语音的文本，长度限制 1024 字节（UTF-8编码）。<strong>复刻音色没有此限制，但是HTTP接口有60s超时限制</strong></p><tr><td><p>text_type</p><td><p>文本类型</p><td><p>2</p><td><p>string</p><td><td><p>plain / ssml, 默认为plain</p><tr><td><p>silence_duration</p><td><p>句尾静音时长</p><td><p>2</p><td><p>int</p><td><td><p>单位为ms，默认为125</p><tr><td><p>operation</p><td><p>操作</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>query（非流式，http只能query） / submit（流式）</p><tr><td><p>with_frontend</p><td><p>时间戳相关</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳</p><tr><td><p>frontend_type</p><td><p>时间戳相关</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳</p><tr><td><p>with_timestamp</p><td><p>时间戳相关</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>传入1时表示启用。新版时间戳参数，可用来替换with_frontend和frontend_type，可返回原文本的时间戳，而非TN后文本，即保留原文中的阿拉伯数字或者特殊符号等。注意：原文本中的多个标点连用或者空格依然会被处理，但不影响时间戳连贯性<br>
（小语种音色目前无法返回正确时间戳）</p><tr><td><p>split_sentence</p><td><p>复刻音色语速优化</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>仅当使用复刻音色时设为1，可优化语速过快问题。有可能会导致时间戳多次返回。详情可见：<a href=https://www.volcengine.com/docs/6561/1204182 rel=noreferrer>声音复刻录音指导-badcase优化建议2</a></p><tr><td><p>pure_english_opt</p><td><p>英文前端优化</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>当pure_english_opt为1的时候，中文音色读纯英文时可以正确处理文本中的阿拉伯数字</p><tr><td><p>extra_param</p><td><p>额外参数</p><td><td><td><td><p>附加功能参数</p><tr><td><p>disable_emoji_filter</p><td><p>emoji不过滤显示</p><td><p>3</p><td><p>bool</p><td><td><p>开启emoji表情在文本中不过滤显示，默认为False，建议搭配时间戳参数一起使用。<br>
Python示例："extra_param": json.dumps({"disable_emoji_filter": True})</p></table><p>请求示例<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"app"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"appid123"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"token"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"access_token"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"cluster"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"volcano_tts"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"user"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"uid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"uid123"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"voice_type"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"BV700_streaming"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"encoding"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"mp3"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"compression_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>24000</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"speed_ratio"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1.0</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"volume_ratio"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1.0</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"pitch_ratio"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1.0</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"emotion"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"happy"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"language"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"cn"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"request"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"reqid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"uuid"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"字节跳动语音合成"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"text_type"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"plain"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"operation"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"query"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"silence_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"125"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"with_frontend"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"1"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"frontend_type"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"unitTson"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"pure_english_opt"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"1"</span>
        <span class=hljs-attr>"extra_param"</span><span class=hljs-punctuation>:</span> json.dumps(<span class=hljs-punctuation>{</span><span class=hljs-attr>"disable_emoji_filter"</span><span class=hljs-punctuation>:</span> True<span class=hljs-punctuation>}</span>)
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=返回参数></span><div id=返回参数 class="md-h1 heading-h1">返回参数</div><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>含义</p><th><p>层级</p><th><p>格式</p><th><p>备注</p><tbody><tr><td><p>reqid</p><td><p>请求 ID</p><td><p>1</p><td><p>string</p><td><p>请求 ID,与传入的参数中 reqid 一致</p><tr><td><p>code</p><td><p>请求状态码</p><td><p>1</p><td><p>int</p><td><p>错误码，参考下方说明</p><tr><td><p>message</p><td><p>请求状态信息</p><td><p>1</p><td><p>string</p><td><p>错误信息</p><tr><td><p>sequence</p><td><p>音频段序号</p><td><p>1</p><td><p>int</p><td><p>负数表示合成完毕</p><tr><td><p>data</p><td><p>合成音频</p><td><p>1</p><td><p>string</p><td><p>返回的音频数据，base64 编码</p><tr><td><p>addition</p><td><p>额外信息</p><td><p>1</p><td><p>string</p><td><p>额外信息父节点</p><tr><td><p>duration</p><td><p>音频时长</p><td><p>2</p><td><p>string</p><td><p>返回音频的长度，单位ms</p><tr><td><p>frontend</p><td><p>时间戳信息</p><td><p>2</p><td><p>string</p><td><p>包含字级别和音素级别的时间戳信息</p></table><p>响应示例<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
   <span class=hljs-attr>"reqid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"reqid"</span><span class=hljs-punctuation>,</span>
   <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3000</span><span class=hljs-punctuation>,</span>
   <span class=hljs-attr>"operation"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"query"</span><span class=hljs-punctuation>,</span>
   <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span><span class=hljs-punctuation>,</span>
   <span class=hljs-attr>"sequence"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>-1</span><span class=hljs-punctuation>,</span>
   <span class=hljs-attr>"data"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"base64 encoded binary data"</span><span class=hljs-punctuation>,</span>
   <span class=hljs-attr>"addition"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
      <span class=hljs-attr>"description"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"..."</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"1960"</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"frontend"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{
         "</span>words<span class=hljs-string>": [{
            "</span>word<span class=hljs-string>": "</span>字<span class=hljs-string>",
            "</span>start_time<span class=hljs-string>": 0.025,
            "</span>end_time<span class=hljs-string>": 0.185
         },
         ... 
         {
            "</span>word<span class=hljs-string>": "</span>。<span class=hljs-string>",
            "</span>start_time<span class=hljs-string>": 1.85,
            "</span>end_time<span class=hljs-string>": 1.955
         }],
         "</span>phonemes<span class=hljs-string>": [{
            "</span>phone<span class=hljs-string>": "</span>C0z<span class=hljs-string>",
            "</span>start_time<span class=hljs-string>": 0.025,
            "</span>end_time<span class=hljs-string>": 0.105
         },
         ... 
         {
            "</span>phone<span class=hljs-string>": "</span>。<span class=hljs-string>",
            "</span>start_time<span class=hljs-string>": 1.85,
            "</span>end_time<span class=hljs-string>": 1.955
         }]
      }"</span>
   <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=错误码说明></span><div id=错误码说明 class="md-h1 heading-h1">错误码说明</div><table class=volc-viewer-table><thead><tr><th><p>错误码</p><th><p>错误描述</p><th><p>举例</p><th><p>建议行为</p><tbody><tr><td><p>3000</p><td><p>请求正确</p><td><p>正常合成</p><td><p>正常处理</p><tr><td><p>3001</p><td><p>无效的请求</p><td><p>一些参数的值非法，比如operation/workflow配置错误</p><td><p>检查参数</p><tr><td><p>3003</p><td><p>并发超限</p><td><p>超过在线设置的并发阈值</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3005</p><td><p>后端服务忙</p><td><p>后端服务器负载高</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3006</p><td><p>服务中断</p><td><p>请求已完成/失败之后，相同reqid再次请求</p><td><p>检查参数</p><tr><td><p>3010</p><td><p>文本长度超限</p><td><p>单次请求超过设置的文本长度阈值</p><td><p>检查参数</p><tr><td><p>3011</p><td><p>无效文本</p><td><p>参数有误或者文本为空、文本与语种不匹配、文本只含标点</p><td><p>检查参数</p><tr><td><p>3030</p><td><p>处理超时</p><td><p>单次请求超过服务最长时间限制</p><td><p>重试或检查文本</p><tr><td><p>3031</p><td><p>处理错误</p><td><p>后端出现异常</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3032</p><td><p>等待获取音频超时</p><td><p>后端网络异常</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3040</p><td><p>音色克隆链路网络异常</p><td><p>后端网络异常</p><td><p>重试</p><tr><td><p>3050</p><td><p>音色克隆音色查询失败</p><td><p>检查使用的voice_type代号</p><td><p>检查参数</p></table><p><span id=常见错误返回说明></span><div id=常见错误返回说明 class="md-h1 heading-h1">常见错误返回说明</div><ol><li>错误返回：<br>
"message": "quota exceeded for types: xxxxxxxxx_lifetime"<br><strong>错误原因：试用版用量用完了，需要开通正式版才能继续使用</strong><li>错误返回：<br>
"message": "quota exceeded for types: concurrency"<br><strong>错误原因：并发超过了限定值，需要减少并发调用情况或者增购并发</strong><li>错误返回：<br>
"message": "Fail to feed text, reason Init Engine Instance failed"<br><strong>错误原因：voice_type / cluster 传递错误</strong><li>错误返回：<br>
"message": "illegal input text!"<br><strong>错误原因：传入的text无效，没有可合成的有效文本。比如全部是标点符号或者emoji表情，或者使用中文音色时，传递日语，以此类推。多语种音色，也需要使用language指定对应的语种</strong><li>错误返回：<br>
"message": "authenticate request: load grant: requested grant not found"<br><strong>错误原因：鉴权失败，需要检查appid&amp;token的值是否设置正确，同时，鉴权的正确格式为</strong><br><strong>headers["Authorization"] = "Bearer;${token}"</strong><li>错误返回：<br>
"message': 'extract request resource id: get resource id: access denied"<br><strong>错误原因：语音合成已开通正式版且未拥有当前音色授权，需要在控制台购买该音色才能调用。标注免费的音色除BV001_streaming及BV002_streaming外，需要在控制台进行下单（支付0元）</strong></ol></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/79821><div class=head-N5m8>上一篇</div><div class=info-R5lO>Websocket接口</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/79824><div class=head-N5m8>下一篇</div><div class=info-R5lO>发音人参数列表</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>