<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/1096680 
 saved date: Mon Jul 21 2025 16:44:01 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>API接口文档--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=API接口文档--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=API接口文档--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 false"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-13><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-12><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>音频生成</span><span class=divider-DURY>/</span><span class=item-Dz0C>精品长文本语音合成</span><span class=divider-DURY>/</span><span class=item-Dz0C>API接口文档</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>API接口文档</div><div class=info-TbRN><span>最近更新时间：2024.04.15 14:17:21</span><span>首次发布时间：2023.06.29 16:18:04</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><div id=接口说明 class="md-h1 heading-h1">接口说明</div><p>精品长文本语音合成为异步合成服务，提供“创建合成任务”和“查询合成结果”两个接口，也可通过http回调获取合成结果。<br>
请确认是否可满足业务需求再进行接入，本产品适用于需要批量合成较长文本，且对返回时效性无强需求的场景，单次可支持10万字符以内文本，异步返回音频。对于输入的文本请求，会进入集群排队处理，返回时长会受集群负载影响波动，通常返回时间会在数十分钟，最长返回时延3小时以内。如出现长时间未返回情况，如无报错，请耐心等待。<br>
长文本合成分为“普通版”和“情感预测版”，两者需要开通不同的服务，接口地址不同，支持的音色列表也不相同，请仔细阅读文档。<div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>创建合成任务的频率限制为10 QPS，请勿一次性提交过多任务。<br>
本产品不适合对于时效性有强需求的场景，如有需求建议接入语音合成（短文本）接口。</p></div><div id=鉴权 class="md-h1 heading-h1">鉴权</div><p>请求接口时，需要携带<code>Resource-Id</code>和<code>Authorization</code>两个header，缺一不可。<blockquote><p>参考文档：<a href=https://www.volcengine.com/docs/6561/1105162 target=_blank rel=noreferrer class=external>鉴权方法</a></p></blockquote><div id=创建合成任务 class="md-h1 heading-h1">创建合成任务</div><h2 id=请求参数>请求参数</h2><table class=volc-viewer-table><thead><tr><th>服务类型<th>接口地址<tbody><tr><td>普通版<td>https://openspeech.bytedance.com/api/v1/tts_async/submit<tr><td>情感预测版<td>https://openspeech.bytedance.com/api/v1/tts_async_with_emotion/submit</table><p><strong>请求方式：<code>POST</code></strong><br><strong>Content-Type：</strong> <code>application/json</code><br><strong>请求参数说明：</strong><table class=volc-viewer-table><thead><tr><th>参数名称<th>参数类型<th>是否必需<th>描述<tbody><tr><td>appid<td>string<td>Y<td>Appid从控制台获取<tr><td>reqid<td>string<td>Y<td>Request ID，不可重复，长度20～64，建议使用uuid<tr><td>text<td>string<td>Y<td>合成文本，长度小于10万字符，支持SSML。SSML需要以&lt;speak&gt;开头和&lt;/speak&gt;结束，且全文只出现一组&lt;speak&gt;标签，支持的SSML标签可参考<a href=https://www.volcengine.com/docs/6561/104897 target=_blank rel=noreferrer class=external>SSML标记语言</a><tr><td>format<td>string<td>Y<td>输出音频格式，支持pcm/wav/mp3/ogg_opus<tr><td>voice_type<td>string<td>Y<td>音色voice_type，见<a href=https://www.volcengine.com/docs/6561/1108211 target=_blank rel=noreferrer class=external>音色列表</a><tr><td>voice<td>string<td>N<td>音色voice，情感预测版voice为空时，使用预测结果；voice不为空时，使用指定的voice；其余情况使用默认voice<tr><td>language<td>string<td>N<td>语种，与音色有关，具体值参考<a href=https://www.volcengine.com/docs/6561/1108211 target=_blank rel=noreferrer class=external>音色列表</a>，默认为中文<tr><td>sample_rate<td>int<td>N<td>采样率，默认为24000<tr><td>volume<td>float<td>N<td>音量，范围0.1～3，默认为1<tr><td>speed<td>float<td>N<td>语速，范围0.2～3，默认为1<tr><td>pitch<td>float<td>N<td>语调，范围0.1～3，默认为1<tr><td>enable_subtitle<td>int<td>N<td>是否开启字幕时间戳，0表示不开启，1表示开启<strong>句级别</strong>字幕时间戳，2表示开启<strong>字词级别</strong>时间戳，3表示开启<strong>音素级别</strong>时间戳<tr><td>sentence_interval<td>int<td>N<td>句间停顿，单位毫秒，范围0～3000，默认为预测值<tr><td>style<td>string<td>N<td>指定情感，“情感预测版”默认为预测值，“普通版”默认为音色默认值，音色支持的情感见<a href=https://www.volcengine.com/docs/6561/1108211 target=_blank rel=noreferrer class=external>音色列表</a><tr><td>callback_url<td>string<td>N<td>回调地址，支持http和https，使用方法见<a href=#%E7%BB%93%E6%9E%9C%E5%9B%9E%E8%B0%83 rel=noreferrer>结果回调</a></table><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>在 “情感预测版”接口中使用不支持多情感的音色，将会合成失败。是否支持多情感见<a href=https://www.volcengine.com/docs/6561/1108211 target=_blank rel=noreferrer class=external>音色列表</a></p></div><p><strong>请求参数示例：</strong><pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"123456"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"火山引擎异步长文本合成。"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"mp3"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"voice_type"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"BV701_streaming"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"sample_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>24000</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"volume"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1.2</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"speed"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0.9</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"pitch"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1.1</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"enable_subtitle"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"callback_url"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"http://x.y.z/callback"</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><h2 id=返回结果>返回结果</h2><p><strong>返回结果示例：</strong><br>
请求成功：<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
  <span class=hljs-attr>"task_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bd0c2171-4b38-4c05-b685-11f3d240ee8d"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"task_status"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"text_length"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>12</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p>请求失败：<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
  <span class=hljs-attr>"reqid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"e8f41275-72a3-45b5-af3c-61047f406cac"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>40000</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"请求参数错误：text不能为空"</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><strong>返回参数说明：</strong><table class=volc-viewer-table><thead><tr><th>参数名称<th>类型<th>描述<tbody><tr><td>task_id<td>string<td>任务ID，<strong>注意保存，用于查询合成结果</strong><tr><td>task_status<td>int<td>任务状态，0-合成中，1-合成成功，2-合成失败<tr><td>text_length<td>int<td>合成需要消耗的字符数，含标点符号<tr><td>code<td>int<td>错误码，参考<a href=#%E9%94%99%E8%AF%AF%E7%A0%81%E8%AF%B4%E6%98%8E rel=noreferrer>错误码说明</a><tr><td>message<td>string<td>错误信息</table><div id=查询合成结果 class="md-h1 heading-h1">查询合成结果</div><h2 id=请求参数-2>请求参数</h2><table class=volc-viewer-table><thead><tr><th>服务类型<th>接口地址<tbody><tr><td>普通版<td>https://openspeech.bytedance.com/api/v1/tts_async/query<tr><td>情感预测版<td>https://openspeech.bytedance.com/api/v1/tts_async_with_emotion/query</table><p><strong>请求方式：<code>GET</code></strong><br><strong>请求参数说明：</strong><table class=volc-viewer-table><thead><tr><th>参数名称<th>参数类型<th>是否必需<th>描述<tbody><tr><td>appid<td>string<td>Y<td>Appid从控制台获取<tr><td>task_id<td>string<td>Y<td>创建合成任务时返回的task_id</table><p><strong>请求参数示例：</strong><pre class=hljs><code class="language-GET volc-pre-code">https://openspeech.bytedance.com/api/v1/tts_async/query?appid=123456&amp;task_id=bd0c2171-4b38-4c05-b685-11f3d240ee8d
</code><div class="volc-pre-code-language no-copy">GET</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><h2 id=返回结果-2>返回结果</h2><p><strong>返回结果示例：</strong><br>
请求成功：<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
  <span class=hljs-attr>"task_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bd0c2171-4b38-4c05-b685-11f3d240ee8d"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"task_status"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"text_length"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>12</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"audio_url"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"https://lf9-lab-speech-tt-sign.bytetos.com/tos-cn-o-14155/aef41ebf89124edba16d4e97e455e007?x-expires=1687778318&amp;x-signature=SJub692wmwsxboJTgl2VX55tIzY%3D"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"url_expire_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1687777943</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"sentences"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
    <span class=hljs-punctuation>{</span>
      <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"火山引擎异步长文本合成。"</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"origin_text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"火山引擎异步长文本合成。"</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"paragraph_no"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"begin_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4211</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"emotion"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"neutral"</span>
      <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
        <span class=hljs-punctuation>{</span>
	        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"火"</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"begin"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>25</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"end"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>235</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"phonemes"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span> <span class=hljs-attr>"ph"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"C0h"</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"begin"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>25</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"end"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>130</span> <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span> <span class=hljs-attr>"ph"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"C0uo"</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"begin"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>130</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"end"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>235</span> <span class=hljs-punctuation>}</span>
          <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
        <span class=hljs-punctuation>{</span>
	        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"山"</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"begin"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>235</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"end"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>495</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"phonemes"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span> <span class=hljs-attr>"ph"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"C0sh"</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"begin"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>235</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"end"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>345</span> <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span> <span class=hljs-attr>"ph"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"C0an"</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"begin"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>345</span><span class=hljs-punctuation>,</span> <span class=hljs-attr>"end"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>495</span> <span class=hljs-punctuation>}</span>
          <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
        ...
      <span class=hljs-punctuation>]</span>
    <span class=hljs-punctuation>}</span>
  <span class=hljs-punctuation>]</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p>请求失败：<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
  <span class=hljs-attr>"reqid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bd0c2171-4b38-4c05-b685-11f3d240ee8d"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>40001</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"没有可以合成的有效字符"</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><strong>返回参数说明：</strong><table class=volc-viewer-table><thead><tr><th>参数名称<th>类型<th>描述<tbody><tr><td>task_id<td>string<td>任务ID<tr><td>task_status<td>int<td>任务状态，0-合成中，1-合成成功，2-合成失败<tr><td>text_length<td>int<td>合成消耗的字符数，含标点符号<tr><td>audio_url<td>string<td>音频URL，<strong>有效期为1个小时，请及时下载</strong><tr><td>url_expire_time<td>int<td>音频URL过期时间（UNIX时间戳）<tr><td>sentences<td>List<td>分句信息，enable_subtitle≥1才会返回<tr><td>sentences.text<td>string<td>实际合成的文本，会过滤掉一些符号、表情和无法合成的字符<tr><td>sentences.origin_text<td>string<td>原文分句，所有句子拼起来与输入文本完全一致<tr><td>sentences.paragraph_no<td>int<td>分句所属段落，以换行符\n或&lt;/p&gt;划分段落<tr><td>sentences.begin_time<td>int<td>分句开始时间，单位：毫秒<tr><td>sentences.end_time<td>int<td>分句结束时间，单位：毫秒<tr><td>sentences.emotion<td>string<td>分句情感，“情感预测版”才会返回<tr><td>sentences.words<td>List<td>字词信息，enable_subtitle≥2才会返回<tr><td>sentences.words.text<td>string<td>字词文本<tr><td>sentences.words.begin<td>int<td>字词开始时间，单位：毫秒<tr><td>sentences.words.end<td>int<td>字词结束时间，单位：毫秒<tr><td>sentences.words.phonemes<td>List<td>音素信息，enable_subtitle=3才会返回<tr><td>sentences.words.phonemes.ph<td>string<td>音素<tr><td>sentences.words.phonemes.begin<td>int<td>音素开始时间，单位：毫秒<tr><td>sentences.words.phonemes.end<td>int<td>音素结束时间，单位：毫秒</table><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<ol><li>合成结果保留7天，7天内都可以通过该接口查询合成结果，过期后自动删除。<li>下载URL有效期为1小时，请勿直接保存audio_url，应及时下载音频或转存至你的云存储中。<li>audio_url过期后（状态码401或403），可重新请求查询接口获取新的URL。</ol></div><div id=错误码说明 class="md-h1 heading-h1">错误码说明</div><table class=volc-viewer-table><thead><tr><th>错误码<th>错误码描述<th>解决办法<tbody><tr><td>40000<td>请求参数错误<td>根据返回的message检查请求参数<tr><td>40001<td>没有可以合成的有效字符<td>检查请求参数中的text<tr><td>40002<td>该音色不支持多情感<td>可用音色见<a href=https://www.volcengine.com/docs/6561/1108211 target=_blank rel=noreferrer class=external>音色列表</a> ，或使用“普通版”合成<tr><td>40300<td>试用额度不足<td>开通正式版服务<tr><td>40400<td>任务不存在或已过期<td>检查task_id是否正确<tr><td>50000<td>服务器错误<td>建议先重试，重试无效请联系客服<tr><td>50001<td>合成失败<td>建议先重试，重试无效请联系客服<tr><td>50002<td>生成下载URL失败<td>建议先重试，重试无效请联系客服</table><div id=结果回调 class="md-h1 heading-h1">结果回调</div><p>如果“创建合成任务”时传入了<strong>callback_url</strong>，服务器将会在合成成功/失败时，以接口回调的方式通知用户。<br><strong>请求方式：<code>POST</code></strong><br><strong>Content-Type：</strong> <code>application/json</code><br><strong>回调参数示例:</strong><br>
合成成功：<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
	<span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span>
  <span class=hljs-attr>"task_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bd0c2171-4b38-4c05-b685-11f3d240ee8d"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"task_status"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"text_length"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>12</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"audio_url"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"https://lf9-lab-speech-tt-sign.bytetos.com/tos-cn-o-14155/aef41ebf89124edba16d4e97e455e007?x-expires=1687778318&amp;x-signature=SJub692wmwsxboJTgl2VX55tIzY%3D"</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"url_expire_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1687777943</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"sentences"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
    ...
  <span class=hljs-punctuation>]</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p>合成失败：<pre class=hljs><code class="language-json volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>40001</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"没有可以合成的有效字符"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"task_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bd0c2171-4b38-4c05-b685-11f3d240ee8d"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"task_status"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"text_length"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>12</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">json</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>不保证回调成功，建议在提交任务一定时间后（如3个小时）仍未收到回调，则主动请求“查询合成结果”接口。</p></div></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/1105162><div class=head-N5m8>上一篇</div><div class=info-R5lO>鉴权方法</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/1108211><div class=head-N5m8>下一篇</div><div class=info-R5lO>音色列表</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>