<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/192519 
 saved date: Mon Jul 21 2025 16:45:12 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>录音文件识别极速版--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=录音文件识别极速版--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=录音文件识别极速版--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><style>img[src="data:,"],source[src="data:,"]{display:none!important}</style><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 false"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-13><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-20><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-17><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>语音识别</span><span class=divider-DURY>/</span><span class=item-Dz0C>录音文件识别</span><span class=divider-DURY>/</span><span class=item-Dz0C>录音文件识别极速版</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>录音文件识别极速版</div><div class=info-TbRN><span>最近更新时间：2025.07.03 13:19:02</span><span>首次发布时间：2023.03.08 14:13:52</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><p><span id=_1-流程简介></span><div id=_1-流程简介 class="md-h1 heading-h1">1. 流程简介</div><p>录音文件识别极速版服务的处理流程分为提交任务和查询结果两个阶段<ul><li>任务提交：提交音频链接，并获取服务端分配的任务 ID<li>结果查询：通过任务 ID 查询转写结果</ul><p>服务也支持回调通知方式。客户端在提交任务时注册回调地址，服务端转写完成后请求回调地址通知结果，不需要客户端主动查询。<br><img alt=Image class=volc-image-img src=data:, width=185><br class=sf-hidden><span id=_2-鉴权></span><div id=_2-鉴权 class="md-h1 heading-h1">2. 鉴权</div><p>设置鉴权内容，请参考<a href=https://www.volcengine.com/docs/6561/107789 rel=noreferrer>鉴权方法</a>。<br><span id=_3-提交任务></span><div id=_3-提交任务 class="md-h1 heading-h1">3. 提交任务</div><p><span id=_3-1-域名></span><h2 id=_3-1-域名>3.1 域名</h2><p>火山地址：https://openspeech.bytedance.com/api/v1/auc/submit<br><span id=_3-2-请求></span><h2 id=_3-2-请求>3.2 请求</h2><p>请求方式：HTTP POST。<br>
请求和应答，均采用在 HTTP BODY 里面传输 JSON 格式字串的方式。<br>
Header 需要加入内容类型标识：<blockquote><p>Content-Type: application/json</p></blockquote><p><span id=_3-2-1-请求字段></span><h3 id=_3-2-1-请求字段>3.2.1 请求字段</h3><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>是否必填</p><th><p>备注</p><tbody><tr><td><p>app</p><td><p>应用相关配置<br>
Application related configuration</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>appid</p><td><p>应用标识<br>
Application id</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><tr><td><p>token</p><td><p>应用令牌<br>
Application token</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>控制访问权限。</p><tr><td><p>cluster</p><td><p>AUC服务集群<br>
Business cluster</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>指定需要访问的集群。在<a href=https://www.volcengine.com/docs/6561/163043#step2%EF%BC%9A%E5%BC%80%E9%80%9A%E6%9C%8D%E5%8A%A1 rel=noreferrer>控制台创建应用并开通录音文件识别极速版服务</a>后，显示的 <code>Cluster ID</code> 字段。</p><tr><td><p>user</p><td><p>用户相关配置<br>
User related configuration</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>uid</p><td><p>用户标识<br>
User id</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>用于区分同一appid下，不同用户的请求，建议采用 IMEI 或 MAC。</p><tr><td><p>audio</p><td><p>音频相关配置<br>
Audio related configuration</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>url</p><td><p>音频地址<br>
Audio URL</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>需提供可下载的音频文件地址。</p><tr><td><p>format</p><td><p>音频容器格式<br>
Audio format</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>wav / ogg / mp3 / mp4，默认以文件名后缀作为格式。</p><tr><td><p>codec</p><td><p>音频编码格式<br>
Audio codec format</p><td><p>2</p><td><p>string</p><td><td><p>raw / opus，默认为 raw。</p><tr><td><p>rate</p><td><p>音频采样率<br>
Audio sample rate</p><td><p>2</p><td><p>int</p><td><td><p>默认为 16000。</p><tr><td><p>bits</p><td><p>音频采样点位数<br>
Audio bits per sample</p><td><p>2</p><td><p>int</p><td><td><p>默认为 16。</p><tr><td><p>channel</p><td><p>音频声道数<br>
Audio channels</p><td><p>2</p><td><p>int</p><td><td><p>1(mono) / 2(stereo)，默认为1。</p><tr><td><p>request</p><td><p>请求相关配置<br>
Request related configuration</p><td><p>1</p><td><p>dict</p><td><td><tr><td><p>callback</p><td><p>回调地址<br>
call back url</p><td><p>2</p><td><p>string</p><td><td><p>业务方的 http 回调地址，识别结束后服务会用给 POST 方法请求回调地址，body 内容与业务方调用查询接口时服务端返回的格式相同。</p><tr><td><p>boosting_table_name</p><td><p>自学习平台上设置的热词词表名称</p><td><p>2</p><td><p>string</p><td><td><p>热词功能和设置方法可以参考<a href=https://www.volcengine.com/docs/6561/155738 rel=noreferrer>文档</a></p><tr><td><p>additions</p><td><p>额外参数</p><td><p>1</p><td><p>dict</p><td><td><p>额外参数控制字段，dict 类型，里面的 key和value 均为 string 类型。</p><tr><td><p>language</p><td><p>语言</p><td><p>2</p><td><p>string</p><td><td><p>默认为中文，所有语种见 <a href=#_3-2-2-%E6%94%AF%E6%8C%81%E8%AF%AD%E7%A7%8D rel=noreferrer>支持语种</a></p><tr><td><p>use_itn</p><td><p>数字归一化</p><td><p>2</p><td><p>string</p><td><td><p>是否开启数字归一化。"True" 表示开启， "False" 表示关闭。<br>
默认开启。</p><tr><td><p>use_punc</p><td><p>标点</p><td><p>2</p><td><p>string</p><td><td><p>是否添加标点。"True" 表示开启， "False" 表示关闭。<br>
默认开启。</p><tr><td><p>use_ddc</p><td><p>顺滑</p><td><p>2</p><td><p>string</p><td><td><p>是否开启顺滑。"True" 表示开启，"False" 表示关闭。目前只有中文、英文、日语支持顺滑<br>
默认关闭</p><tr><td><p>with_speaker_info</p><td><p>返回说话人信息</p><td><p>2</p><td><p>string</p><td><td><p>"True" 表示返回说话人信息， "False"表示不返回。<br>
默认不返回。</p><tr><td><p>enable_query</p><td><p>使用回调时是否允许主动查询</p><td><p>2</p><td><p>string</p><td><td><p>如果 submit 提交任务时使用回调功能，默认不能调用 query 接口查询结果。设置这个参数允许主动查询<br>
"True": 使用回调时允许主动查询; "False": 使用回调时不允许主动查询。默认 "False"</p><tr><td><p>channel_split</p><td><p>多声道音频是否区分声道</p><td><p>2</p><td><p>string</p><td><td><p>如果设为"True"，则会在返回结果中使用channel_id标记，1为左声道，2为右声道。默认 "False"</p></table><p><span id=_3-2-2-支持语种></span><h3 id=_3-2-2-支持语种>3.2.2 支持语种</h3><table class=volc-viewer-table><thead><tr><th><p>序号</p><th><p>语言</p><th><p>language code</p><tbody><tr><td rowspan=4><p>1</p><td><p>中文普通话(简体)</p><td><p>zh-CN</p><tr><td><p>粤语</p><td><p>cant</p><tr><td><p>四川话</p><td><p>sc</p><tr><td><p>上海话</p><td><p>zh_shanghai</p><tr><td><p>2</p><td><p>英文</p><td><p>en-US</p><tr><td><p>3</p><td><p>日语</p><td><p>ja-JP</p><tr><td><p>4</p><td><p>韩语</p><td><p>ko-KR</p><tr><td><p>5</p><td><p>法语</p><td><p>fr-FR</p><tr><td><p>6</p><td><p>西班牙语</p><td><p>es-MX</p><tr><td><p>7</p><td><p>葡萄牙语</p><td><p>pt-BR</p><tr><td><p>8</p><td><p>印尼语</p><td><p>id-ID</p></table><p>请求示例：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"app"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"token"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"cluster"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"user"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"uid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"388808087185088"</span>
   <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"mp3"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"url"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"http://xxx.com/obj/sample.mp3"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"additions"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"use_itn"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"False"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"with_speaker_info"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"True"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_3-3-应答></span><h2 id=_3-3-应答>3.3 应答</h2><p>应答格式： JSON<br>
应答字段：<table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>是否必填</p><th><p>备注</p><tbody><tr><td><p>resp</p><td><p>返回内容</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>code</p><td><p>状态码<br>
Request Code</p><td><p>2</p><td><p>int</p><td><p>✓</p><td><p>1000 为成功，非 1000 为失败。</p><tr><td><p>message</p><td><p>状态信息<br>
Request message</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>失败时标记失败原因。</p><tr><td><p>id</p><td><p>任务 ID<br>
task id</p><td><p>2</p><td><p>string</p><td><td><p>仅当提交成功时填写。</p></table><p>应答示例：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"resp"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"1000"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"fc5aa03e-6ae4-46a3-b8cf-1910a44e0d8a"</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-查询结果></span><div id=_4-查询结果 class="md-h1 heading-h1">4. 查询结果</div><p><span id=_4-1-域名></span><h2 id=_4-1-域名>4.1 域名</h2><p>火山地址：https://openspeech.bytedance.com/api/v1/auc/query<br><span id=_4-2-请求></span><h2 id=_4-2-请求>4.2 请求</h2><p>请求方式：HTTP POST<br>
请求和应答均采用在 HTTP BODY 里面传输 JSON 格式字串的方式<br>
Header 需要加入内容类型标识：<blockquote><p>Content-Type: application/json</p></blockquote><p>请求示例：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"token"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"cluster"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"fc5aa03e-6ae4-46a3-b8cf-1910a44e0d8a"</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-3-应答></span><h2 id=_4-3-应答>4.3 应答</h2><p>应答格式 ：JSON。如果离线识别还没识别完，只会返回code和message，全部识别完后才会有识别结果的内容。<br>
应答字段：<table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>是否必填</p><th><p>备注</p><tbody><tr><td><p>resp</p><td><p>返回内容</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>id</p><td><p>请求标识<br>
task id</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><tr><td><p>code</p><td><p>请求状态号<br>
task status code</p><td><p>2</p><td><p>int</p><td><p>✓</p><td><tr><td><p>message</p><td><p>请求状态信息<br>
task proceeing message</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><tr><td><p>text</p><td><p>识别结果文本<br>
asr text</p><td><p>2</p><td><p>tring</p><td><td><tr><td><p>utterances</p><td><p>识别结果语音分句信息<br>
utterances info</p><td><p>2</p><td><p>list</p><td><td><p>分句列表。</p><tr><td><p>start_time</p><td><p>起始时间（毫秒）</p><td><p>3</p><td><p>int</p><td><td><p>单个分句开始时间。</p><tr><td><p>end_time</p><td><p>结束时间（毫秒）</p><td><p>3</p><td><p>int</p><td><td><p>单个分句结束时间。</p><tr><td><p>words</p><td><p>词粒度信息</p><td><p>3</p><td><p>dict</p><td><td><p>词列表。</p><tr><td><p>start_time</p><td><p>起始时间（毫秒）</p><td><p>4</p><td><p>int</p><td><td><p>单个词开始时间。</p><tr><td><p>end_time</p><td><p>结束时间（毫秒）</p><td><p>4</p><td><p>int</p><td><td><p>单个词结束时间。</p><tr><td><p>additions</p><td><p>额外信息</p><td><p>3</p><td><p>dict</p><td><td><tr><td><p>speaker</p><td><p>说话人信息</p><td><p>4</p><td><p>string</p><td><td><p>说话人结果，使用数字标识。</p></table><p>应答示例：<br>
(1)返回文本的形式：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"resp"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"fc5aa03e-6ae4-46a3-b8cf-1910a44e0d8a"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1000</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这是字节跳动, 今日头条母公司"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"utterances"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span>
                <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这是字节跳动"</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1500</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3000</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1500</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1700</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"是"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1700</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2000</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"字"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2000</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2200</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"节"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2200</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2600</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"跳"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2600</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2800</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"动"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2800</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3000</span>
                    <span class=hljs-punctuation>}</span>
                <span class=hljs-punctuation>]</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"additions"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
                    <span class=hljs-attr>"speaker"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"1"</span>
                <span class=hljs-punctuation>}</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
                <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"今日头条母公司"</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4000</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>6150</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"今"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4000</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4200</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"日"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4200</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4420</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"头"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4500</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4800</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"条"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>4800</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5000</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"母"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5000</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5200</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"公"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5400</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5800</span>
                    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                    <span class=hljs-punctuation>{</span>
                        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"司"</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5850</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>6150</span>
                    <span class=hljs-punctuation>}</span>
                <span class=hljs-punctuation>]</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"additions"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
                    <span class=hljs-attr>"speaker"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"2"</span>
                <span class=hljs-punctuation>}</span>
            <span class=hljs-punctuation>}</span>
        <span class=hljs-punctuation>]</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_5-错误码说明></span><div id=_5-错误码说明 class="md-h1 heading-h1">5. 错误码说明</div><table class=volc-viewer-table><thead><tr><th><p>错误码</p><th><p>含义</p><th><p>说明</p><tbody><tr><td><p>1000</p><td><p>识别成功</p><td><tr><td><p>1001</p><td><p>请求参数无效</p><td><p>请求参数缺失必需字段 / 字段值无效 / 重复请求。</p><tr><td><p>1002</p><td><p>无访问权限</p><td><p>token 无效 / 过期 / 无权访问指定服务。</p><tr><td><p>1003</p><td><p>访问超频</p><td><p>当前 appid 访问 QPS 超出设定阈值。</p><tr><td><p>1004</p><td><p>访问超额</p><td><p>当前 appid 访问次数超出限制。</p><tr><td><p>1005</p><td><p>服务器繁忙</p><td><p>服务过载，无法处理当前请求。</p><tr><td><p>1006</p><td><p>请求中断</p><td><p>当前请求已失效 / 发生错误。</p><tr><td><p>1007 - 1009</p><td><p>保留号段</p><td><p>待定。</p><tr><td><p>1010</p><td><p>音频过长</p><td><p>音频数据时长超出阈值。</p><tr><td><p>1011</p><td><p>音频过大</p><td><p>音频数据大小超出阈值(暂定单包不超过2M）。</p><tr><td><p>1012</p><td><p>音频格式无效</p><td><p>音频 header 有误 / 无法进行音频解码。</p><tr><td><p>1013</p><td><p>音频静音</p><td><p>音频未识别出任何文本结果。</p><tr><td><p>1014</p><td><p>空音频</p><td><p>下载的音频为空。</p><tr><td><p>1015</p><td><p>下载失败</p><td><p>音频链接下载失败。</p><tr><td><p>1016-1019</p><td><p>保留号段</p><td><p>待定。</p><tr><td><p>1020</p><td><p>识别等待超时</p><td><p>等待就绪超时。</p><tr><td><p>1021</p><td><p>识别处理超时</p><td><p>识别处理过程超时。</p><tr><td><p>1022</p><td><p>识别错误</p><td><p>识别过程中发生错误。</p><tr><td><p>1023 - 1029</p><td><p>保留号段</p><td><p>待定。</p><tr><td><p>1030 - 1098</p><td><p>保留号段</p><td><p>待定。</p><tr><td><p>1099</p><td><p>未知错误</p><td><p>未归类错误。</p><tr><td><p>2000</p><td><p>正在处理</p><td><p>任务处理中。</p><tr><td><p>2001</p><td><p>排队中</p><td><p>任务在等待队里中。</p></table><p><span id=_6-接入-demo></span><div id=_6-接入-demo class="md-h1 heading-h1">6. 接入 demo</div><p>Demo 中需要填写 appid、access_token、access_secret、cluster 字段信息，这些信息可以从<a href=https://www.volcengine.com/docs/6561/163043 rel=noreferrer>控制台创建应用开通服务</a>获得。<pre class=hljs><code class="language-Python volc-pre-code hljs"><span class=hljs-comment>#coding=utf-8</span>
<span class=hljs-keyword>import</span> requests
<span class=hljs-keyword>import</span> json
<span class=hljs-keyword>import</span> time
<span class=hljs-keyword>import</span> os
<span class=hljs-keyword>import</span> uuid

s = requests

appid = <span class=hljs-string>''</span>
token = <span class=hljs-string>''</span>
cluster = <span class=hljs-string>''</span>
audio_url = <span class=hljs-string>''</span>
service_url = <span class=hljs-string>'https://openspeech.bytedance.com/api/v1/auc'</span>

headers = {<span class=hljs-string>'Authorization'</span>: <span class=hljs-string>'Bearer; {}'</span>.<span class=hljs-built_in>format</span>(token)}

<span class=hljs-keyword>def</span> <span class="hljs-title function_">submit_task</span>():
    request = {
        <span class=hljs-string>"app"</span>: {
            <span class=hljs-string>"appid"</span>: appid,
            <span class=hljs-string>"token"</span>: token,
            <span class=hljs-string>"cluster"</span>: cluster
        },
        <span class=hljs-string>"user"</span>: {
            <span class=hljs-string>"uid"</span>: <span class=hljs-string>"388808087185088_demo"</span>
        },
        <span class=hljs-string>"audio"</span>: {
            <span class=hljs-string>"format"</span>: <span class=hljs-string>"wav"</span>,
            <span class=hljs-string>"url"</span>: audio_url
        },
        <span class=hljs-string>"additions"</span>: {
            <span class=hljs-string>'with_speaker_info'</span>: <span class=hljs-string>'False'</span>,
        }
    }

    r = s.post(service_url + <span class=hljs-string>'/submit'</span>, data=json.dumps(request), headers=headers)
    resp_dic = json.loads(r.text)
    <span class=hljs-built_in>print</span>(resp_dic)
    <span class=hljs-built_in>id</span> = resp_dic[<span class=hljs-string>'resp'</span>][<span class=hljs-string>'id'</span>]
    <span class=hljs-built_in>print</span>(<span class=hljs-built_in>id</span>)
    <span class=hljs-keyword>return</span> <span class=hljs-built_in>id</span>


<span class=hljs-keyword>def</span> <span class="hljs-title function_">query_task</span>(<span class=hljs-params>task_id</span>):
    query_dic = {}
    query_dic[<span class=hljs-string>'appid'</span>] = appid
    query_dic[<span class=hljs-string>'token'</span>] = token
    query_dic[<span class=hljs-string>'id'</span>] = task_id
    query_dic[<span class=hljs-string>'cluster'</span>] = cluster
    query_req = json.dumps(query_dic)
    <span class=hljs-built_in>print</span>(query_req)
    r = s.post(service_url + <span class=hljs-string>'/query'</span>, data=query_req, headers=headers)
    <span class=hljs-built_in>print</span>(r.text)
    resp_dic = json.loads(r.text)
    <span class=hljs-keyword>return</span> resp_dic


<span class=hljs-keyword>def</span> <span class="hljs-title function_">file_recognize</span>():
    task_id = submit_task()
    start_time = time.time()
    <span class=hljs-keyword>while</span> <span class=hljs-literal>True</span>:
        time.sleep(<span class=hljs-number>2</span>)
        <span class=hljs-comment># query result</span>
        resp_dic = query_task(task_id)
        <span class=hljs-keyword>if</span> resp_dic[<span class=hljs-string>'resp'</span>][<span class=hljs-string>'code'</span>] == <span class=hljs-number>1000</span>: <span class=hljs-comment># task finished</span>
            <span class=hljs-built_in>print</span>(<span class=hljs-string>"success"</span>)
            exit(<span class=hljs-number>0</span>)
        <span class=hljs-keyword>elif</span> resp_dic[<span class=hljs-string>'resp'</span>][<span class=hljs-string>'code'</span>] &lt; <span class=hljs-number>2000</span>: <span class=hljs-comment># task failed</span>
            <span class=hljs-built_in>print</span>(<span class=hljs-string>"failed"</span>)
            exit(<span class=hljs-number>0</span>)
        now_time = time.time()
        <span class=hljs-keyword>if</span> now_time - start_time &gt; <span class=hljs-number>300</span>: <span class=hljs-comment># wait time exceeds 300s</span>
            <span class=hljs-built_in>print</span>(<span class=hljs-string>'wait time exceeds 300s'</span>)
            exit(<span class=hljs-number>0</span>)


<span class=hljs-keyword>if</span> __name__ == <span class=hljs-string>'__main__'</span>:
    file_recognize()
</code><div class="volc-pre-code-language no-copy">Python</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_7-注意事项></span><h2 id=_7-注意事项>7. 注意事项</h2><ul><li>提交的音频应该小于 512MB，并且时长小于 5 小时<li>半小时内提交的总时长不能超过 500 小时，如果有更大的需求，请联系售前专家<li>转写结果在服务端保存 24 小时，超时后会查询失败</ul></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/80820><div class=head-N5m8>上一篇</div><div class=info-R5lO>录音文件识别标准版</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/108771><div class=head-N5m8>下一篇</div><div class=info-R5lO>流式语音识别SDK开发者使用合规规范</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>