<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/1354869 
 saved date: Mon Jul 21 2025 16:41:45 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>大模型流式语音识别API--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=大模型流式语音识别API--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=大模型流式语音识别API--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><style>img[src="data:,"],source[src="data:,"]{display:none!important}</style><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 sidebarbot-Qz1r"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-13><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>语音识别大模型</span><span class=divider-DURY>/</span><span class=item-Dz0C>大模型流式语音识别API</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>大模型流式语音识别API</div><div class=info-TbRN><span>最近更新时间：2025.07.18 15:24:34</span><span>首次发布时间：2024.10.15 10:59:11</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><p><span id=1d388eb1></span><div id=简介 class="md-h1 heading-h1">简介</div><p>本文档介绍如何通过WebSocket协议实时访问大模型流式语音识别服务 (ASR)，主要包含鉴权相关、协议详情、常见问题和使用Demo四部分。<br>
双向流式模式使用的接口地址是 wss://<a href=http://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream target=_blank rel=noreferrer class=external>openspeech.bytedance.com/api/v3/sauc/bigmodel</a><br>
流式输入模式使用的接口地址是 wss://<a href=http://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream target=_blank rel=noreferrer class=external>openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream</a><br>
两者都是每输入一个包返回一个包，双向流式模式会尽快返回识别到的字符，速度较快。<br>
流式输入模式会在输入音频大于15s或发送最后一包（负包）后返回识别到的结果，准确率更高。<br>
无论是哪种模式，单包音频建议在100~200ms大小左右，不能过大或者过小，否则均会影响性能。<br>
（注：针对双向流式模式，单包为200ms大小时性能最优，建议双向流式模式选取200ms大小的分包）<br>
流式输入模式在平均音频时长5s时，可以做到300~400ms以内返回。<hr><p>双向流式模式（优化版本）接口地址：wss://<a href=http://openspeech.bytedance.com/api/v3/sauc/bigmodel_async target=_blank rel=noreferrer class=external>openspeech.bytedance.com/api/v3/sauc/bigmodel_async</a><br>
该模式下，不再是每一包输入对应一包返回，只有当结果有变化时才会返回新的数据包<br>
（性能优化 rtf 和首字、尾字时延均有一定程度提升）<br>
优化版本中，websocket 建连成功后会返回一个 带 event 事件的 full server response，中间包含event=150（正常）、153（失败），链路请求方式与双向流式非优化版存在一定区别，具体可参考文档最下方demo。<br>
双向流式版本，更推荐使用双向流式模式（优化版本），性能相对更优。<br><span id=25d1d6d6></span><div id=鉴权 class="md-h1 heading-h1">鉴权</div><p>在 websocket 建连的 HTTP 请求头（Header 中）添加以下信息<table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><td><td><tr><td><p>X-Api-App-Key</p><td><p>使用火山引擎控制台获取的APP ID，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a></p><td><p>123456789</p><tr><td><p>X-Api-Access-Key</p><td><p>使用火山引擎控制台获取的Access Token，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a></p><td><p>your-access-key</p><tr><td rowspan=3><p>X-Api-Resource-Id</p><td rowspan=3><p>表示调用服务的资源信息 ID，是固定值<ul><li>大模型流式语音识别<li>大模型流式语音识别</ul><td rowspan=3><p>小时版：volc.bigasr.sauc.duration<br>
并发版：volc.bigasr.sauc.concurrent</p><tr><tr><tr><td><p>X-Api-Connect-Id</p><td><p>用于追踪当前连接的标志 ID，推荐设置UUID等</p><td><p>67ee89ba-7050-4c04-a3d7-ac61a63499b3</p></table><p>websocket 握手成功后，会返回这些 Response header。强烈建议记录X-Tt-Logid（logid）作为排错线索。<table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Api-Connect-Id</p><td><p>用于追踪当前调用信息的标志 ID，推荐用UUID等</p><td><p>67ee89ba-7050-4c04-a3d7-ac61a63499b3</p><tr><td><p>X-Tt-Logid</p><td><p>服务端返回的 logid，建议用户获取和打印方便定位问题</p><td><p>202407261553070FACFE6D19421815D605</p></table><pre class=hljs><code class="language-HTTP volc-pre-code">// 建连 HTTP 请求头示例
GET /api/v3/sauc/bigmodel
Host: openspeech.bytedance.com
X-Api-App-Key: 123456789
X-Api-Access-Key: your-access-key
X-Api-Resource-Id: volc.bigasr.sauc.duration
X-Api-Connect-Id: 随机生成的UUID

## 返回 Header
X-Tt-Logid: 202407261553070FACFE6D19421815D605
</code><div class="volc-pre-code-language no-copy">HTTP</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=ca5745cc></span><div id=协议详情 class="md-h1 heading-h1">协议详情</div><p><span id=3672cb1f></span><h2 id=交互流程>交互流程</h2><p><img alt=Image class=volc-image-img src=data:, width=757><br class=sf-hidden><span id=db13e485></span><h2 id=websocket-二进制协议>WebSocket 二进制协议</h2><p>WebSocket 使用二进制协议传输数据。协议的组成由至少 4 个字节的可变 header、payload size 和 payload 三部分组成，其中 header 描述消息类型、序列化方式以及压缩格式等信息，payload size 是 payload 的长度，payload 是具体负载内容，依据消息类型不同 payload 内容不同。<br>
需注意：协议中整数类型的字段都使用<strong>大端</strong>表示。<br><span id=df933e14></span><h3 id=header-数据格式>header 数据格式</h3><table class=volc-viewer-table><thead><tr><th><p><strong>Byte \ Bit</strong></p><th><p><strong>7</strong></p><th><p><strong>6</strong></p><th><p><strong>5</strong></p><th><p><strong>4</strong></p><th><p><strong>3</strong></p><th><p><strong>2</strong></p><th><p><strong>1</strong></p><th><p><strong>0</strong></p><tbody><tr><td><p><strong>0</strong></p><td colspan=4><p>Protocol version</p><td colspan=4><p>Header size</p><tr><td><p><strong>1</strong></p><td colspan=4><p>Message type</p><td colspan=4><p>Message type specific flags</p><tr><td><p><strong>2</strong></p><td colspan=4><p>Message serialization method</p><td colspan=4><p>Message compression</p><tr><td><p><strong>3</strong></p><td colspan=8><p>Reserved</p><tr><td><p><strong>4</strong></p><td colspan=8><p>[Optional header extensions]</p><tr><td><p><strong>5</strong></p><td colspan=8><p>[Payload, depending on the Message Type]</p><tr><td><p><strong>6</strong></p><td colspan=8><p>...</p></table><p><span id=996c63e9></span><h3 id=header-字段描述>header 字段描述</h3><table class=volc-viewer-table><thead><tr><th><p>字段 (size in bits)</p><th><p>说明</p><th><p>值</p><tbody><tr><td><p>Protocol version (4)</p><td><p>将来可能会决定使用不同的协议版本，因此此字段是为了使客户端和服务器在版本上达成共识。</p><td><p>0b0001 - version 1 (目前只有该版本)</p><tr><td><p>Header (4)</p><td><p>Header 大小。实际 header 大小（以字节为单位）是 header size value x 4 。</p><td><p>0b0001 - header size = 4 (1 x 4)</p><tr><td><p>Message type (4)</p><td><p>消息类型。</p><td><p>0b0001 - 端上发送包含请求参数的 full client request<br>
0b0010 - 端上发送包含音频数据的 audio only request<br>
0b1001 - 服务端下发包含识别结果的 full server response<br>
0b1111 - 服务端处理错误时下发的消息类型（如无效的消息格式，不支持的序列化方法等）</p><tr><td><p>Message type specific flags (4)</p><td><p>Message type 的补充信息。</p><td><p>0b0000 - header后4个字节不为sequence number<br>
0b0001 - header后4个字节为sequence number且为正<br>
0b0010 - header后4个字节不为sequence number，仅指示此为最后一包（负包）<br>
0b0011 - header后4个字节为sequence number且需要为负数（最后一包/负包）<br>
0b0100 - header后4个字节为 event</p><tr><td><p>Message serialization method (4)</p><td><p>full client request 的 payload 序列化方法；<br>
服务器将使用与客户端相同的序列化方法。</p><td><p>0b0000 - 无序列化<br>
0b0001 - JSON 格式</p><tr><td><p>Message Compression (4)</p><td><p>定义 payload 的压缩方法；<br>
服务端将使用客户端的压缩方法。</p><td><p>0b0000 - no compression<br>
0b0001 - Gzip 压缩</p><tr><td><p>Reserved (8)</p><td><p>保留以供将来使用，还用作填充（使整个标头总计4个字节）。</p><td></table><p><span id=231d2daf></span><h2 id=请求流程>请求流程</h2><p><span id=921764de></span><h3 id=建立连接>建立连接</h3><p>根据 WebSocket 协议本身的机制，client 会发送 HTTP GET 请求和 server 建立连接做协议升级。<br>
需要在其中根据身份认证协议加入鉴权签名头。设置方法请参考鉴权。<br><span id=f8167db8></span><h3 id=发送-full-client-request>发送 full client request</h3><p>WebSocket 建立连接后，发送的第一个请求是 full client request。格式是：<table class=volc-viewer-table><thead><tr><th><p><strong>31 ... 24</strong></p><th><p><strong>23 ... 16</strong></p><th><p><strong>15 ... 8</strong></p><th><p><strong>7 ... 0</strong></p><tbody><tr><td colspan=4><p>Header</p><tr><td colspan=4><p>Payload size (4B, unsigned int32)</p><tr><td colspan=4><p>Payload</p></table><p>Header： 前文描述的 4 字节头。<br>
Payload size： 是按 Header 中指定压缩方式压缩 payload 后的长度，使用<strong>大端</strong>表示。<br>
Payload： 包含音频的元数据以及 server 所需的相关参数，一般是 JSON 格式。具体的参数字段见下表：<table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>是否必填</p><th><p>备注</p><tbody><tr><td><p>user</p><td><p>用户相关配置</p><td><p>1</p><td><p>dict</p><td><td><p>提供后可供服务端过滤日志</p><tr><td><p>uid</p><td><p>用户标识</p><td><p>2</p><td><p>string</p><td><td><p>建议采用 IMEI 或 MAC。</p><tr><td><p>did</p><td><p>设备名称</p><td><p>2</p><td><p>string</p><td><td><tr><td><p>platform</p><td><p>操作系统及API版本号</p><td><p>2</p><td><p>string</p><td><td><p>iOS/Android/Linux</p><tr><td><p>sdk_version</p><td><p>sdk版本</p><td><p>2</p><td><p>string</p><td><td><tr><td><p>app_version</p><td><p>app 版本</p><td><p>2</p><td><p>string</p><td><td><tr><td><p>audio</p><td><p>音频相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>format</p><td><p>音频容器格式</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>pcm(pcm_s16le) / wav(pcm_s16le) / ogg</p><tr><td><p>codec</p><td><p>音频编码格式</p><td><p>2</p><td><p>string</p><td><td><p>raw / opus，默认为 raw(pcm) 。</p><tr><td><p>rate</p><td><p>音频采样率</p><td><p>2</p><td><p>int</p><td><td><p>默认为 16000，目前只支持16000</p><tr><td><p>bits</p><td><p>音频采样点位数</p><td><p>2</p><td><p>int</p><td><td><p>默认为 16。</p><tr><td><p>channel</p><td><p>音频声道数</p><td><p>2</p><td><p>int</p><td><td><p>1(mono) / 2(stereo)，默认为1。</p><tr><td><p>request</p><td><p>请求相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>model_name</p><td><p>模型名称</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>目前只有bigmodel</p><tr><td><p>enable_itn</p><td><p>启用itn</p><td><p>2</p><td><p>bool</p><td><td><p>默认为true。<br>
文本规范化 (ITN) 是自动语音识别 (ASR) 后处理管道的一部分。 ITN 的任务是将 ASR 模型的原始语音输出转换为书面形式，以提高文本的可读性。<br>
例如，“一九七零年”-&gt;“1970年”和“一百二十三美元”-&gt;“$123”。</p><tr><td><p>enable_punc</p><td><p>启用标点</p><td><p>2</p><td><p>bool</p><td><td><p>默认为true。</p><tr><td><p>enable_ddc</p><td><p>启用顺滑</p><td><p>2</p><td><p>bool</p><td><td><p>默认为false。<br>
**<ins>语义顺滑</ins>**‌是一种技术，旨在提高自动语音识别（ASR）结果的文本可读性和流畅性。这项技术通过删除或修改ASR结果中的不流畅部分，如停顿词、语气词、语义重复词等，使得文本更加易于阅读和理解。</p><tr><td><p>show_utterances</p><td><p>输出语音停顿、分句、分词信息</p><td><p>2</p><td><p>bool</p><td><td><tr><td><p>result_type</p><td><p>结果返回方式</p><td><p>2</p><td><p>string</p><td><td><p>默认为"full",全量返回。<br>
设置为"single"则为增量结果返回，即不返回之前分句的结果。</p><tr><td><p>vad_segment_duration</p><td><p>语义切句的最大静音阈值</p><td><p>2</p><td><p>int</p><td><td><p>单位ms，默认为3000。当静音时间超过该值时，会将文本分为两个句子。不决定判停，所以不会修改definite出现的位置。在end_window_size配置后，该参数失效。</p><tr><td><p>end_window_size</p><td><p>强制判停时间</p><td><p>2</p><td><p>int</p><td><td><p>单位ms，默认为800，最小200。静音时长超过该值，会直接判停，输出definite。配置该值，不使用语义分句，根据静音时长来分句。用于实时性要求较高场景，可以提前获得definite句子</p><tr><td><p>force_to_speech_time</p><td><p>强制语音时间</p><td><p>2</p><td><p>int</p><td><td><p>单位ms，默认为10000，最小1。音频时长超过该值之后，才会判停，根据静音时长输出definite，需配合end_window_size使用。<br>
用于解决短音频+实时性要求较高场景，不配置该参数，只使用end_window_size时，前10s不会判停。推荐设置1000，可能会影响识别准确率。</p><tr><td><p>sensitive_words_filter</p><td><p>敏感词过滤</p><td><p>2</p><td><p>string</p><td><td><p>敏感词过滤功能,支持开启或关闭,支持自定义敏感词。该参数可实现：不处理(默认,即展示原文)、过滤、替换为*。<br>
示例：<br>
system_reserved_filter //是否使用系统敏感词，会替换成*(默认系统敏感词主要包含一些限制级词汇）<br>
filter_with_empty // 想要替换成空的敏感词<br>
filter_with_signed // 想要替换成 * 的敏感词<pre class=hljs><code class="language-Python volc-pre-code hljs"><span class=hljs-string>"sensitive_words_filter"</span>:{\<span class=hljs-string>"system_reserved_filter\":true,\"filter_with_empty\":[\"敏感词\"],\"filter_with_signed\":[\"敏感词\"]}"</span>,
</code><div class="volc-pre-code-language no-copy">Python</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><tr><td><p>corpus</p><td><p>语料/干预词等</p><td><p>2</p><td><p>dict</p><td><td><tr><td><p>boosting_table_name</p><td><p>自学习平台上设置的热词词表名称</p><td><p>3</p><td><p>string</p><td><td><p>热词表功能和设置方法可以参考<a href=https://www.volcengine.com/docs/6561/155739 target=_blank rel=noreferrer class=external>文档</a></p><tr><td><p>boosting_table_id</p><td><p>自学习平台上设置的热词词表id</p><td><p>3</p><td><p>string</p><td><td><p>热词表功能和设置方法可以参考<a href=https://www.volcengine.com/docs/6561/155739 target=_blank rel=noreferrer class=external>文档</a></p><tr><td><p>correct_table_name</p><td><p>自学习平台上设置的替换词词表名称</p><td><p>3</p><td><p>string</p><td><td><p>替换词功能和设置方法可以参考<a href=https://www.volcengine.com/docs/6561/1206007 target=_blank rel=noreferrer class=external>文档</a></p><tr><td><p>correct_table_id</p><td><p>自学习平台上设置的替换词词表id</p><td><p>3</p><td><p>string</p><td><td><p>替换词功能和设置方法可以参考<a href=https://www.volcengine.com/docs/6561/1206007 target=_blank rel=noreferrer class=external>文档</a></p><tr><td><p>context</p><td><p>热词或者上下文</p><td><p>3</p><td><p>string</p><td><td><ol><li>热词直传（优先级高于传热词表，不建议同时设置，会有冲突），双向流式支持100tokens，流式输入nostream支持200 tokens</ol><p>"context":"{"hotwords":[{"word":"热词1号"}, {"word":"热词2号"}]}"<ol start=2><li>上下文，限制800 tokens及20轮（含）内，超出会按照时间顺序从新到旧截断，优先保留更新的对话</ol><p>context_data字段按照从新到旧的顺序排列，传入需要序列化为jsonstring（转义引号）<pre class=hljs><code class="language-SQL volc-pre-code hljs">{
    \"context_type\": \"dialog_ctx\",
    \"context_data\":[
        {\"text\": \"text1\"},
        {\"text\": \"text2\"},
        {\"text\": \"text3\"},
        {\"text\": \"text4\"},
        ...
    ]
}
</code><div class="volc-pre-code-language no-copy">SQL</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre></table><p>参数示例：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"user"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"uid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"388808088185088"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"wav"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>16000</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"bits"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>16</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"channel"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"language"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"zh-CN"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"request"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"model_name"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bigmodel"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"enable_itn"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>false</span></span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"enable_ddc"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>false</span></span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"enable_punc"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>false</span></span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"corpus"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
            <span class=hljs-attr>"boosting_table_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"通过自学习平台配置热词的词表id"</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-attr>"context"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
                \<span class=hljs-string>"context_type\": \"dialog_ctx\",
                \"context_data\":[
                    {\"text\": \"text1\"},
                    {\"text\": \"text2\"},
                    {\"text\": \"text3\"},
                    {\"text\": \"text4\"},
                    ...
                ]
            }
        }
    }
}
</span></code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=eaf63ef1></span><h3 id=发送-audio-only-request>发送 audio only request</h3><p>Client 发送 full client request 后，再发送包含音频数据的 audio-only client request。音频应采用 full client request 中指定的格式（音频格式、编解码器、采样率、声道）。格式如下：<table class=volc-viewer-table><thead><tr><th><p><strong>31 ... 24</strong></p><th><p><strong>23 ... 16</strong></p><th><p><strong>15 ... 8</strong></p><th><p><strong>7 ... 0</strong></p><tbody><tr><td colspan=4><p>Header</p><tr><td colspan=4><p>Payload size (4B, unsigned int32)</p><tr><td colspan=4><p>Payload</p></table><p>Payload 是使用指定压缩方法，压缩音频数据后的内容。可以多次发送 audio only request 请求，例如在流式语音识别中如果每次发送 100ms 的音频数据，那么 audio only request 中的 Payload 就是 100ms 的音频数据。<br><span id=096d0921></span><h3 id=full-server-response>full server response</h3><p>Client 发送的 full client request 和 audio only request，服务端都会返回 full server response。格式如下：<table class=volc-viewer-table><thead><tr><th><p><strong>31 ... 24</strong></p><th><p><strong>23 ... 16</strong></p><th><p><strong>15 ... 8</strong></p><th><p><strong>7 ... 0</strong></p><tbody><tr><td colspan=4><p>Header</p><tr><td colspan=4><p>Sequence</p><tr><td colspan=4><p>Payload size (4B, unsigned int32)</p><tr><td colspan=4><p>Payload</p></table><p>Payload 内容是包含识别结果的 JSON 格式，字段说明如下：<table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>是否必填</p><th><p>备注</p><tbody><tr><td><p>result</p><td><p>识别结果</p><td><p>1</p><td><p>list</p><td><td><p>仅当识别成功时填写</p><tr><td><p>text</p><td><p>整个音频的识别结果文本</p><td><p>2</p><td><p>string</p><td><td><p>仅当识别成功时填写。</p><tr><td><p>utterances</p><td><p>识别结果语音分句信息</p><td><p>2</p><td><p>list</p><td><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>text</p><td><p>utterance级的文本内容</p><td><p>3</p><td><p>string</p><td><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>start_time</p><td><p>起始时间（毫秒）</p><td><p>3</p><td><p>int</p><td><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>end_time</p><td><p>结束时间（毫秒）</p><td><p>3</p><td><p>int</p><td><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>definite</p><td><p>是否是一个确定分句</p><td><p>3</p><td><p>bool</p><td><td><p>仅当识别成功且开启show_utterances时填写。</p></table><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
  <span class=hljs-attr>"audio_info"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span><span class=hljs-attr>"duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>10000</span><span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"result"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
      <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这是字节跳动， 今日头条母公司。"</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"utterances"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
        <span class=hljs-punctuation>{</span>
          <span class=hljs-attr>"definite"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1705</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这是字节跳动，"</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>860</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>740</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1020</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>860</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"是"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1200</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1020</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"字"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1400</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1200</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"节"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1560</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1400</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"跳"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1640</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1560</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"动"</span>
            <span class=hljs-punctuation>}</span>
          <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
        <span class=hljs-punctuation>{</span>
          <span class=hljs-attr>"definite"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2110</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"今日头条母公司。"</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3070</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2910</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"今"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3230</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3070</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"日"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3390</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3230</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"头"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3550</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3390</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"条"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3670</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3550</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"母"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3670</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"公"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"司"</span>
            <span class=hljs-punctuation>}</span>
          <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span>
      <span class=hljs-punctuation>]</span>
   <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"audio_info"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span>
  <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=8aa108f1></span><h3 id=error-message-from-server>Error message from server</h3><p>当 server 发现无法解决的二进制/传输协议问题时，将发送 Error message from server 消息（例如，client 以 server 不支持的序列化格式发送消息）。格式如下：<table class=volc-viewer-table><thead><tr><th><p><strong>31 ... 24</strong></p><th><p><strong>23 ... 16</strong></p><th><p><strong>15 ... 8</strong></p><th><p><strong>7 ... 0</strong></p><tbody><tr><td colspan=4><p>Header</p><tr><td colspan=4><p>Error message code (4B, unsigned int32)</p><tr><td colspan=4><p>Error message size (4B, unsigned int32)</p><tr><td colspan=4><p>Error message (UTF8 string)</p></table><p>Header： 前文描述的 4 字节头。<br>
Error message code： 错误码，使用<strong>大端</strong>表示。<br>
Error message size： 错误信息长度，使用<strong>大端</strong>表示。<br>
Error message： 错误信息。<br><span id=4665ea66></span><h3 id=示例>示例</h3><p><span id=87bf74a6></span><h4>示例：客户发送 3 个请求</h4><p>下面的 message flow 会发送多次消息，每个消息都带有版本、header 大小、保留数据。由于每次消息中这些字段值相同，所以有些消息中这些字段省略了。<br>
Message flow:<br>
client 发送 "Full client request"<p>version: <code>b0001</code> (4 bits)<br>
header size: <code>b0001</code> (4 bits)<br>
message type: <code>b0001</code> (Full client request) (4bits)<br>
message type specific flags: <code>b0000</code> (use_specific_pos_sequence) (4bits)<br>
message serialization method: <code>b0001</code> (JSON) (4 bits)<br>
message compression: <code>b0001</code> (Gzip) (4bits)<br>
reserved data: <code>0x00</code> (1 byte)<br>
payload size = Gzip 压缩后的长度<br>
payload: json 格式的请求字段经过 Gzip 压缩后的数据<p>server 响应 "Full server response"<p>version: <code>b0001</code><br>
header size: <code>b0001</code><br>
message type: <code>b1001</code> (Full server response)<br>
message type specific flags: <code>b0001</code> (none)<br>
message serialization method: <code>b0001</code> (JSON 和请求相同)<br>
message compression: <code>b0001</code> (Gzip 和请求相同)<br>
reserved data: <code>0x00</code><br>
sequence: 0x00 0x00 0x00 0x01 (4 byte) sequence=1<br>
payload size = Gzip 压缩后数据的长度<br>
payload: Gzip 压缩后的响应数据<p>client 发送包含第一包音频数据的 "Audio only client request"<p>version: <code>b0001</code><br>
header size: <code>b0001</code><br>
message type: <code>b0010</code> (audio only client request)<br>
message type specific flags: <code>b0000</code> (用户设置正数 sequence number)<br>
message serialization method: <code>b0000</code> (none - raw bytes)<br>
message compression: <code>b0001</code> (Gzip)<br>
reserved data: <code>0x00</code><br>
payload size = Gzip 压缩后的音频长度<br>
payload: 音频数据经过 Gzip 压缩后的数据<p>server 响应 "Full server response"<p>message type: <code>0b1001</code> - Full server response<br>
message specific flags: <code>0b0001</code> (none)<br>
message serialization: <code>0b0001</code> (JSON, 和请求相同)<br>
message compression <code>0b0001</code> (Gzip, 和请求相同)<br>
reserved data: <code>0x00</code><br>
sequence data: 0x00 0x00 0x00 0x02 (4 byte) sequence=2<br>
payload size = Gzip 压缩后数据的长度<br>
payload: Gzip 压缩后的响应数据<p>client 发送包含最后一包音频数据（通过 message type specific flags) 的 "Audio-only client request"，<p>message type: <code>b0010</code> (audio only client request)<br>
message type specific flags: <strong><code>b0010</code></strong> (最后一包音频请求)<br>
message serialization method: <code>b0000</code> (none - raw bytes)<br>
message compression: <code>b0001</code> (Gzip)<br>
reserved data: <code>0x00</code><br>
payload size = Gzip 压缩后的音频长度<br>
payload: Gzip 压缩后的音频数据<p>server 响应 "Full server response" - 最终回应及处理结果<p>message type: <code>b1001</code> (Full server response)<br>
message type specific flags: <code>b0011</code> (最后一包音频结果)<br>
message serialization method: <code>b0001</code> (JSON)<br>
message compression: <code>b0001</code> (Gzip)<br>
reserved data: <code>0x00</code><br>
sequence data: <code>0x00 0x00 0x00 0x03</code> (4byte) sequence=3<br>
payload size = Gzip 压缩后的 JSON 长度<br>
payload: Gzip 压缩后的 JSON 数据<p>如处理过程中出现错误信息，可能有以下错误帧的返回<p>message type: <code>b1111</code> (error response)<br>
message type specific flags: <code>b0000</code> (none)<br>
message serialization method: <code>b0001</code> (JSON)<br>
message compression: <code>b0000</code> (none)<br>
reserved data: <code>0x00</code><br>
Error code data: <code>0x2A 0x0D 0x0A2 0xff</code> (4byte) 错误码<br>
payload size = 错误信息对象的 JSON 长度<br>
payload: 错误信息对象的 JSON 数据<p><span id=989f9570></span><h2 id=错误码>错误码</h2><table class=volc-viewer-table><thead><tr><th><p>错误码</p><th><p>含义</p><th><p>说明</p><tbody><tr><td><p>20000000</p><td><p>成功</p><td><tr><td><p>45000001</p><td><p>请求参数无效</p><td><p>请求参数缺失必需字段 / 字段值无效 / 重复请求。</p><tr><td><p>45000002</p><td><p>空音频</p><td><tr><td><p>45000081</p><td><p>等包超时</p><td><tr><td><p>45000151</p><td><p>音频格式不正确</p><td><tr><td><p>550xxxxx</p><td><p>服务内部处理错误</p><td><tr><td><p>55000031</p><td><p>服务器繁忙</p><td><p>服务过载，无法处理当前请求。</p></table><p><span id=4468a455></span><div id=demo class="md-h1 heading-h1">Demo</div><p>Python：<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>sauc_python.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><br>
Go：<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>sauc_go.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><br>
Java：<p><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>java_client.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/1354871><div class=head-N5m8>上一篇</div><div class=info-R5lO>产品简介</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/1354868><div class=head-N5m8>下一篇</div><div class=info-R5lO>大模型录音文件识别API</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>