# 火山引擎声音复刻模型

## 模型概述

**模型ID**: `volcengine-voice-clone`  
**模型类型**: 声音复刻 (Voice Cloning)  
**接口类型**: HTTP REST API  
**适用场景**: 个性化语音合成，声音定制  

## 复刻版本对比

### 复刻1.0（传统版本）
- **model_type**: 0
- **特点**: 基础声音复刻
- **适用**: 一般场景

### 复刻2.0（ICL版本）
- **model_type**: 1
- **特点**: 增强效果，更自然
- **适用**: 高质量要求场景

### DiT标准版
- **model_type**: 2
- **特点**: 音色复刻，不还原用户风格
- **支持语种**: 中英日西印葡德法8种语言

### DiT还原版
- **model_type**: 3
- **特点**: 音色+风格完整复刻（口音、语速等）
- **支持语种**: 中英2种语言

## API接口规范

### 音频上传训练接口
```
POST https://openspeech.bytedance.com/api/v1/mega_tts/audio/upload
```

### 训练状态查询接口
```
POST https://openspeech.bytedance.com/api/v1/mega_tts/status
```

### 认证方式
```http
Authorization: Bearer; {access_token}
Resource-Id: volc.megatts.voiceclone
Content-Type: application/json
```

## 音频上传训练

### 请求参数
```json
{
  "appid": "your_app_id",
  "speaker_id": "S_unique_speaker_id",
  "audios": [{
    "audio_bytes": "base64_encoded_audio",
    "audio_format": "wav",
    "text": "参考文本内容"
  }],
  "source": 2,
  "language": 0,
  "model_type": 1
}
```

### 参数说明
- **appid**: 应用ID
- **speaker_id**: 唯一音色代号（从控制台获取）
- **audios**: 音频数据数组
  - **audio_bytes**: base64编码的音频数据
  - **audio_format**: 音频格式（wav/mp3/ogg/m4a/aac/pcm）
  - **text**: 可选的参考文本，用于对比音频差异
- **source**: 固定值2
- **language**: 语种代码（0=中文，1=英文，2=日语等）
- **model_type**: 复刻版本（0/1/2/3）

### 音频要求
- **格式支持**: wav、mp3、ogg、m4a、aac、pcm
- **文件大小**: 单文件最大10MB
- **上传数量**: 每次最多1个音频文件
- **音频质量**: 建议清晰、无噪音、单人说话

### 语种支持

#### model_type=0/1时
- cn = 0 (中文，默认)
- en = 1 (英文)
- ja = 2 (日语)
- es = 3 (西班牙语)
- id = 4 (印尼语)
- pt = 5 (葡萄牙语)

#### model_type=2时
- cn = 0 (中文，默认)
- en = 1 (英文)
- ja = 2 (日语)
- es = 3 (西班牙语)
- id = 4 (印尼语)
- pt = 5 (葡萄牙语)
- de = 6 (德语)
- fr = 7 (法语)

#### model_type=3时
- cn = 0 (中文，默认)
- en = 1 (英文)

### 上传响应
```json
{
  "BaseResp": {
    "StatusCode": 0,
    "StatusMessage": ""
  },
  "speaker_id": "S_unique_speaker_id"
}
```

## 训练状态查询

### 查询请求
```json
{
  "appid": "your_app_id",
  "speaker_id": "S_unique_speaker_id"
}
```

### 查询响应
```json
{
  "BaseResp": {
    "StatusCode": 0,
    "StatusMessage": ""
  },
  "speaker_id": "S_unique_speaker_id",
  "status": 2,
  "create_time": 1701055304000,
  "version": "V1",
  "demo_audio": "http://demo_audio_url.wav"
}
```

### 状态说明
- **0 (NotFound)**: 未找到
- **1 (Training)**: 训练中
- **2 (Success)**: 训练成功
- **3 (Failed)**: 训练失败
- **4 (Active)**: 激活状态

**注意**: 状态为2或4时都可以调用TTS合成

## 错误码说明

### 上传错误码
- **0**: 成功
- **1001**: 请求参数有误
- **1101**: 音频上传失败
- **1102**: ASR转写失败
- **1103**: SID声纹检测失败
- **1104**: 声纹检测未通过，与名人相似度过高
- **1105**: 获取音频数据失败
- **1106**: SpeakerID重复
- **1107**: SpeakerID未找到
- **1108**: 音频转码失败
- **1109**: WER检测错误，音频与文本对比字错率过高
- **1111**: AED检测错误，音频不包含说话声
- **1112**: SNR检测错误，信噪比过高
- **1113**: 降噪处理失败
- **1114**: 音频质量低，降噪失败
- **1122**: 未检测到人声
- **1123**: 已达上传次数限制（同一音色支持10次上传）

## 语音合成使用

### 合成接口
训练完成后，使用标准TTS接口进行合成：

```json
{
  "app": {
    "appid": "your_app_id",
    "token": "access_token",
    "cluster": "volcano_icl"
  },
  "user": {
    "uid": "user_id"
  },
  "audio": {
    "voice_type": "S_unique_speaker_id",
    "encoding": "mp3",
    "rate": 24000,
    "speed_ratio": 1.0
  },
  "request": {
    "reqid": "unique_request_id",
    "text": "要合成的文本",
    "operation": "query"
  }
}
```

### 重要配置变更
使用复刻音色时需要更换cluster：
- **字符版**: `volcano_mega` → `volcano_icl`
- **并发版**: `volcano_mega_concurr` → `volcano_icl_concurr`

## 集成示例

### JavaScript/TypeScript
```typescript
interface VoiceCloneRequest {
  appid: string;
  speaker_id: string;
  audio_file: File;
  language: number;
  model_type: number;
  reference_text?: string;
}

class VolcengineVoiceClone {
  private apiKey: string;
  private baseURL = 'https://openspeech.bytedance.com';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async uploadAudio(request: VoiceCloneRequest): Promise<string> {
    // 将文件转换为base64
    const audioBytes = await this.fileToBase64(request.audio_file);
    
    const payload = {
      appid: request.appid,
      speaker_id: request.speaker_id,
      audios: [{
        audio_bytes: audioBytes,
        audio_format: this.getFileExtension(request.audio_file.name),
        text: request.reference_text
      }],
      source: 2,
      language: request.language,
      model_type: request.model_type
    };

    const response = await fetch(`${this.baseURL}/api/v1/mega_tts/audio/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.megatts.voiceclone',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();
    if (result.BaseResp.StatusCode === 0) {
      return result.speaker_id;
    }
    throw new Error(result.BaseResp.StatusMessage);
  }

  async checkStatus(appid: string, speakerId: string): Promise<any> {
    const payload = {
      appid: appid,
      speaker_id: speakerId
    };

    const response = await fetch(`${this.baseURL}/api/v1/mega_tts/status`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.megatts.voiceclone',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    return await response.json();
  }

  async waitForTraining(appid: string, speakerId: string, maxWaitTime = 3600000): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.checkStatus(appid, speakerId);
      
      if (status.status === 2 || status.status === 4) {
        return status; // 训练成功
      } else if (status.status === 3) {
        throw new Error('Training failed');
      }
      
      // 等待30秒后重试
      await new Promise(resolve => setTimeout(resolve, 30000));
    }
    
    throw new Error('Training timeout');
  }

  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = (reader.result as string).split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || 'wav';
  }
}
```

### Python
```python
import requests
import base64
import time
from typing import Optional

class VolcengineVoiceClone:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://openspeech.bytedance.com"
    
    def upload_audio(self, appid: str, speaker_id: str, audio_path: str, 
                    language: int = 0, model_type: int = 1, 
                    reference_text: Optional[str] = None) -> str:
        # 读取音频文件并编码
        with open(audio_path, 'rb') as f:
            audio_data = f.read()
        audio_bytes = base64.b64encode(audio_data).decode('utf-8')
        
        # 获取文件格式
        audio_format = audio_path.split('.')[-1].lower()
        
        payload = {
            "appid": appid,
            "speaker_id": speaker_id,
            "audios": [{
                "audio_bytes": audio_bytes,
                "audio_format": audio_format,
                "text": reference_text
            }],
            "source": 2,
            "language": language,
            "model_type": model_type
        }
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.megatts.voiceclone",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{self.base_url}/api/v1/mega_tts/audio/upload",
            json=payload,
            headers=headers
        )
        
        result = response.json()
        if result["BaseResp"]["StatusCode"] == 0:
            return result["speaker_id"]
        else:
            raise Exception(result["BaseResp"]["StatusMessage"])
    
    def check_status(self, appid: str, speaker_id: str) -> dict:
        payload = {
            "appid": appid,
            "speaker_id": speaker_id
        }
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.megatts.voiceclone",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{self.base_url}/api/v1/mega_tts/status",
            json=payload,
            headers=headers
        )
        
        return response.json()
    
    def wait_for_training(self, appid: str, speaker_id: str, max_wait_time: int = 3600) -> dict:
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status = self.check_status(appid, speaker_id)
            
            if status["status"] in [2, 4]:  # 训练成功
                return status
            elif status["status"] == 3:  # 训练失败
                raise Exception("Training failed")
            
            time.sleep(30)  # 等待30秒
        
        raise TimeoutError("Training timeout")
```

## Cloudflare Pages集成

### API路由实现
```typescript
// app/api/volcengine/voice-clone/upload/route.ts
export async function POST(request: Request) {
  const formData = await request.formData();
  const audioFile = formData.get('audio') as File;
  const speakerId = formData.get('speaker_id') as string;
  const modelType = parseInt(formData.get('model_type') as string) || 1;
  
  const voiceClone = new VolcengineVoiceClone(
    process.env.VOLCENGINE_ACCESS_TOKEN!
  );
  
  const result = await voiceClone.uploadAudio({
    appid: process.env.VOLCENGINE_APP_ID!,
    speaker_id: speakerId,
    audio_file: audioFile,
    language: 0, // 中文
    model_type: modelType
  });
  
  return Response.json({ speaker_id: result });
}

// app/api/volcengine/voice-clone/status/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const speakerId = searchParams.get('speaker_id');
  
  if (!speakerId) {
    return Response.json({ error: 'Missing speaker_id' }, { status: 400 });
  }
  
  const voiceClone = new VolcengineVoiceClone(
    process.env.VOLCENGINE_ACCESS_TOKEN!
  );
  
  const status = await voiceClone.checkStatus(
    process.env.VOLCENGINE_APP_ID!,
    speakerId
  );
  
  return Response.json(status);
}
```

## 最佳实践

### 1. 音频质量优化
```typescript
function validateAudioQuality(file: File): string[] {
  const errors: string[] = [];
  
  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    errors.push('音频文件不能超过10MB');
  }
  
  // 检查文件格式
  const allowedFormats = ['wav', 'mp3', 'ogg', 'm4a', 'aac'];
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (!extension || !allowedFormats.includes(extension)) {
    errors.push('不支持的音频格式');
  }
  
  return errors;
}
```

### 2. 训练进度监控
```typescript
async function monitorTraining(voiceClone: VolcengineVoiceClone, appid: string, speakerId: string) {
  const maxRetries = 120; // 最多等待1小时
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const status = await voiceClone.checkStatus(appid, speakerId);
      
      console.log(`Training progress: ${status.status}`);
      
      if (status.status === 2 || status.status === 4) {
        return status;
      } else if (status.status === 3) {
        throw new Error('Training failed');
      }
      
      await new Promise(resolve => setTimeout(resolve, 30000));
      retries++;
    } catch (error) {
      console.error('Status check failed:', error);
      retries++;
    }
  }
  
  throw new Error('Training timeout');
}
```

### 3. 成本控制
- 每个音色最多支持10次上传
- 建议使用高质量音频减少重试
- 合理选择model_type平衡效果和成本
- 实施音色管理和复用策略
