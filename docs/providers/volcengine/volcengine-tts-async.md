# 火山引擎长文本异步语音合成模型

## 模型概述

**模型ID**: `volcengine-tts-async`  
**模型类型**: 语音合成 (TTS)  
**接口类型**: HTTP REST API (异步)  
**适用场景**: 批量处理，支持10万字符以内文本  

## API接口规范

### 任务提交接口
```
POST https://openspeech.bytedance.com/api/v1/tts_async/submit
```

### 结果查询接口
```
POST https://openspeech.bytedance.com/api/v1/tts_async/query
```

### 认证方式
```http
Authorization: Bearer; {access_token}
Resource-Id: volc.tts_async.default
Content-Type: application/json
```

## 任务提交

### 请求参数
```json
{
  "appid": "your_app_id",
  "reqid": "unique_request_id",
  "text": "长文本内容",
  "format": "mp3",
  "voice_type": "BV701_streaming",
  "sample_rate": 24000,
  "volume": 1.2,
  "speed": 0.9,
  "pitch": 1.1,
  "enable_subtitle": 1,
  "callback_url": "https://your-domain.com/api/volcengine/callback"
}
```

### 参数说明
- **appid**: 应用ID
- **reqid**: 唯一请求ID
- **text**: 要合成的文本（最大10万字符）
- **format**: 音频格式（mp3/wav/pcm）
- **voice_type**: 音色类型
- **sample_rate**: 采样率（8000/16000/24000）
- **volume**: 音量（0.5-2.0）
- **speed**: 语速（0.2-3.0）
- **pitch**: 音调（0.5-2.0）
- **enable_subtitle**: 是否生成字幕（0/1）
- **callback_url**: 回调地址（可选）

### 提交响应
```json
{
  "code": 20000000,
  "message": "Success",
  "task_id": "task_uuid_12345"
}
```

## 结果查询

### 查询请求
```http
GET /api/v1/tts_async/query?appid=123456&task_id=task_uuid_12345
```

### 查询响应

#### 处理中
```json
{
  "code": 20000000,
  "message": "Success",
  "task_id": "task_uuid_12345",
  "status": "processing",
  "progress": 45
}
```

#### 完成
```json
{
  "code": 20000000,
  "message": "Success",
  "task_id": "task_uuid_12345",
  "status": "success",
  "audio_url": "https://storage.volcengine.com/audio/result.mp3",
  "subtitle_url": "https://storage.volcengine.com/subtitle/result.srt",
  "duration": 180000,
  "file_size": 2048576
}
```

#### 失败
```json
{
  "code": 40000001,
  "message": "Text processing failed",
  "task_id": "task_uuid_12345",
  "status": "failed"
}
```

## 回调机制

### 回调请求格式
当任务完成时，系统会向指定的callback_url发送POST请求：

```json
{
  "task_id": "task_uuid_12345",
  "status": "success",
  "code": 20000000,
  "message": "Success",
  "audio_url": "https://storage.volcengine.com/audio/result.mp3",
  "subtitle_url": "https://storage.volcengine.com/subtitle/result.srt",
  "duration": 180000,
  "file_size": 2048576,
  "callback_data": "custom_data_from_request"
}
```

### 回调验证
建议验证回调请求的合法性：
```typescript
function verifyCallback(signature: string, body: string, secret: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(body)
    .digest('hex');
  return signature === expectedSignature;
}
```

## 集成示例

### JavaScript/TypeScript
```typescript
interface AsyncTTSRequest {
  appid: string;
  reqid: string;
  text: string;
  format: string;
  voice_type: string;
  sample_rate: number;
  callback_url?: string;
}

interface AsyncTTSResponse {
  code: number;
  message: string;
  task_id: string;
  status?: string;
  audio_url?: string;
  subtitle_url?: string;
  duration?: number;
}

class VolcengineAsyncTTS {
  private apiKey: string;
  private appId: string;
  private baseURL = 'https://openspeech.bytedance.com';

  constructor(apiKey: string, appId: string) {
    this.apiKey = apiKey;
    this.appId = appId;
  }

  async submitTask(request: AsyncTTSRequest): Promise<string> {
    const response = await fetch(`${this.baseURL}/api/v1/tts_async/submit`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.tts_async.default',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...request,
        appid: this.appId
      })
    });

    const result: AsyncTTSResponse = await response.json();
    if (result.code === 20000000) {
      return result.task_id;
    }
    throw new Error(result.message);
  }

  async queryResult(taskId: string): Promise<AsyncTTSResponse> {
    const response = await fetch(
      `${this.baseURL}/api/v1/tts_async/query?appid=${this.appId}&task_id=${taskId}`,
      {
        headers: {
          'Authorization': `Bearer; ${this.apiKey}`,
          'Resource-Id': 'volc.tts_async.default'
        }
      }
    );

    return await response.json();
  }

  async waitForCompletion(taskId: string, maxWaitTime = 300000): Promise<AsyncTTSResponse> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const result = await this.queryResult(taskId);
      
      if (result.status === 'success' || result.status === 'failed') {
        return result;
      }
      
      // 等待5秒后重试
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    throw new Error('Task timeout');
  }
}
```

### Python
```python
import requests
import time
import json
from typing import Dict, Optional

class VolcengineAsyncTTS:
    def __init__(self, api_key: str, app_id: str):
        self.api_key = api_key
        self.app_id = app_id
        self.base_url = "https://openspeech.bytedance.com"
    
    def submit_task(self, text: str, voice_type: str = "BV701_streaming", 
                   callback_url: Optional[str] = None) -> str:
        url = f"{self.base_url}/api/v1/tts_async/submit"
        
        payload = {
            "appid": self.app_id,
            "reqid": f"req_{int(time.time())}",
            "text": text,
            "format": "mp3",
            "voice_type": voice_type,
            "sample_rate": 24000
        }
        
        if callback_url:
            payload["callback_url"] = callback_url
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.tts_async.default",
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        result = response.json()
        
        if result["code"] == 20000000:
            return result["task_id"]
        else:
            raise Exception(result["message"])
    
    def query_result(self, task_id: str) -> Dict:
        url = f"{self.base_url}/api/v1/tts_async/query"
        params = {"appid": self.app_id, "task_id": task_id}
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.tts_async.default"
        }
        
        response = requests.get(url, params=params, headers=headers)
        return response.json()
    
    def wait_for_completion(self, task_id: str, max_wait_time: int = 300) -> Dict:
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            result = self.query_result(task_id)
            
            if result["status"] in ["success", "failed"]:
                return result
            
            time.sleep(5)  # 等待5秒
        
        raise TimeoutError("Task timeout")
```

## 状态码说明

### 成功状态码
- **20000000**: 成功

### 错误状态码
- **40000001**: 文本处理失败
- **40000002**: 音频生成失败
- **40000003**: 任务不存在
- **40000004**: 任务已过期
- **50000000**: 服务器内部错误

## 任务状态

### 状态类型
- **pending**: 等待处理
- **processing**: 处理中
- **success**: 成功完成
- **failed**: 处理失败
- **expired**: 任务过期

### 状态轮询建议
- 初始间隔：1秒
- 最大间隔：10秒
- 指数退避策略
- 最大等待时间：5分钟

## Cloudflare Pages集成

### API路由实现
```typescript
// app/api/volcengine/tts-async/submit/route.ts
export async function POST(request: Request) {
  const { text, voice, options } = await request.json();
  
  const tts = new VolcengineAsyncTTS(
    process.env.VOLCENGINE_ACCESS_TOKEN!,
    process.env.VOLCENGINE_APP_ID!
  );
  
  const taskId = await tts.submitTask({
    reqid: crypto.randomUUID(),
    text: text,
    voice_type: voice || "BV701_streaming",
    format: "mp3",
    sample_rate: 24000,
    callback_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/volcengine/callback`,
    ...options
  });
  
  return Response.json({ task_id: taskId });
}

// app/api/volcengine/tts-async/query/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const taskId = searchParams.get('task_id');
  
  if (!taskId) {
    return Response.json({ error: 'Missing task_id' }, { status: 400 });
  }
  
  const tts = new VolcengineAsyncTTS(
    process.env.VOLCENGINE_ACCESS_TOKEN!,
    process.env.VOLCENGINE_APP_ID!
  );
  
  const result = await tts.queryResult(taskId);
  return Response.json(result);
}

// app/api/volcengine/callback/route.ts
export async function POST(request: Request) {
  const body = await request.json();
  
  // 处理回调结果
  if (body.status === 'success') {
    // 保存音频URL到数据库
    await saveAudioResult(body.task_id, body.audio_url);
  }
  
  return Response.json({ received: true });
}
```

## 最佳实践

### 1. 文本预处理
```typescript
function preprocessText(text: string): string {
  // 移除多余空格
  text = text.replace(/\s+/g, ' ').trim();
  
  // 处理特殊字符
  text = text.replace(/[^\u4e00-\u9fa5\u0020-\u007E]/g, '');
  
  // 限制长度
  if (text.length > 100000) {
    text = text.substring(0, 100000);
  }
  
  return text;
}
```

### 2. 错误重试机制
```typescript
async function submitWithRetry(tts: VolcengineAsyncTTS, request: AsyncTTSRequest, maxRetries = 3): Promise<string> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await tts.submitTask(request);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)));
    }
  }
  throw new Error('Max retries exceeded');
}
```

### 3. 成本优化
- 合理选择音频格式和采样率
- 批量处理多个文本
- 使用回调机制减少轮询
- 实施文本缓存策略
