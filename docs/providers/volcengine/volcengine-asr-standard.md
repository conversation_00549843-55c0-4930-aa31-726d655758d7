# 火山引擎标准语音识别模型

## 模型概述

**模型ID**: `volcengine-asr-standard`  
**模型类型**: 语音识别 (ASR)  
**接口类型**: HTTP REST API  
**适用场景**: 通用语音识别，成本优化场景  

## 模型版本

### 录音文件识别极速版
- **特点**: 快速识别，成本较低
- **适用**: 对速度要求高的场景
- **精度**: 标准精度

### 录音文件识别标准版
- **特点**: 高精度识别
- **适用**: 对准确率要求高的场景
- **精度**: 高精度

## API接口规范

### 任务提交接口
```
POST https://openspeech.bytedance.com/api/v1/auc/submit
```

### 结果查询接口
```
POST https://openspeech.bytedance.com/api/v1/auc/query
```

### 认证方式
```http
Authorization: Bearer; {access_token}
Resource-Id: volc.auc.sauc
Content-Type: application/json
```

## 任务提交

### 请求参数
```json
{
  "appid": "your_app_id",
  "reqid": "unique_request_id",
  "cluster": "volcano_asr",
  "url": "https://your-domain.com/audio/sample.wav",
  "language": "zh-CN",
  "use_itn": true,
  "use_capitalize": false,
  "callback_url": "https://your-domain.com/api/volcengine/asr/callback"
}
```

### 参数说明
- **appid**: 应用ID
- **reqid**: 唯一请求ID
- **cluster**: 集群标识
  - `volcano_asr`: 极速版
  - `volcano_asr_standard`: 标准版
- **url**: 音频文件URL（公网可访问）
- **language**: 语言代码
- **use_itn**: 是否使用逆文本标准化
- **use_capitalize**: 是否使用首字母大写
- **callback_url**: 回调地址（可选）

### 支持的语言
- **中文普通话**: `zh-CN`
- **英文**: `en-US`
- **日语**: `ja-JP`
- **韩语**: `ko-KR`
- **法语**: `fr-FR`
- **西班牙语**: `es-ES`
- **葡萄牙语**: `pt-BR`
- **印尼语**: `id-ID`

### 音频要求
- **格式支持**: wav、mp3、m4a、aac、ogg、flac
- **采样率**: 8000Hz-48000Hz
- **声道**: 单声道/立体声
- **时长**: 最长3小时
- **文件大小**: 最大200MB
- **比特率**: 建议64kbps以上

### 提交响应
```json
{
  "code": 10000,
  "message": "Success",
  "id": "task_uuid_12345"
}
```

## 结果查询

### 查询请求
```json
{
  "appid": "your_app_id",
  "id": "task_uuid_12345"
}
```

### 查询响应

#### 处理中
```json
{
  "code": 10000,
  "message": "Success",
  "id": "task_uuid_12345",
  "status": "processing"
}
```

#### 完成
```json
{
  "code": 10000,
  "message": "Success",
  "id": "task_uuid_12345",
  "status": "success",
  "result": {
    "text": "识别出的完整文本内容",
    "utterances": [
      {
        "text": "第一句话的内容",
        "start_time": 1.0,
        "end_time": 3.0
      },
      {
        "text": "第二句话的内容",
        "start_time": 3.5,
        "end_time": 6.0
      }
    ]
  }
}
```

#### 失败
```json
{
  "code": 40001,
  "message": "Audio download failed",
  "id": "task_uuid_12345",
  "status": "failed"
}
```

## 集成示例

### JavaScript/TypeScript
```typescript
interface ASRStandardRequest {
  appid: string;
  reqid: string;
  cluster: string;
  url: string;
  language: string;
  use_itn?: boolean;
  use_capitalize?: boolean;
  callback_url?: string;
}

interface ASRStandardResult {
  text: string;
  utterances: Array<{
    text: string;
    start_time: number;
    end_time: number;
  }>;
}

class VolcengineASRStandard {
  private apiKey: string;
  private appId: string;
  private baseURL = 'https://openspeech.bytedance.com';

  constructor(apiKey: string, appId: string) {
    this.apiKey = apiKey;
    this.appId = appId;
  }

  async submitTask(request: ASRStandardRequest): Promise<string> {
    const response = await fetch(`${this.baseURL}/api/v1/auc/submit`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.auc.sauc',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...request,
        appid: this.appId
      })
    });

    const result = await response.json();
    if (result.code === 10000) {
      return result.id;
    }
    throw new Error(result.message);
  }

  async queryResult(taskId: string): Promise<any> {
    const response = await fetch(`${this.baseURL}/api/v1/auc/query`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.auc.sauc',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        appid: this.appId,
        id: taskId
      })
    });

    return await response.json();
  }

  async waitForCompletion(taskId: string, maxWaitTime = 300000): Promise<ASRStandardResult> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const result = await this.queryResult(taskId);
      
      if (result.status === 'success') {
        return result.result;
      } else if (result.status === 'failed') {
        throw new Error(result.message);
      }
      
      // 等待3秒后重试
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    throw new Error('Task timeout');
  }

  async recognizeAudio(audioUrl: string, options: {
    version?: 'fast' | 'standard';
    language?: string;
    use_itn?: boolean;
  } = {}): Promise<ASRStandardResult> {
    const {
      version = 'fast',
      language = 'zh-CN',
      use_itn = true
    } = options;

    const cluster = version === 'standard' ? 'volcano_asr_standard' : 'volcano_asr';

    const taskId = await this.submitTask({
      appid: this.appId,
      reqid: crypto.randomUUID(),
      cluster: cluster,
      url: audioUrl,
      language: language,
      use_itn: use_itn,
      use_capitalize: false
    });

    return await this.waitForCompletion(taskId);
  }
}
```

### Python
```python
import requests
import time
import uuid
from typing import Dict, Optional, Literal

class VolcengineASRStandard:
    def __init__(self, api_key: str, app_id: str):
        self.api_key = api_key
        self.app_id = app_id
        self.base_url = "https://openspeech.bytedance.com"
    
    def submit_task(self, audio_url: str, 
                   version: Literal['fast', 'standard'] = 'fast',
                   language: str = "zh-CN", 
                   callback_url: Optional[str] = None) -> str:
        url = f"{self.base_url}/api/v1/auc/submit"
        
        cluster = "volcano_asr_standard" if version == "standard" else "volcano_asr"
        
        payload = {
            "appid": self.app_id,
            "reqid": str(uuid.uuid4()),
            "cluster": cluster,
            "url": audio_url,
            "language": language,
            "use_itn": True,
            "use_capitalize": False
        }
        
        if callback_url:
            payload["callback_url"] = callback_url
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.auc.sauc",
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        result = response.json()
        
        if result["code"] == 10000:
            return result["id"]
        else:
            raise Exception(result["message"])
    
    def query_result(self, task_id: str) -> Dict:
        url = f"{self.base_url}/api/v1/auc/query"
        
        payload = {
            "appid": self.app_id,
            "id": task_id
        }
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.auc.sauc",
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        return response.json()
    
    def wait_for_completion(self, task_id: str, max_wait_time: int = 300) -> Dict:
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            result = self.query_result(task_id)
            
            if result["status"] == "success":
                return result["result"]
            elif result["status"] == "failed":
                raise Exception(result["message"])
            
            time.sleep(3)  # 等待3秒
        
        raise TimeoutError("Task timeout")
    
    def recognize_audio(self, audio_url: str, 
                       version: Literal['fast', 'standard'] = 'fast',
                       language: str = "zh-CN") -> Dict:
        task_id = self.submit_task(audio_url, version, language)
        return self.wait_for_completion(task_id)
```

## 版本对比

### 极速版 vs 标准版

| 特性 | 极速版 | 标准版 |
|------|--------|--------|
| 识别速度 | 快 | 中等 |
| 识别精度 | 标准 | 高 |
| 成本 | 低 | 中等 |
| 适用场景 | 实时场景 | 高精度要求 |
| 最大时长 | 3小时 | 3小时 |
| 语言支持 | 8种 | 8种 |

### 选择建议
- **极速版**: 适合实时转录、会议记录等对速度要求高的场景
- **标准版**: 适合字幕制作、内容审核等对精度要求高的场景

## 状态码说明

### 成功状态码
- **10000**: 成功

### 错误状态码
- **40001**: 音频下载失败
- **40002**: 音频格式不支持
- **40003**: 音频时长超限
- **40004**: 音频文件损坏
- **40005**: 语言不支持
- **50000**: 服务器内部错误

## Cloudflare Pages集成

### API路由实现
```typescript
// app/api/volcengine/asr-standard/submit/route.ts
export async function POST(request: Request) {
  const { audio_url, version, language, options } = await request.json();
  
  const asr = new VolcengineASRStandard(
    process.env.VOLCENGINE_ACCESS_TOKEN!,
    process.env.VOLCENGINE_APP_ID!
  );
  
  const taskId = await asr.submitTask({
    appid: process.env.VOLCENGINE_APP_ID!,
    reqid: crypto.randomUUID(),
    cluster: version === 'standard' ? 'volcano_asr_standard' : 'volcano_asr',
    url: audio_url,
    language: language || 'zh-CN',
    callback_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/volcengine/asr-standard/callback`,
    ...options
  });
  
  return Response.json({ task_id: taskId });
}

// app/api/volcengine/asr-standard/query/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const taskId = searchParams.get('task_id');
  
  if (!taskId) {
    return Response.json({ error: 'Missing task_id' }, { status: 400 });
  }
  
  const asr = new VolcengineASRStandard(
    process.env.VOLCENGINE_ACCESS_TOKEN!,
    process.env.VOLCENGINE_APP_ID!
  );
  
  const result = await asr.queryResult(taskId);
  return Response.json(result);
}
```

## 最佳实践

### 1. 版本选择策略
```typescript
function selectASRVersion(requirements: {
  speed_priority: boolean;
  accuracy_priority: boolean;
  cost_sensitive: boolean;
}): 'fast' | 'standard' {
  if (requirements.accuracy_priority && !requirements.cost_sensitive) {
    return 'standard';
  }
  
  if (requirements.speed_priority || requirements.cost_sensitive) {
    return 'fast';
  }
  
  return 'fast'; // 默认选择极速版
}
```

### 2. 语言检测
```typescript
function detectLanguage(audioUrl: string): Promise<string> {
  // 可以先用极速版快速识别一小段音频来检测语言
  // 然后再用检测到的语言进行完整识别
  return Promise.resolve('zh-CN'); // 简化示例
}
```

### 3. 结果质量评估
```typescript
function evaluateASRQuality(result: ASRStandardResult): {
  confidence: number;
  suggestions: string[];
} {
  const suggestions: string[] = [];
  let confidence = 1.0;
  
  // 检查文本长度
  if (result.text.length < 10) {
    confidence -= 0.2;
    suggestions.push('音频可能过短或无有效语音');
  }
  
  // 检查句子数量
  if (result.utterances.length === 0) {
    confidence -= 0.5;
    suggestions.push('未检测到有效语句');
  }
  
  return { confidence, suggestions };
}
```

### 4. 成本优化
- 根据需求选择合适的版本
- 使用回调机制减少轮询
- 实施音频预处理优化
- 合理设置重试策略
