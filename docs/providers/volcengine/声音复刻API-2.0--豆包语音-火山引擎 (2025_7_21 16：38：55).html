<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/1305191 
 saved date: Mon Jul 21 2025 16:38:55 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>声音复刻API-2.0--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=声音复刻API-2.0--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=声音复刻API-2.0--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><style>img[src="data:,"],source[src="data:,"]{display:none!important}</style><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 sidebarbot-Qz1r"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-8><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-9><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-13><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>声音复刻API-2.0</div><div class=info-TbRN><span>最近更新时间：2025.07.21 15:59:52</span><span>首次发布时间：2024.07.10 20:51:57</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>声音复刻从2024.07.10开始提供2.0升级效果。所有还剩余训练次数的音色均可以调用2.0训练接口进行训练。<br>
调用时注意传递model_type=1，另外原始音频如果不是中文也必须指定语种。<br>
2025.04.30开始提供dit复刻效果（更适合非实时场景），model_type=2为DiT标准版效果（音色、不还原用户的风格），model_type=3为DiT还原版效果（音色、还原用户口音、语速等风格）</p></div><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>model_type=1/2/3时，在合成时需要替换cluster。<br>
如使用字符版，控制台显示的旧cluster为volcano_mega，需替换为volcano_icl;<br>
如使用并发版，控制台显示的旧cluster为volcano_mega_concurr，需替换为volcano_icl_concurr<br>
在使用大模型语音合成-双向流式API时，<br>
X-Api-Resource-Id：volc.megatts.default（小时版）<br>
volc.megatts.concurr（并发版）</p></div><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>复刻音色在购买后需要一定服务准备时间，预计3～5分钟后可以开始训练音色，1小时后可以参与合成</p></div><p><span id=597da1a0></span><div id=创建音色 class="md-h1 heading-h1">创建音色</div><ol><li>请求方式</ol><p><strong>域名：</strong> https://openspeech.bytedance.com<br>
具体请求方式可参考下方<code>示例代码</code><br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>uploadAndStatus.py</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><ol start=2><li>训练（upload接口）</ol><p><strong>接口路径:</strong> <code>POST</code>/api/v1/mega_tts/audio/upload<br><strong>接口描述: 提交音频训练音色</strong><br>
认证方式使用Bearer Token，在请求的header中加上<code>"Authorization": "Bearer; {token}"</code>，并在请求的json中填入对应的appid。<div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>Bearer和token使用分号 ; 分隔，替换时请勿保留{}<br>
AppID/Token/Cluster 等信息可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F target=_blank rel=noreferrer class=external>控制台使用FAQ-Q1</a></p></div><p><span id=ca9fa4cb></span><h4>请求参数</h4><p><strong>Header:</strong><table class=volc-viewer-table><thead><tr><th><p>参数名称</p><th><p>参数类型</p><th><p>必须参数</p><th><p>备注</p><tbody><tr><td><p>Authorization</p><td><p>string</p><td><p>必填</p><td><p>Bearer;${Access Token}</p><tr><td><p>Resource-Id</p><td><p>string</p><td><p>必填</p><td><p>volc.megatts.voiceclone<br>
（声音复刻2.0目前已经支持双向流式）</p></table><p><strong>Body:</strong><table class=volc-viewer-table><thead><tr><th><p>参数名称</p><th><p>层级</p><th><p>参数类型</p><th><p>必须参数</p><th><p>备注</p><tbody><tr><td><p>appid</p><td><p>1</p><td><p>string</p><td><p>必填</p><td><tr><td><p>speaker_id</p><td><p>1</p><td><p>string</p><td><p>必填</p><td><p>唯一音色代号</p><tr><td><p>audios</p><td><p>1</p><td><p>list</p><td><p>必填</p><td><p>音频格式支持：wav、mp3、ogg、m4a、aac、pcm，其中pcm仅支持24k 单通道目前限制单文件上传最大10MB，每次最多上传1个音频文件</p><tr><td><p>audio_bytes</p><td><p>2</p><td><p>string</p><td><p>必填</p><td><p>二进制音频字节，需对二进制音频进行base64编码</p><tr><td><p>audio_format</p><td><p>2</p><td><p>string</p><td><td><p>音频格式，pcm、m4a必传，其余可选</p><tr><td><p>text</p><td><p>2</p><td><p>string</p><td><td><p>可以让用户按照该文本念诵，服务会对比音频与该文本的差异。若差异过大会返回1109 WERError</p><tr><td><p>source</p><td><p>1</p><td><p>int</p><td><p>必填</p><td><p>固定值：2</p><tr><td><p>language</p><td><p>1</p><td><p>int</p><td><td><p>model_type为0或者1时候，支持以下语种<ul><li>cn = 0 中文（默认）<li>en = 1 英文<li>ja = 2 日语<li>es = 3 西班牙语<li>id = 4 印尼语<li>pt = 5 葡萄牙语</ul><p>model_type为2时候，支持以下语种<ul><li>cn = 0 中文（默认）<li>en = 1 英文<li>ja = 2 日语<li>es = 3 西班牙语<li>id = 4 印尼语<li>pt = 5 葡萄牙语<li>de = 6 德语<li>fr = 7 法语</ul><p>model_type为3时候，仅支持以下语种<ul><li>cn = 0 中文（默认）<li>en = 1 英文</ul><tr><td><p>model_type</p><td><p>1</p><td><p>int</p><td><td><p>默认为0<br>
1为2.0效果（ICL），0为1.0效果<br>
2为DiT标准版效果（音色、不还原用户的风格）<br>
3为DiT还原版效果（音色、还原用户口音、语速等风格）</p></table><p><strong>json示例</strong><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"speaker_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_*******"</span><span class=hljs-punctuation>,</span>（需从控制台获取，参考文档：声音复刻下单及使用指南）
        <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"your appid"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"audios"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span><span class=hljs-punctuation>{</span>
                <span class=hljs-attr>"audio_bytes"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"base64编码后的音频"</span><span class=hljs-punctuation>,</span>
                <span class=hljs-attr>"audio_format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"wav"</span>
        <span class=hljs-punctuation>}</span><span class=hljs-punctuation>]</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"source"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"language"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"model_type"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=10484156></span><h4>返回数据</h4><p><strong>Body:</strong><table class=volc-viewer-table><thead><tr><th><p>参数名称</p><th><p>层级</p><th><p>参数类型</p><th><p>必须参数</p><th><p>备注</p><tbody><tr><td><p>BaseResp</p><td><p>1</p><td><p>object</p><td><p>必填</p><td><tr><td><p>StatusCode</p><td><p>2</p><td><p>int</p><td><p>必填</p><td><p>成功:0</p><tr><td><p>StatusMessage</p><td><p>2</p><td><p>string</p><td><td><p>错误信息</p><tr><td><p>speaker_id</p><td><p>1</p><td><p>string</p><td><p>必填</p><td><p>唯一音色代号</p></table><p><strong>json示例</strong><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"BaseResp"</span><span class=hljs-punctuation>:</span><span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"StatusCode"</span><span class=hljs-punctuation>:</span><span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"StatusMessage"</span><span class=hljs-punctuation>:</span><span class=hljs-string>""</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"speaker_id"</span><span class=hljs-punctuation>:</span><span class=hljs-string>"S_*******"</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ol start=3><li>返回码：</ol><table class=volc-viewer-table><thead><tr><th><p>Success</p><th><p>0</p><th><p>成功</p><tbody><tr><td><p>BadRequestError</p><td><p>1001</p><td><p>请求参数有误</p><tr><td><p>AudioUploadError</p><td><p>1101</p><td><p>音频上传失败</p><tr><td><p>ASRError</p><td><p>1102</p><td><p>ASR（语音识别成文字）转写失败</p><tr><td><p>SIDError</p><td><p>1103</p><td><p>SID声纹检测失败</p><tr><td><p>SIDFailError</p><td><p>1104</p><td><p>声纹检测未通过，声纹跟名人相似度过高</p><tr><td><p>GetAudioDataError</p><td><p>1105</p><td><p>获取音频数据失败</p><tr><td><p>SpeakerIDDuplicationError</p><td><p>1106</p><td><p>SpeakerID重复</p><tr><td><p>SpeakerIDNotFoundError</p><td><p>1107</p><td><p>SpeakerID未找到</p><tr><td><p>AudioConvertError</p><td><p>1108</p><td><p>音频转码失败</p><tr><td><p>WERError</p><td><p>1109</p><td><p>wer检测错误，上传音频与请求携带文本对比字错率过高</p><tr><td><p>AEDError</p><td><p>1111</p><td><p>aed检测错误，通常由于音频不包含说话声</p><tr><td><p>SNRError</p><td><p>1112</p><td><p>SNR检测错误，通常由于信噪比过高</p><tr><td><p>DenoiseError</p><td><p>1113</p><td><p>降噪处理失败</p><tr><td><p>AudioQualityError</p><td><p>1114</p><td><p>音频质量低，降噪失败</p><tr><td><p>ASRNoSpeakerError</p><td><p>1122</p><td><p>未检测到人声</p><tr><td><p>已达上传次数限制</p><td><p>1123</p><td><p>上传接口已经达到次数限制，目前同一个音色支持10次上传</p></table><ol start=4><li>状态查询（status接口）</ol><p><strong>接口路径:</strong> <code>POST</code>/api/v1/mega_tts/status<br><strong>接口描述: 查询音色训练状态</strong><br><span id=7d8a3c2a></span><h4>请求参数</h4><p><strong>Header:</strong><table class=volc-viewer-table><thead><tr><th><p>参数名称</p><th><p>参数类型</p><th><p>必须参数</p><th><p>备注</p><tbody><tr><td><p>Authorization</p><td><p>string</p><td><p>必填</p><td><p>Bearer;${Access Token}</p><tr><td><p>Resource-Id</p><td><p>string</p><td><p>必填</p><td><p>填入volc.megatts.voiceclone</p></table><p><strong>Body:</strong><table class=volc-viewer-table><thead><tr><th><p>参数名称</p><th><p>层级</p><th><p>类型</p><th><p>必填</p><th><p>备注</p><tbody><tr><td><p>appid</p><td><p>1</p><td><p>string</p><td><p>必填</p><td><tr><td><p>speaker_id</p><td><p>1</p><td><p>string</p><td><p>必填</p><td><p>唯一音色代号</p></table><p><strong>json示例</strong><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"your appid"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"speaker_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_*******"</span>（需从控制台获取，参考文档：声音复刻下单及使用指南）
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=776080fe></span><h4>返回数据</h4><p><strong>Body:</strong><table class=volc-viewer-table><thead><tr><th><p>参数名称</p><th><p>层级</p><th><p>参数类型</p><th><p>必须参数</p><th><p>备注</p><tbody><tr><td><p>BaseResp</p><td><p>1</p><td><p>object</p><td><p>必填</p><td><tr><td><p>StatusCode</p><td><p>2</p><td><p>int</p><td><p>必填</p><td><p>成功:0</p><tr><td><p>StatusMessage</p><td><p>2</p><td><p>string</p><td><td><p>错误信息</p><tr><td><p>speaker_id</p><td><p>1</p><td><p>string</p><td><p>必填</p><td><p>唯一音色代号</p><tr><td><p>status</p><td><p>1</p><td><p>enum { NotFound = 0 Training = 1 Success = 2 Failed = 3 Active = 4 }</p><td><p>必填</p><td><p>训练状态，状态为2或4时都可以调用tts</p><tr><td><p>create_time</p><td><p>1</p><td><p>int</p><td><p>必填</p><td><p>创建时间</p><tr><td><p>version</p><td><p>1</p><td><p>string</p><td><p>选填</p><td><p>训练版本</p><tr><td><p>demo_audio</p><td><p>1</p><td><p>string</p><td><p>选填</p><td><p>Success状态时返回，一小时有效，若需要，请下载后使用</p></table><p><strong>json示例</strong><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"BaseResp"</span><span class=hljs-punctuation>:</span><span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"StatusCode"</span><span class=hljs-punctuation>:</span><span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"StatusMessage"</span><span class=hljs-punctuation>:</span><span class=hljs-string>""</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"creaet_time"</span><span class=hljs-punctuation>:</span><span class=hljs-number>1701055304000</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"V1"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"demo_audio"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"http://**********.wav"</span>
    <span class=hljs-attr>"speaker_id"</span><span class=hljs-punctuation>:</span><span class=hljs-string>"S_*******"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"status"</span><span class=hljs-punctuation>:</span><span class=hljs-number>2</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=4a39abd8></span><div id=tts-语音合成接口（ws-http） class="md-h1 heading-h1">TTS 语音合成接口（WS/HTTP）</div><p>音色训练成功后，需要通过调用TTS接口来使用音色合成指定文本的音频。<div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>接口与TTS参数有差别，需要将<code>cluster</code>换成<code>volcano_</code>icl，<code>voice_type</code>传<code>声音id</code>。</p></div><p><span id=90321bbf></span><h2 id=websocket>Websocket</h2><blockquote><p>使用账号申请部分申请到的appid&amp;access_token进行调用<br>
文本一次性送入，后端边合成边返回音频数据</p></blockquote><p><span id=8473acf1></span><h3 id=_1-接口说明>1. 接口说明</h3><blockquote><p>接口地址为 <strong>wss://openspeech.bytedance.com/api/v1/tts/ws_binary</strong></p></blockquote><p><span id=b36183d7></span><h3 id=_2-身份认证>2. 身份认证</h3><p>认证方式使用Bearer Token，在请求的header中加上<code>"Authorization": "Bearer; {token}"</code>，并在请求的json中填入对应的appid。<div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>Bearer和token使用分号 ; 分隔，替换时请勿保留{}</p></div><p>AppID/Token/Cluster 等信息可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a><br><span id=cb29e655></span><h3 id=_3-请求方式>3. 请求方式</h3><p><span id=eebd84f7></span><h4>3.1 二进制协议</h4><p><span id=a8a704c1></span><h5>报文格式(Message format)</h5><p><img alt=Image class=volc-image-img src=data:, width=1816><br class=sf-hidden>
所有字段以 <a href=https://zh.wikipedia.org/wiki/%E5%AD%97%E8%8A%82%E5%BA%8F#%E5%A4%A7%E7%AB%AF%E5%BA%8F target=_blank rel=noreferrer class=external>Big Endian(大端序)</a> 的方式存储。<br><span id=3431cf06></span><h6><strong>字段描述</strong></h6><table class=volc-viewer-table><thead><tr><th><p>字段 Field (大小, 单位bit)</p><th><p>描述 Description</p><th><p>值 Values</p><tbody><tr><td><p>协议版本(Protocol version) (4)</p><td><p>可能会在将来使用不同的协议版本，所以这个字段是为了让客户端和服务器在版本上保持一致。</p><td><p><code>0b0001</code> - 版本 1 (目前只有版本1)</p><tr><td><p>报头大小(Header size) (4)</p><td><p>header实际大小是 <code>header size value x 4</code> bytes.<br>
这里有个特殊值 <code>0b1111</code> 表示header大小大于或等于60(15 x 4 bytes)，也就是会存在header extension字段。</p><td><p><code>0b0001</code> - 报头大小 = 4 (1 x 4)<br><code>0b0010</code> - 报头大小 = 8 (2 x 4)<br><code>0b1010</code> - 报头大小 = 40 (10 x 4)<br><code>0b1110</code> - 报头大小 = 56 (14 x 4)<br><code>0b1111</code> - 报头大小为60或更大; 实际大小在header extension中定义</p><tr><td><p>消息类型(Message type) (4)</p><td><p>定义消息类型。</p><td><p><code>0b0001</code> - full client request.<br><code>~~0b1001~~</code> <s>- full server response(弃用).</s><br><code>0b1011</code> - Audio-only server response (ACK).<br><code>0b1111</code> - Error message from server (例如错误的消息类型，不支持的序列化方法等等)</p><tr><td><p>Message type specific flags (4)</p><td><p>flags含义取决于消息类型。<br>
具体内容请看消息类型小节.</p><td><tr><td><p>序列化方法(Message serialization method) (4)</p><td><p>定义序列化payload的方法。<br>
注意：它只对某些特定的消息类型有意义 (例如Audio-only server response <code>0b1011</code> 就不需要序列化).</p><td><p><code>0b0000</code> - 无序列化 (raw bytes)<br><code>0b0001</code> - JSON<br><code>0b1111</code> - 自定义类型, 在header extension中定义</p><tr><td><p>压缩方法(Message Compression) (4)</p><td><p>定义payload的压缩方法。<br>
Payload size字段不压缩(如果有的话，取决于消息类型)，而且Payload size指的是payload压缩后的大小。<br>
Header不压缩。</p><td><p><code>0b0000</code> - 无压缩<br><code>0b0001</code> - gzip<br><code>0b1111</code> - 自定义压缩方法, 在header extension中定义</p><tr><td><p>保留字段(Reserved) (8)</p><td><p>保留字段，同时作为边界 (使整个报头大小为4个字节).</p><td><p><code>0x00</code> - 目前只有0</p></table><p><span id=aef9feaa></span><h5>消息类型详细说明</h5><p>目前所有TTS websocket请求都使用full client request格式，无论"query"还是"submit"。<br><span id=27fb7710></span><h5>Full client request</h5><ul><li>Header size为<code>b0001</code>(即4B，没有header extension)。<li>Message type为<code>b0001</code>.<li>Message type specific flags固定为<code>b0000</code>.<li>Message serialization method为<code>b0001</code>JSON。字段参考上方表格。<li>如果使用gzip压缩payload，则payload size为压缩后的大小。</ul><p><span id=9e31c953></span><h5>Audio-only server response</h5><ul><li>Header size应该为<code>b0001</code>.<li>Message type为<code>b1011</code>.<li>Message type specific flags可能的值有：
<ul><li><code>b0000</code> - 没有sequence number.<li><code>b0001</code> - sequence number &gt; 0.<li><code>b0010</code>or<code>b0011</code> - sequence number &lt; 0，表示来自服务器的最后一条消息，此时客户端应合并所有音频片段(如果有多条)。</ul><li>Message serialization method为<code>b0000</code>(raw bytes).</ul><p><span id=5b40b4b2></span><h3 id=_4-注意事项>4.注意事项</h3><ul><li>每次合成时reqid这个参数需要重新设置，且要保证唯一性（建议使用uuid.V4生成）<li>websocket demo中单条链接仅支持单次合成，若需要合成多次，需自行实现。每次创建websocket连接后，按顺序串行发送每一包。一次合成结束后，可以发送新的合成请求。<li>operation需要设置为submit才是流式返回<li>在 websocket 握手成功后，会返回这些 Response header</ul><table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Tt-Logid</p><td><p>服务端返回的 logid，建议用户获取和打印方便定位问题</p><td><p>202407261553070FACFE6D19421815D605</p></table><p><span id=7152ac17></span><h3 id=_5-demo>5.Demo</h3><p><span id=a0669346></span><h4>python</h4><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>tts_websocket_demo.py</div><div class=volc-attachment-desc>6.89KB</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=f15c69a4></span><h4>Java</h4><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>tts-demo-java.zip</div><div class=volc-attachment-desc>7.01KB</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=04d6b71e></span><h4>Go</h4><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>tts_websocket_demo.go</div><div class=volc-attachment-desc>7.68KB</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=79dbd2f5></span><h2 id=http>HTTP</h2><blockquote><p>使用账号申请部分申请到的appid&amp;access_token进行调用<br>
文本全部合成完毕之后，一次性返回全部的音频数据</p></blockquote><p><span id=062e0694></span><h3 id=_1-接口说明-2>1. 接口说明</h3><blockquote><p>接口地址为 <strong>https://openspeech.bytedance.com/api/v1/tts</strong></p></blockquote><p><span id=6ceb285d></span><h3 id=_2-身份认证-2>2. 身份认证</h3><p>认证方式采用 Bearer Token.<br>
1)需要在请求的 Header 中填入"Authorization":"Bearer;${token}"<div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<p>Bearer和token使用分号 ; 分隔，替换时请勿保留${}</p></div><p>AppID/Token/Cluster 等信息可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a><br><span id=ae690903></span><h3 id=_3-注意事项>3. 注意事项</h3><ul><li>使用 HTTP Post 方式进行请求，返回的结果为 JSON 格式，需要进行解析<li>因 json 格式无法直接携带二进制音频，音频经base64编码。使用base64解码后，即为二进制音频<li>每次合成时 reqid 这个参数需要重新设置，且要保证唯一性（建议使用 UUID/GUID 等生成）<li>websocket demo中单条链接仅支持单次合成，若需要合成多次，需自行实现。每次创建websocket连接后，按顺序串行发送每一包。一次合成结束后，可以发送新的合成请求。</ul><ol start=4><li>Demo<br><span id=c9bb6471></span></ol><h3 id=python>Python</h3><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>tts_http_demo.py</div><div class=volc-attachment-desc>1.33KB</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=87485822></span><h3 id=java>Java</h3><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>tts_http_demo.zip</div><div class=volc-attachment-desc>13.27KB</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=657131c3></span><h3 id=go>Go</h3><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>tts_http_demo.go</div><div class=volc-attachment-desc>3.44KB</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=e58375cb></span><h2 id=参数说明>参数说明</h2><table class=volc-viewer-table><thead><tr><th><p><strong>字段</strong></p><th><p>含义</p><th><p>层级</p><th><p>格式</p><th><p>必需</p><th><p>备注</p><tbody><tr><td><p><strong>app</strong></p><td><p>应用相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p><strong>appid</strong></p><td><p>应用标识</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>需要申请</p><tr><td><p><strong>token</strong></p><td><p>应用令牌</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>可传任意非空字符串</p><tr><td><p><strong>cluster</strong></p><td><p>业务集群</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>volcano_icl或volcano_icl_concurr</p><tr><td><p><strong>user</strong></p><td><p>用户相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p><strong>uid</strong></p><td><p>用户标识</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>可传任意非空字符串，传入值可以通过服务端日志追溯</p><tr><td><p><strong>audio</strong></p><td><p>音频相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><p>语音合成参考音色列表；声音复刻语音合成请通过下单获取</p><tr><td><p><strong>voice_type</strong></p><td><p>音色类型</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>填入S_开头的声音id（SpeakerId）</p><tr><td><p><strong>encoding</strong></p><td><p>音频编码格式</p><td><p>2</p><td><p>string</p><td><td><p>wav / pcm / ogg_opus / mp3，默认为 pcm<br>
注意：wav 不支持流式</p><tr><td><p><strong>loudness_ratio</strong></p><td><p>音量调节</p><td><p>2</p><td><p>float</p><td><td><p>[0.5,2]，默认为1，通常保留一位小数即可。0.5代表原音量0.5倍，2代表原音量2倍</p><tr><td><p><strong>rate</strong></p><td><p>音频采样率</p><td><p>2</p><td><p>int</p><td><td><p>默认为 24000，可选8000，16000</p><tr><td><p><strong>speed_ratio</strong></p><td><p>语速</p><td><p>2</p><td><p>float</p><td><td><p>[0.2,3]，默认为1，通常保留一位小数即可</p><tr><td><p><strong>explicit_language</strong></p><td><p>明确语种</p><td><p>2</p><td><p>string</p><td><td><p>仅读指定语种的文本<ul><li>不给定参数，正常中英混<li><code>crosslingual</code> 启用多语种前端（包含zh/en/ja/es-ms/id/pt-br）<li><code>zh</code> 中文为主，支持中英混<li><code>en</code> 仅英文<li><code>ja</code> 仅日文<li><code>es-mx</code> 仅墨西<li><code>id</code> 仅印尼<li><code>pt-br</code> 仅巴葡</ul><p>当音色是使用model_type=2训练的，即采用dit标准版效果时，建议指定明确语种，目前支持：<ul><li>不给定参数，启用多语种前端<code>zh,en,ja,es-mx,id,pt-br,de,fr</code><li><code>zh,en,ja,es-mx,id,pt-br,de,fr</code> 启用多语种前端<li><code>zh</code> 中文为主，支持中英混<li><code>en</code> 仅英文<li><code>ja</code> 仅日文<li><code>es-mx</code> 仅墨西<li><code>id</code> 仅印尼<li><code>pt-br</code> 仅巴葡<li><code>de</code> 仅德语<li><code>fr</code> 仅法语</ul><p>当音色是使用model_type=3训练的，即采用dit还原版效果时，必须指定明确语种，目前支持：<ul><li><code>zh</code> 中文为主，支持中英混<li><code>en</code> 仅英文</ul><tr><td><p><strong>context_language</strong></p><td><p>参考语种</p><td><p>2</p><td><p>string</p><td><td><p>给模型提供参考的语种<ul><li>不给定 西欧语种采用英语<li>id 西欧语种采用印尼<li>es 西欧语种采用墨西<li>pt 西欧语种采用巴葡</ul><tr><td><p><strong>request</strong></p><td><p>请求相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p><strong>text_type</strong></p><td><p>文本类型</p><td><p>2</p><td><p>string</p><td><td><p>plain / ssml, 默认为plain。ssml参考<a href=https://www.volcengine.com/docs/6561/1330194 target=_blank rel=noreferrer class=external>SSML标记语言--语音技术-火山引擎 (volcengine.com)</a><br>
（DiT音色暂不支持ssml）</p><tr><td><p><strong>with_timestamp</strong></p><td><p>时间戳相关</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>传入1时表示启用，可返回原文本的时间戳，而非TN后文本，即保留原文中的阿拉伯数字或者特殊符号等。注意：原文本中的多个标点连用或者空格依然会被处理，但不影响时间戳连贯性</p><tr><td><p><strong>reqid</strong></p><td><p>请求标识</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>需要保证每次调用传入值唯一，建议使用 UUID</p><tr><td><p><strong>text</strong></p><td><p>文本</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>合成语音的文本，长度限制 1024 字节（UTF-8编码）</p><tr><td><p><strong>operation</strong></p><td><p>操作</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>query（非流式，http只能query） / submit（流式）</p><tr><td><p><strong>split_sentence</strong></p><td><p>复刻1.0语速相关</p><td><p>2</p><td><p>int<br>
string</p><td><td><p>传入1时表示启用，用以解决1.0的声音复刻合成时语速过快的情况</p><tr><td><p><strong>silence_duration</strong></p><td><p>句尾静音</p><td><p>2</p><td><p>float</p><td><td><p>设置该参数可在句尾增加静音时长，范围0~30000ms。（注：增加的句尾静音主要针对传入文本最后的句尾，而非每句话的句尾）若启用该参数，必须在request下首先设置enable_trailing_silence_audio = true</p><tr><td><p><strong>extra_param</strong></p><td><p>额外参数</p><td><p>2</p><td><p>jsonstring</p><td><td><tr><td><p><strong>mute_cut_remain_ms</strong></p><td><p>句首静音参数</p><td><p>3</p><td><p>string</p><td><td><p>该参数需配合mute_cut_threshold参数一起使用，其中：<br>
"mute_cut_threshold": "400", // 静音判断的阈值（音量小于该值时判定为静音）<br>
"mute_cut_remain_ms": "50", // 需要保留的静音长度<br>
注：参数和value都为string格式<br>
以python为示例：<pre class=hljs><code class="language-Python volc-pre-code hljs"><span class=hljs-string>"extra_param"</span>:(<span class=hljs-string>"{\"mute_cut_threshold\":\"400\", \"mute_cut_remain_ms\": \"100\"}"</span>)
</code><div class="volc-pre-code-language no-copy">Python</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p>特别提醒：<ul><li>因MP3格式的特殊性，句首始终会存在100ms内的静音无法消除，WAV格式的音频句首静音可全部消除，建议依照自身业务需求综合判断选择</ul><tr><td><p><strong>disable_emoji_filter</strong></p><td><p>emoji不过滤显示</p><td><p>3</p><td><p>bool</p><td><td><p>开启emoji表情在文本中不过滤显示，默认为False，建议搭配时间戳参数一起使用。<br>
Python示例："extra_param": json.dumps({"disable_emoji_filter": True})</p><tr><td><p><strong>unsupported_char_ratio_thresh</strong></p><td><p>不支持语种占比阈值</p><td><p>3</p><td><p>float</p><td><td><p>默认: 0.3，最大值: 1.0<br>
检测出不支持语种超过设置的比例，则会返回错误码或者返回兜底音频。<br>
Python示例："extra_param": json.dumps({"unsupported_char_ratio_thresh": 0.3})</p><tr><td><p><strong>cache_config</strong></p><td><p>缓存相关参数</p><td><p>3</p><td><p>dict</p><td><td><p>开启缓存，开启后合成相同文本时，服务会直接读取缓存返回上一次合成该文本的音频，可明显加快相同文本的合成速率，缓存数据保留时间1小时。<br>
（通过缓存返回的数据不会附带时间戳）<br>
Python示例："extra_param": json.dumps({"cache_config": {"text_type": 1,"use_cache": True}})</p><tr><td><p><strong>text_type</strong></p><td><p>缓存相关参数</p><td><p>4</p><td><p>int</p><td><td><p>和use_cache参数一起使用，需要开启缓存时传1</p><tr><td><p><strong>use_cache</strong></p><td><p>缓存相关参数</p><td><p>4</p><td><p>bool</p><td><td><p>和text_type参数一起使用，需要开启缓存时传true</p></table><p>备注：<ol><li>支持ssml能力，参考<a href=https://www.volcengine.com/docs/6561/1330194 target=_blank rel=noreferrer class=external>SSML标记语言--豆包语音-火山引擎 (volcengine.com)</a><li>暂时不支持音高<li>支持中英混，支持语种自动识别</ol><p>请求示例<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"app"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"appid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"appid123"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"token"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"access_token"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"cluster"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"volcano_icl"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"user"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"uid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"uid123"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"voice_type"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_xxxx"</span><span class=hljs-punctuation>,</span>（需从控制台获取，参考文档：声音复刻下单及使用指南）
        <span class=hljs-attr>"encoding"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"mp3"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"speed_ratio"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"request"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"reqid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"uuid"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"字节跳动语音合成"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"operation"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"query"</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=265b9128></span><h2 id=返回参数>返回参数</h2><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>含义</p><th><p>层级</p><th><p>格式</p><th><p>备注</p><tbody><tr><td><p>reqid</p><td><p>请求 ID</p><td><p>1</p><td><p>string</p><td><p>请求 ID,与传入的参数中 reqid 一致</p><tr><td><p>code</p><td><p>请求状态码</p><td><p>1</p><td><p>int</p><td><p>错误码，参考下方说明</p><tr><td><p>message</p><td><p>请求状态信息</p><td><p>1</p><td><p>string</p><td><p>错误信息</p><tr><td><p>sequence</p><td><p>音频段序号</p><td><p>1</p><td><p>int</p><td><p>负数表示合成完毕</p><tr><td><p>data</p><td><p>合成音频</p><td><p>1</p><td><p>string</p><td><p>返回的音频数据，base64 编码</p><tr><td><p>addition</p><td><p>额外信息</p><td><p>1</p><td><p>string</p><td><p>额外信息父节点</p><tr><td><p>duration</p><td><p>音频时长</p><td><p>2</p><td><p>string</p><td><p>返回音频的长度，单位ms</p></table><ul><li>在 websocket/http 握手成功后，会返回这些 Response header</ul><table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Tt-Logid</p><td><p>服务端返回的 logid，建议用户获取和打印方便定位问题，使用默认格式即可，不要自定义格式</p><td><p>202407261553070FACFE6D19421815D605</p></table><p><span id=a2106be9></span><div class="md-h1 heading-h1"></div><p>响应示例<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"reqid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"reqid"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"code"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3000</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"operation"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"query"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"sequence"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>-1</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"data"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"base64 encoded binary data"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"addition"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"1960"</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=553dedc3></span><h2 id=返回码说明>返回码说明</h2><table class=volc-viewer-table><thead><tr><th><p>错误码</p><th><p>错误描述</p><th><p>举例</p><th><p>建议行为</p><tbody><tr><td><p>3000</p><td><p>请求正确</p><td><p>正常合成</p><td><p>正常处理</p><tr><td><p>3001</p><td><p>无效的请求</p><td><p>一些参数的值非法，比如operation配置错误</p><td><p>检查参数</p><tr><td><p>3003</p><td><p>并发超限</p><td><p>超过在线设置的并发阈值</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3005</p><td><p>后端服务忙</p><td><p>后端服务器负载高</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3006</p><td><p>服务中断</p><td><p>请求已完成/失败之后，相同reqid再次请求</p><td><p>检查参数</p><tr><td><p>3010</p><td><p>文本长度超限</p><td><p>单次请求超过设置的文本长度阈值</p><td><p>检查参数</p><tr><td><p>3011</p><td><p>无效文本</p><td><p>参数有误或者文本为空、文本与语种不匹配、文本只含标点</p><td><p>检查参数</p><tr><td><p>3030</p><td><p>处理超时</p><td><p>单次请求超过服务最长时间限制</p><td><p>重试或检查文本</p><tr><td><p>3031</p><td><p>处理错误</p><td><p>后端出现异常</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3032</p><td><p>等待获取音频超时</p><td><p>后端网络异常</p><td><p>重试；使用sdk的情况下切换离线</p><tr><td><p>3040</p><td><p>后端链路连接错误</p><td><p>后端网络异常</p><td><p>重试</p><tr><td><p>3050</p><td><p>音色不存在</p><td><p>检查使用的voice_type代号</p><td><p>检查参数</p></table><p><span id=2b1e6803></span><h2 id=常见错误返回说明>常见错误返回说明</h2><ol><li><p>错误返回：<p>"message": "quota exceeded for types: xxxxxxxxx_lifetime"<br><strong>错误原因：试用版用量用完了，需要开通正式版才能继续使用</strong></p><li><p>错误返回：</p></ol><p>"message": "quota exceeded for types: concurrency"<br><strong>错误原因：并发超过了限定值，需要减少并发调用情况或者增购并发</strong><ol start=3><li><p>错误返回：<p>"message": "Fail to feed text, reason Init Engine Instance failed"<br><strong>错误原因：voice_type / cluster 传递错误</strong></p><li><p>错误返回：</p></ol><p>"message": "illegal input text!"<br><strong>错误原因：传入的text无效，没有可合成的有效文本。比如全部是标点符号或者emoji表情，或者使用中文音色时，传递日语，以此类推。多语种音色，也需要使用language指定对应的语种</strong><ol start=5><li>错误返回：</ol><p>"message": "authenticate request: load grant: requested grant not found"<br><strong>错误原因：鉴权失败，需要检查appid&amp;token的值是否设置正确，同时，鉴权的正确格式为</strong><br><strong>headers["Authorization"] = "Bearer;${token}"</strong><br><span id=83f5315a></span><div id=音色接口 class="md-h1 heading-h1">音色接口</div><p><span id=cda74ea0></span><h2 id=api接入说明>API接入说明</h2><p><span id=2e989347></span><h3 id=访问鉴权>访问鉴权</h3><ol><li>鉴权方式说明 <a href=https://www.volcengine.com/docs/6369/67268 target=_blank rel=noreferrer class=external>公共参数--API签名调用指南-火山引擎 (volcengine.com)</a></ol><p>线上请求地址域名 open.volcengineapi.com<ol start=2><li>固定公共参数</ol><pre class=hljs><code class="language-Plain volc-pre-code">Region = "cn-north-1"
Service = "speech_saas_prod"
Version = "2023-11-07"
解释
</code><div class="volc-pre-code-language no-copy">Plain</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ol start=3><li>AKSK获取 <a href=https://console.volcengine.com/iam/keymanage target=_blank rel=noreferrer class=external>访问控制-火山引擎 (volcengine.com)</a></ol><p>说明：<a href=https://www.volcengine.com/docs/6291/65568 target=_blank rel=noreferrer class=external>Access Key（密钥）管理--API访问密钥（Access Key）-火山引擎 (volcengine.com)</a><ol start=4><li>调用方式
<ol><li>SDK <a href=https://www.volcengine.com/docs/6369/156029 target=_blank rel=noreferrer class=external>SDK概览--API签名调用指南-火山引擎 (volcengine.com)</a><li>直接签名后调用</ol></ol><p>结合文档内api说明调用 <code>ListMegaTTSTrainStatus</code> 的例子(*其他语言和使用sdk调用的方式请参考火山鉴权源码<a href=https://www.volcengine.com/docs/6369/185600 target=_blank rel=noreferrer class=external>说明</a> 一)<ol start=3><li>示例代码：</ol><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>sign.go</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>sign.py</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>sign.java</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><span id=15d26d16></span><h3 id=错误码>错误码</h3><ol><li>非 <strong>2xx</strong> 开头的HTTP返回状态码被可以认为是<strong>错误</strong><li>错误的HTTP返回结构体如下</ol><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"ResponseMetadata"</span><span class=hljs-punctuation>:</span> 
    <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"RequestId"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"20220214145719010211209131054BC103"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// header中的X-Top-Request-Id参数</span>
        <span class=hljs-attr>"Action"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"ListMegaTTSTrainStatus"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"2023-11-07"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Service"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Service}"</span><span class=hljs-punctuation>,</span><span class=hljs-comment>// header中的X-Top-Service参数</span>
        <span class=hljs-attr>"Region"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Region}"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// header中的X-Top-Region参数</span>
        <span class=hljs-attr>"Error"</span><span class=hljs-punctuation>:</span> 
        <span class=hljs-punctuation>{</span>
            <span class=hljs-attr>"Code"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"InternalError.NotCaptured"</span><span class=hljs-punctuation>,</span>
            <span class=hljs-attr>"Message"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"xxx"</span>
        <span class=hljs-punctuation>}</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ol start=3><li><strong>"ResponseMetadata.Error.Code"</strong> 客户端可以依照这个字段判断错误种类，已知种类和含义如下</ol><table class=volc-viewer-table><thead><tr><th><p>Code</p><th><p>Description</p><tbody><tr><td><p>OperationDenied.InvalidSpeakerID</p><td><p>账号或AppID无权限操作或无法操作SpeakerID列表中的一个或多个实例</p><tr><td><p>OperationDenied.InvalidParameter</p><td><p>请求体字段不合法（缺失必填字段、类型错误等）</p><tr><td><p>InternalError.NotCaptured</p><td><p>未知的服务内部错误</p></table><p><span id=243e99e6></span><h2 id=api列表>API列表</h2><p><span id=d4956898></span><h3 id=查询-speakerid-状态信息-listmegattstrainstatus>查询 SpeakerID 状态信息 <code>ListMegaTTSTrainStatus</code></h3><p><span id=61d4b195></span><h4>接口说明</h4><p>查询已购买的音色状态信息，支持按<code>SpeakerIDs</code>和<code>State</code>过滤。<br>
如果查询条件为空，返回账号的AppID下所有的列表（音色超过1000，强烈建议使用分页接口<code>BatchListMegaTTSTrainStatus</code>）。<br><span id=b9c479bc></span><h4><strong>请求方式</strong></h4><p><code>POST</code><br><span id=225a69ce></span><h4>请求参数</h4><table class=volc-viewer-table><thead><tr><th><p>Parameter</p><th><p>Type</p><th><p>Must</p><th><p>Argument type</p><th><p>Description</p><tbody><tr><td><p>Content-Type</p><td><p>string</p><td><p>Y</p><td><p>header</p><td><p>固定字符串: application/json; charset=utf-8</p><tr><td><p>Action</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>ListMegaTTSTrainStatus</p><tr><td><p>Version</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>2023-11-07</p><tr><td><p>AppID</p><td><p>string</p><td><p>Y</p><td><p>body</p><td><p>AppID</p><tr><td><p>SpeakerIDs</p><td><p>[]string</p><td><p>N</p><td><p>body</p><td><p>SpeakerID的列表，如果忽略SpeakerIDs查询数据，强烈建议使用分页接口：BatchListMegaTTSTrainStatus</p><tr><td><p>State</p><td><p>string</p><td><p>N</p><td><p>body</p><td><p>音色状态，支持取值：Unknown、Training、Success、Active、Expired、Reclaimed<br>
详见附录：State状态枚举值</p><tr><td><p>OrderTimeStart</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>下单时间检索上边界毫秒级时间戳，受实例交付速度影响，可能比支付完成的时间晚</p><tr><td><p>OrderTimeEnd</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>下单时间检索下边界毫秒级时间戳，受实例交付速度影响，可能比支付完成的时间晚</p><tr><td><p>ExpireTimeStart</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>实例到期时间的检索上边界毫秒级时间戳</p><tr><td><p>ExpireTimeEnd</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>实例到期时间的检索下边界毫秒级时间戳</p></table><p><span id=8b872a49></span><h4>返回数据</h4><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
          <span class=hljs-attr>"ResponseMetadata"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"RequestId"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"20220214145719010211209131054BC103"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// header中的X-Top-Request-Id参数</span>
              <span class=hljs-attr>"Action"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"Service"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Service}"</span><span class=hljs-punctuation>,</span><span class=hljs-comment>// header中的X-Top-Service参数</span>
              <span class=hljs-attr>"Region"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Region}"</span> <span class=hljs-comment>// header中的X-Top-Region参数</span>
          <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"Result"</span><span class=hljs-punctuation>:</span><span class=hljs-punctuation>{</span>
                  <span class=hljs-attr>"Statuses"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
                         <span class=hljs-punctuation>{</span>
                              <span class=hljs-attr>"CreateTime"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1700727790000</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// unix epoch格式的创建时间，单位ms</span>
                              <span class=hljs-attr>"DemoAudio"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"https://example.com"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// http demo链接</span>
                              <span class=hljs-attr>"InstanceNO"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Model_storage_meUQ8YtIPm"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 火山引擎实例number</span>
                              <span class=hljs-attr>"IsActivable"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 是否可激活</span>
                              <span class=hljs-attr>"SpeakerID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_VYBmqB0A"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID</span>
                              <span class=hljs-attr>"State"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID的状态</span>
                              <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"V1"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID已训练过的次数</span>
                              <span class=hljs-attr>"ExpireTime"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1732895999000</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>//过期时间</span>
                              <span class=hljs-attr>"Alias"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>//别名，和控制台同步</span>
                              <span class=hljs-attr>"AvailableTrainingTimes"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>9</span> <span class=hljs-comment>//剩余训练次数，激活音色为0</span>
                              <span class=hljs-attr>"OrderTime"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1701771990000</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 下单时间，单位ms</span>
                        <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                        <span class=hljs-punctuation>{</span>
                              <span class=hljs-attr>"SpeakerID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_VYBmqB0B"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID</span>
                              <span class=hljs-attr>"State"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Unknown"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID的状态</span>
                        <span class=hljs-punctuation>}</span>
                  <span class=hljs-punctuation>]</span>
          <span class=hljs-punctuation>}</span>
      <span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=a8755e5b></span><h3 id=分页查询speakerid状态-batchlistmegattstrainstatus>分页查询SpeakerID状态 <code>BatchListMegaTTSTrainStatus</code></h3><p><span id=99293c5f></span><h4>接口说明</h4><p>查询已购买的音色状态；相比<code>ListMegaTTSTrainStatus</code> 增加了分页相关参数和返回；支持使用token和声明页数两种分页方式；其中，<ul><li>分页token在最后一页为空<li>分页token采用私有密钥进行加密<li>分页接口为新接口，不影响已有接口行为</ul><p><span id=1008d9c9></span><h4><strong>请求方式</strong></h4><p><code>POST</code><br><span id=4c708b2a></span><h4>请求参数</h4><table class=volc-viewer-table><thead><tr><th><p>Parameter</p><th><p>Type</p><th><p>Must</p><th><p>Argument type</p><th><p>Description</p><tbody><tr><td><p>Content-Type</p><td><td><p>Y</p><td><p>header</p><td><p>固定字符串: application/json; charset=utf-8</p><tr><td><p>Action</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>BatchListMegaTTSTrainStatus</p><tr><td><p>Version</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>2023-11-07</p><tr><td><p>AppID</p><td><p>string</p><td><p>Y</p><td><p>body</p><td><p>AppID</p><tr><td><p>SpeakerIDs</p><td><p>[]string</p><td><p>N</p><td><p>body</p><td><p>SpeakerID的列表，传空为返回指定APPID下的全部SpeakerID</p><tr><td><p>State</p><td><p>string</p><td><p>N</p><td><p>body</p><td><p>音色状态，支持取值：Unknown、Training、Success、Active、Expired、Reclaimed<br>
详见附录：State状态枚举值</p><tr><td><p>PageNumber</p><td><p>int</p><td><p>N</p><td><p>body</p><td><p>页数, 需大于0, 默认为1</p><tr><td><p>PageSize</p><td><p>int</p><td><p>N</p><td><p>body</p><td><p>每页条数, 必须在范围[1, 100]内, 默认为10</p><tr><td><p>NextToken</p><td><p>string</p><td><p>N</p><td><p>body</p><td><p>上次请求返回的字符串; 如果不为空的话, 将覆盖PageNumber及PageSize的值</p><tr><td><p>MaxResults</p><td><p>int</p><td><p>N</p><td><p>body</p><td><p>与NextToken相配合控制返回结果的最大数量; 如果不为空则必须在范围[1, 100]内, 默认为10</p><tr><td><p>OrderTimeStart</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>下单时间检索上边界毫秒级时间戳，受实例交付速度影响，可能比支付完成的时间晚</p><tr><td><p>OrderTimeEnd</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>下单时间检索下边界毫秒级时间戳，受实例交付速度影响，可能比支付完成的时间晚</p><tr><td><p>ExpireTimeStart</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>实例到期时间的检索上边界毫秒级时间戳</p><tr><td><p>ExpireTimeEnd</p><td><p>int64</p><td><p>N</p><td><p>body</p><td><p>实例到期时间的检索下边界毫秒级时间戳</p></table><p><span id=07ec8372></span><h4>返回数据</h4><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"ResponseMetadata"</span><span class=hljs-punctuation>:</span> 
    <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"RequestId"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"20220214145719010211209131054BC103"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// header中的X-Top-Request-Id参数</span>
        <span class=hljs-attr>"Action"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"BatchListMegaTTSTrainStatus"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"2023-11-07"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Service"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Service}"</span><span class=hljs-punctuation>,</span><span class=hljs-comment>// header中的X-Top-Service参数</span>
        <span class=hljs-attr>"Region"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Region}"</span> <span class=hljs-comment>// header中的X-Top-Region参数},</span>
        <span class=hljs-attr>"Result"</span><span class=hljs-punctuation>:</span>
        <span class=hljs-punctuation>{</span>
            <span class=hljs-attr>"AppID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"xxx"</span><span class=hljs-punctuation>,</span>
            <span class=hljs-attr>"TotalCount"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerIDs总数量</span>
            <span class=hljs-attr>"NextToken"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// NextToken字符串，可发送请求后面的结果; 如果没有更多结果将为空</span>
            <span class=hljs-attr>"PageNumber"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 使用分页参数时的当前页数</span>
            <span class=hljs-attr>"PageSize"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 使用分页参数时当前页包含的条数</span>
            <span class=hljs-attr>"Statuses"</span><span class=hljs-punctuation>:</span> 
            <span class=hljs-punctuation>[</span>
                <span class=hljs-punctuation>{</span>
                    <span class=hljs-attr>"CreateTime"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1700727790000</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// unix epoch格式的创建时间，单位ms</span>
                    <span class=hljs-attr>"DemoAudio"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"https://example.com"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// http demo链接</span>
                    <span class=hljs-attr>"InstanceNO"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Model_storage_meUQ8YtIPm"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 火山引擎实例Number</span>
                    <span class=hljs-attr>"IsActivable"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 是否可激活</span>
                    <span class=hljs-attr>"SpeakerID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_VYBmqB0A"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID</span>
                    <span class=hljs-attr>"State"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Success"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID的状态</span>
                    <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"V1"</span> <span class=hljs-comment>// speakerID已训练过的次数</span>
                    <span class=hljs-attr>"ExpireTime"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1964793599000</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 到期时间</span>
                    <span class=hljs-attr>"OrderTime"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1701771990000</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 下单时间</span>
                    <span class=hljs-attr>"Alias"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>""</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// 别名，和控制台同步</span>
                    <span class=hljs-attr>"AvailableTrainingTimes"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>10</span> <span class=hljs-comment>// 剩余训练次数</span>
                <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
                <span class=hljs-punctuation>{</span>
                    <span class=hljs-attr>"SpeakerID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"S_VYBmqB0B"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID</span>
                    <span class=hljs-attr>"State"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Unknown"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// speakerID的状态</span>
                    <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"V1"</span> <span class=hljs-comment>// speakerID已训练过的次数</span>
                <span class=hljs-punctuation>}</span>
            <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=7c43fc32></span><h3 id=音色下单orderaccessresourcepacks>音色下单<code>OrderAccessResourcePacks</code></h3><p><span id=7461c273></span><h4>接口说明</h4><p>一步下单音色并支付订单，前置条件：<ul><li><strong>AppID已经开通声音复刻</strong><li><strong>账户里面有足够的余额（或代金券），可以自动支付该订单</strong><li><strong>频率限制：2分钟内最多下单2000个音色</strong></ul><p><span id=1c84b987></span><h4><strong>请求方式</strong></h4><p><code>POST</code><br><span id=b4eac64b></span><h4>请求参数</h4><table class=volc-viewer-table><thead><tr><th><p>Parameter</p><th><p>Type</p><th><p>Must</p><th><p>Argument type</p><th><p>Description</p><tbody><tr><td><p>Content-Type</p><td><td><p>Y</p><td><p>header</p><td><p>固定字符串: application/json; charset=utf-8</p><tr><td><p>Action</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>OrderAccessResourcePacks</p><tr><td><p>Version</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>2023-11-07</p><tr><td><p>AppID</p><td><p>string</p><td><p>Y</p><td><p>body</p><td><p>AppID</p><tr><td><p>ResourceID</p><td><p>string</p><td><p>Y</p><td><p>body</p><td><p>平台的服务类型资源标识，必填且唯一：volc.megatts.voiceclone</p><tr><td><p>Code</p><td><p>string</p><td><p>Y</p><td><p>body</p><td><p>平台的计费项标识，必填且唯一：<br>
Model_storage</p><tr><td><p>Times</p><td><p>int</p><td><p>Y</p><td><p>body</p><td><p>下单单个音色的时长，单位为月</p><tr><td><p>Quantity</p><td><p>int</p><td><p>Y</p><td><p>body</p><td><p>下单音色的个数，如100，即为购买100个音色</p><tr><td><p>AutoUseCoupon</p><td><p>bool</p><td><p>N</p><td><p>body</p><td><p>是否自动使用代金券</p><tr><td><p>CouponID</p><td><p>string</p><td><p>N</p><td><p>body</p><td><p>代金券ID，通过<a href=https://www.volcengine.com/docs/6269/67339 target=_blank rel=noreferrer class=external>代金券管理</a>获取</p><tr><td><p>ResourceTag</p><td><p>object</p><td><p>N</p><td><p>body</p><td><p>项目&amp;标签账单配置</p><tr><td><p>ResourceTag.CustomTags</p><td><p>map[string]string</p><td><p>N</p><td><p>body</p><td><p>标签，通过<a href=https://www.volcengine.com/docs/6649/189381 target=_blank rel=noreferrer class=external>标签管理</a>获取</p><tr><td><p>ResourceTag.ProjectName</p><td><p>string</p><td><p>N</p><td><p>body</p><td><p>项目名称，通过<a href=https://www.volcengine.com/docs/6649/94336 target=_blank rel=noreferrer class=external>项目管理</a>获取</p></table><p><span id=db02ef6d></span><h4>请求示例</h4><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"AppID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"100000000"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"ResourceID"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"volc.megatts.voiceclone"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"Code"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"Model_storage"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"Times"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>12</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"Quantity"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2000</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=27634009></span><h4>返回数据</h4><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"ResponseMetadata"</span><span class=hljs-punctuation>:</span> 
    <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"RequestId"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"20220214145719010211209131054BC103"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// header中的X-Top-Request-Id参数</span>
        <span class=hljs-attr>"Action"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"OrderAccessResourcePacks"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"2023-11-07"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Service"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Service}"</span><span class=hljs-punctuation>,</span><span class=hljs-comment>// header中的X-Top-Service参数</span>
        <span class=hljs-attr>"Region"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Region}"</span> <span class=hljs-comment>// header中的X-Top-Region参数},</span>
        <span class=hljs-attr>"Result"</span><span class=hljs-punctuation>:</span>
        <span class=hljs-punctuation>{</span>
            <span class=hljs-attr>"OrderIDs"</span><span class=hljs-punctuation>:</span> 
            <span class=hljs-punctuation>[</span>
                <span class=hljs-string>"Order20010000000000000001"</span> <span class=hljs-comment>// 购买成功返回的订单号ID</span>
            <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=3a657453></span><h3 id=音色续费renewaccessresourcepacks>音色续费<code>RenewAccessResourcePacks</code></h3><p><span id=7b5015fb></span><h4>接口说明</h4><p>一步续费音色并支付订单，前置条件：<ul><li><strong>账户里面有足够的余额（或代金券），可以自动支付该订单</strong><li><strong>频率限制：2分钟内最多续费2000个音色</strong></ul><p><span id=45184772></span><h4><strong>请求方式</strong></h4><p><code>POST</code><br><span id=f2c357dd></span><h4>请求参数</h4><table class=volc-viewer-table><thead><tr><th><p>Parameter</p><th><p>Type</p><th><p>Must</p><th><p>Argument type</p><th><p>Description</p><tbody><tr><td><p>Content-Type</p><td><td><p>Y</p><td><p>header</p><td><p>固定字符串: application/json; charset=utf-8</p><tr><td><p>Action</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p><code>RenewAccessResourcePacks</code></p><tr><td><p>Version</p><td><p>string</p><td><p>Y</p><td><p>query</p><td><p>2023-11-07</p><tr><td><p>Times</p><td><p>int</p><td><p>Y</p><td><p>body</p><td><p>续费音色的时长，单位为月</p><tr><td><p>SpeakerIDs</p><td><p>[]string</p><td><p>N</p><td><p>body</p><td><p>要续费的SpeakerID的列表，可以通过<code>BatchListMegaTTSTrainStatus</code>接口过滤获取</p><tr><td><p>AutoUseCoupon</p><td><p>bool</p><td><p>N</p><td><p>body</p><td><p>是否自动使用代金券</p><tr><td><p>CouponID</p><td><p>string</p><td><p>N</p><td><p>body</p><td><p>代金券ID，通过<a href=https://www.volcengine.com/docs/6269/67339 target=_blank rel=noreferrer class=external>代金券管理</a>获取</p></table><p><span id=1b2c0e9f></span><h4>返回数据</h4><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"ResponseMetadata"</span><span class=hljs-punctuation>:</span> 
    <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"RequestId"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"20220214145719010211209131054BC103"</span><span class=hljs-punctuation>,</span> <span class=hljs-comment>// header中的X-Top-Request-Id参数</span>
        <span class=hljs-attr>"Action"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"OrderAccessResourcePacks"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Version"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"2023-11-07"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"Service"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Service}"</span><span class=hljs-punctuation>,</span><span class=hljs-comment>// header中的X-Top-Service参数</span>
        <span class=hljs-attr>"Region"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"{Region}"</span> <span class=hljs-comment>// header中的X-Top-Region参数},</span>
        <span class=hljs-attr>"Result"</span><span class=hljs-punctuation>:</span>
        <span class=hljs-punctuation>{</span>
            <span class=hljs-attr>"OrderIDs"</span><span class=hljs-punctuation>:</span> 
            <span class=hljs-punctuation>[</span>
                <span class=hljs-string>"Order20010000000000000001"</span> <span class=hljs-comment>// 购买成功返回的订单号ID</span>
            <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=c2b77147></span><h3 id=附录>附录</h3><p><span id=cc0d2106></span><h4>State状态枚举值</h4><table class=volc-viewer-table><thead><tr><th><p>State</p><th><p>Description</p><tbody><tr><td><p>Unknown</p><td><p>SpeakerID尚未进行训练</p><tr><td><p>Training</p><td><p>声音复刻训练中（长时间处于复刻中状态请联系火山引擎技术人员）</p><tr><td><p>Success</p><td><p>声音复刻训练成功，可以进行TTS合成</p><tr><td><p>Active</p><td><p>已激活（无法再次训练）</p><tr><td><p>Expired</p><td><p>火山控制台实例已过期或账号欠费</p><tr><td><p>Reclaimed</p><td><p>火山控制台实例已回收</p></table><p><span id=7e757ed2></span><h4>常见错误枚举值</h4><table class=volc-viewer-table><thead><tr><th><p>Error</p><th><p>Description</p><tbody><tr><td><p>InvalidParameter</p><td><p>请求参数错误</p><tr><td><p>Forbidden.InvalidService</p><td><p>未开通声音复刻</p><tr><td><p>Forbidden.ErrAccountNotPermission</p><td><p>账号没有权限</p><tr><td><p>Forbidden.LimitedTradingFrequency</p><td><p>下单限流错误</p><tr><td><p>InvalidParameter.AppID</p><td><p>AppID错误或者无效</p><tr><td><p>NotFound.ResourcePack</p><td><p>音色（或资源包）不存在</p><tr><td><p>InvalidParameter.InstanceNumber</p><td><p>无效的音色（或实例）</p></table></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/1204182><div class=head-N5m8>上一篇</div><div class=info-R5lO>声音复刻2.0-最佳实践</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/1136414><div class=head-N5m8>下一篇</div><div class=info-R5lO>火山引擎声音复刻协议</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>