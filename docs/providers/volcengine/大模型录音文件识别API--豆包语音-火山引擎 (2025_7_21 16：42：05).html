<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/1354868 
 saved date: Mon Jul 21 2025 16:42:05 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>大模型录音文件识别API--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=大模型录音文件识别API--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=大模型录音文件识别API--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><style>img[src="data:,"],source[src="data:,"]{display:none!important}</style><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 sidebarbot-Qz1r"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-13><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>语音识别大模型</span><span class=divider-DURY>/</span><span class=item-Dz0C>大模型录音文件识别API</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>大模型录音文件识别API</div><div class=info-TbRN><span>最近更新时间：2025.07.11 16:07:37</span><span>首次发布时间：2024.10.15 10:59:11</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><p><span id=bdae7a9d></span><div id=流程简介 class="md-h1 heading-h1">流程简介</div><p>大模型录音文件识别服务的处理流程分为提交任务和查询结果两个阶段<br>
任务提交：提交音频链接，并获取服务端分配的任务 ID<br>
结果查询：通过任务 ID 查询转写结果<div><img class=volc-image-img src=data:, width=368px></div><p><span id=8a0814b2></span><div id=提交任务 class="md-h1 heading-h1">提交任务</div><p><span id=45358956></span><h2 id=接口地址>接口地址</h2><p>火山地址：https://openspeech.bytedance.com/api/v3/auc/bigmodel/submit<br><span id=979caf11></span><h2 id=请求>请求</h2><p>请求方式：HTTP POST。<br>
请求和应答，均采用在 HTTP BODY 里面传输 JSON 格式字串的方式。<br>
Header 需要加入内容类型标识：<table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Api-App-Key</p><td><p>使用火山引擎控制台获取的APP ID，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a></p><td><p>123456789</p><tr><td><p>X-Api-Access-Key</p><td><p>使用火山引擎控制台获取的Access Token，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a></p><td><p>your-access-key</p><tr><td><p>X-Api-Resource-Id</p><td><p>表示调用服务的资源信息 ID，固定值volc.bigasr.auc</p><td><p>volc.bigasr.auc</p><tr><td><p>X-Api-Request-Id</p><td><p>用于提交和查询任务的任务ID，推荐传入随机生成的UUID</p><td><p>67ee89ba-7050-4c04-a3d7-ac61a63499b3</p><tr><td><p>X-Api-Sequence</p><td><p>发包序号，固定值，-1</p><td></table><p><span id=989b21ce></span><h3 id=请求字段>请求字段</h3><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>是否必填</p><th><p>备注</p><tbody><tr><td><p>user</p><td><p>用户相关配置</p><td><p>1</p><td><p>dict</p><td><td><tr><td><p>uid</p><td><p>用户标识</p><td><p>2</p><td><p>string</p><td><td><p>建议采用 IMEI 或 MAC。</p><tr><td><p>audio</p><td><p>音频相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>url</p><td><p>音频链接</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><tr><td><p>format</p><td><p>音频容器格式</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>raw / wav / mp3 / ogg</p><tr><td><p>codec</p><td><p>音频编码格式</p><td><p>2</p><td><p>string</p><td><td><p>raw / opus，默认为 raw(pcm) 。</p><tr><td><p>rate</p><td><p>音频采样率</p><td><p>2</p><td><p>int</p><td><td><p>默认为 16000。</p><tr><td><p>bits</p><td><p>音频采样点位数</p><td><p>2</p><td><p>int</p><td><td><p>默认为 16。</p><tr><td><p>channel</p><td><p>音频声道数</p><td><p>2</p><td><p>int</p><td><td><p>1(mono) / 2(stereo)，默认为1。</p><tr><td><p>request</p><td><p>请求相关配置</p><td><p>1</p><td><p>dict</p><td><p>✓</p><td><tr><td><p>model_name</p><td><p>模型名称</p><td><p>2</p><td><p>string</p><td><p>✓</p><td><p>目前只有bigmodel</p><tr><td><p>enable_itn</p><td><p>启用itn</p><td><p>2</p><td><p>bool</p><td><td><p>默认为true。<br>
文本规范化 (ITN) 是自动语音识别 (ASR) 后处理管道的一部分。 ITN 的任务是将 ASR 模型的原始语音输出转换为书面形式，以提高文本的可读性。<br>
例如，“一九七零年”-&gt;“1970年”和“一百二十三美元”-&gt;“$123”。</p><tr><td><p>enable_punc</p><td><p>启用标点</p><td><p>2</p><td><p>bool</p><td><td><p>默认为false。</p><tr><td><p>enable_ddc</p><td><p>启用顺滑</p><td><p>2</p><td><p>bool</p><td><td><p>默认为false。<br>
**<ins>语义顺滑</ins>**‌是一种技术，旨在提高自动语音识别（ASR）结果的文本可读性和流畅性。这项技术通过删除或修改ASR结果中的不流畅部分，如停顿词、语气词、语义重复词等，使得文本更加易于阅读和理解。</p><tr><td><p>enable_speaker_info</p><td><p>启用说话人聚类分离</p><td><p>2</p><td><p>bool</p><td><td><p>默认为false，开启后可返回说话人的信息，10人以内，效果较好。<br>
（如果音频存在音量、远近等明显变化，无法保证区分效果）</p><tr><td><p>enable_channel_split</p><td><p>启用双声道识别</p><td><p>2</p><td><p>bool</p><td><td><p>如果设为"True"，则会在返回结果中使用channel_id标记，1为左声道，2为右声道。默认 "False"默认为false</p><tr><td><p>show_utterances</p><td><p>输出语音停顿、分句、分词信息</p><td><p>2</p><td><p>bool</p><td><td><tr><td><p>vad_segment</p><td><p>使用vad分句</p><td><p>2</p><td><p>bool</p><td><td><p>默认为false，默认是语义分句。<br>
打开双声道识别时，通常需要使用vad分句，可同时打开此参数</p><tr><td><p>end_window_size</p><td><p>强制判停时间</p><td><p>2</p><td><p>int</p><td><td><p>范围300 - 5000ms，建议设置800ms或者1000ms，比较敏感的场景可以配置500ms或者更小。（如果配置的过小，则会导致分句过碎，配置过大会导致不容易将说话内容分开。建议依照自身场景按需配置）<br>
配置该值，不使用语义分句，根据静音时长来分句。</p><tr><td><p>sensitive_words_filter</p><td><p>敏感词过滤</p><td><p>2</p><td><p>string</p><td><td><p>敏感词过滤功能,支持开启或关闭,支持自定义敏感词。该参数可实现：不处理(默认,即展示原文)、过滤、替换为*。<br>
示例：<br>
system_reserved_filter //是否使用系统敏感词，会替换成*(默认系统敏感词主要包含一些限制级词汇）<br>
filter_with_empty // 想要替换成空的敏感词<br>
filter_with_signed // 想要替换成 * 的敏感词<pre class=hljs><code class="language-Python volc-pre-code hljs"><span class=hljs-string>"sensitive_words_filter"</span>:{\<span class=hljs-string>"system_reserved_filter\":true,\"filter_with_empty\":[\"敏感词\"],\"filter_with_signed\":[\"敏感词\"]}"</span>,
</code><div class="volc-pre-code-language no-copy">Python</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><tr><td><p>corpus</p><td><p>语料/干预词等</p><td><p>2</p><td><p>string</p><td><td><tr><td><p>boosting_table_name</p><td><p>自学习平台上设置的热词词表名称</p><td><p>3</p><td><p>string</p><td><td><p>热词功能和设置方法可以参考<a href=https://www.volcengine.com/docs/6561/155738 target=_blank rel=noreferrer class=external>文档</a></p><tr><td><p>context</p><td><p>上下文功能</p><td><p>3</p><td><p>string</p><td><td><p>热词直传，限制200 tokens<br>
"context":"{"hotwords":[{"word":"热词1号"}, {"word":"热词2号"}]}"<br>
上下文，限制800 tokens及20轮（含）内，超出会按照时间顺序从新到旧截断，优先保留更新的对话<br>
context_data字段按照从新到旧的顺序排列，以下是反序列化后的例子，传入需要序列化为jsonstring（转义引号）<pre class=hljs><code class="language-SQL volc-pre-code hljs">{
    "context_type":"dialog_ctx",
    "context_data":[
        {"text":"text1"},
        {"text":"text2"},
        {"text":"text3"},
        {"text":"text4"},
        ...
    ]
}
</code><div class="volc-pre-code-language no-copy">SQL</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><tr><td><p>callback</p><td><p>回调地址</p><td><p>1</p><td><p>string</p><td><td><p>举例：<pre class=hljs><code class="language-Plain volc-pre-code">"callback": "http://xxx"
</code><div class="volc-pre-code-language no-copy">Plain</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><tr><td><p>callback_data</p><td><p>回调信息</p><td><p>1</p><td><p>string</p><td><td><p>举例：<pre class=hljs><code class="language-Plain volc-pre-code">"callback_data":"$Request-Id"
</code><div class="volc-pre-code-language no-copy">Plain</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre></table><p>请求示例：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"user"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"uid"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"388808087185088"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"mp3"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"url"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"http://xxx.com/obj/sample.mp3"</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"request"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"model_name"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"bigmodel"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"enable_itn"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=950b1aef></span><h2 id=应答>应答</h2><p>Response header如下：<table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Tt-Logid</p><td><p>服务端返回的 logid，建议用户获取和打印方便定位问题</p><td><p>202407261553070FACFE6D19421815D605</p><tr><td><p>X-Api-Status-Code</p><td><p>提交任务后服务端返回的状态码，20000000表示提交成功，其他表示失败</p><td><tr><td><p>X-Api-Message</p><td><p>提交任务后服务端返回的信息，OK表示成功，其他表示失败</p><td></table><p>Response body为空<br><span id=aa510007></span><div id=查询结果 class="md-h1 heading-h1">查询结果</div><p><span id=9151467f></span><h2 id=接口地址-2>接口地址</h2><p>火山地址：https://openspeech.bytedance.com/api/v3/auc/bigmodel/query<br><span id=a5746fcf></span><h2 id=请求-2>请求</h2><p>请求方式：HTTP POST。<br>
请求和应答，均采用在 HTTP BODY 里面传输 JSON 格式字串的方式。<br>
Header 需要加入内容类型标识：<table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Api-App-Key</p><td><p>使用火山引擎控制台获取的APP ID，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a></p><td><p>123456789</p><tr><td><p>X-Api-Access-Key</p><td><p>使用火山引擎控制台获取的Access Token，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F rel=noreferrer>控制台使用FAQ-Q1</a></p><td><p>your-access-key</p><tr><td><p>X-Api-Resource-Id</p><td><p>表示调用服务的资源信息 ID，固定值volc.bigasr.auc</p><td><p>volc.bigasr.auc</p><tr><td><p>X-Api-Request-Id</p><td><p>用于提交和查询任务的任务ID。查询时需使用提交成功的任务Id</p><td><p>67ee89ba-7050-4c04-a3d7-ac61a63499b3</p></table><p>body为空json：<pre class=hljs><code class="language-Go volc-pre-code hljs">{}
</code><div class="volc-pre-code-language no-copy">Go</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=311c9941></span><h2 id=应答-2>应答</h2><p>Response header如下：<table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value 示例</p><tbody><tr><td><p>X-Tt-Logid</p><td><p>服务端返回的 logid，建议用户获取和打印方便定位问题</p><td><p>202407261553070FACFE6D19421815D605</p><tr><td><p>X-Api-Status-Code</p><td><p>提交任务后服务端返回的状态码，具体错误码参考下面错误码列表</p><td><tr><td><p>X-Api-Message</p><td><p>提交任务后服务端返回的信息，OK表示成功，其他表示失败</p><td></table><p>Response Body格式 ：JSON。<br>
应答字段：<table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>说明</p><th><p>层级</p><th><p>格式</p><th><p>备注</p><tbody><tr><td><p>result</p><td><p>识别结果</p><td><p>1</p><td><p>list</p><td><p>仅当识别成功时填写</p><tr><td><p>text</p><td><p>整个音频的识别结果文本</p><td><p>2</p><td><p>string</p><td><p>仅当识别成功时填写。</p><tr><td><p>utterances</p><td><p>识别结果语音分句信息</p><td><p>2</p><td><p>list</p><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>text</p><td><p>utterance级的文本内容</p><td><p>3</p><td><p>string</p><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>start_time</p><td><p>起始时间（毫秒）</p><td><p>3</p><td><p>int</p><td><p>仅当识别成功且开启show_utterances时填写。</p><tr><td><p>end_time</p><td><p>结束时间（毫秒）</p><td><p>3</p><td><p>int</p><td><p>仅当识别成功且开启show_utterances时填写。</p></table><p>应答示例：<br>
返回文本的形式：<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
  <span class=hljs-attr>"audio_info"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span><span class=hljs-attr>"duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>10000</span><span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"result"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
      <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这是字节跳动， 今日头条母公司。"</span><span class=hljs-punctuation>,</span>
      <span class=hljs-attr>"utterances"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
        <span class=hljs-punctuation>{</span>
          <span class=hljs-attr>"definite"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1705</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这是字节跳动，"</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>860</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>740</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"这"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1020</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>860</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"是"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1200</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1020</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"字"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1400</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1200</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"节"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1560</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1400</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"跳"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1640</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>1560</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"动"</span>
            <span class=hljs-punctuation>}</span>
          <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
        <span class=hljs-punctuation>{</span>
          <span class=hljs-attr>"definite"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>true</span></span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2110</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"今日头条母公司。"</span><span class=hljs-punctuation>,</span>
          <span class=hljs-attr>"words"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>[</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3070</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>2910</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"今"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3230</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3070</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"日"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3390</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3230</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"头"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3550</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3390</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"条"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3670</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3550</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"母"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3670</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"公"</span>
            <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
            <span class=hljs-punctuation>{</span>
              <span class=hljs-attr>"blank_duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"end_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"start_time"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span><span class=hljs-punctuation>,</span>
              <span class=hljs-attr>"text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"司"</span>
            <span class=hljs-punctuation>}</span>
          <span class=hljs-punctuation>]</span>
        <span class=hljs-punctuation>}</span>
      <span class=hljs-punctuation>]</span>
   <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
  <span class=hljs-attr>"audio_info"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"duration"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>3696</span>
  <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=e56afc6c></span><h2 id=错误码>错误码</h2><table class=volc-viewer-table><thead><tr><th><p>错误码</p><th><p>含义</p><th><p>说明</p><tbody><tr><td><p>20000000</p><td><p>成功</p><td><tr><td><p>20000001</p><td><p>正在处理中</p><td><tr><td><p>20000002</p><td><p>任务在队列中</p><td><tr><td><p>20000003</p><td><p>静音音频</p><td><p>返回该错误码无需重新query，直接重新submit</p><tr><td><p>45000001</p><td><p>请求参数无效</p><td><p>请求参数缺失必需字段 / 字段值无效 / 重复请求。</p><tr><td><p>45000002</p><td><p>空音频</p><td><tr><td><p>45000151</p><td><p>音频格式不正确</p><td><tr><td><p>550xxxx</p><td><p>服务内部处理错误</p><td><tr><td><p>55000031</p><td><p>服务器繁忙</p><td><p>服务过载，无法处理当前请求。</p></table><p><span id=f4d1642c></span><div id=demo class="md-h1 heading-h1">Demo</div><p>python:<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>auc_python.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><br>
Go：<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>auc_go.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><br>
Java:<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>java_client.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p></p></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/1354869><div class=head-N5m8>上一篇</div><div class=info-R5lO>大模型流式语音识别API</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/1631584><div class=head-N5m8>下一篇</div><div class=info-R5lO>大模型录音文件极速版识别API</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>