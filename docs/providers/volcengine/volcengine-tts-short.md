# 火山引擎短文本语音合成模型

## 模型概述

**模型ID**: `volcengine-tts-short`  
**模型类型**: 语音合成 (TTS)  
**接口类型**: HTTP REST API  
**适用场景**: 实时语音合成，文本长度限制1024字节  

## API接口规范

### 接口地址
```
POST https://openspeech.bytedance.com/api/v1/tts
```

### 认证方式
```http
Authorization: Bearer; {access_token}
Content-Type: application/json
```

### 请求参数

#### 基础配置
```json
{
  "app": {
    "appid": "your_app_id",
    "token": "access_token", 
    "cluster": "volcano_tts"
  },
  "user": {
    "uid": "user_identifier"
  }
}
```

#### 音频配置
```json
{
  "audio": {
    "voice_type": "BV700_streaming",
    "encoding": "mp3",
    "rate": 24000,
    "speed_ratio": 1.0,
    "volume_ratio": 1.0,
    "pitch_ratio": 1.0,
    "emotion": "happy",
    "language": "cn"
  }
}
```

#### 请求配置
```json
{
  "request": {
    "reqid": "unique_request_id",
    "text": "要合成的文本内容",
    "text_type": "plain",
    "operation": "query"
  }
}
```

### 支持的音频格式
- **编码格式**: wav, pcm, ogg_opus, mp3
- **采样率**: 8000, 16000, 24000 Hz
- **声道**: 单声道/立体声

### 响应格式

#### 成功响应
```json
{
  "reqid": "request_id",
  "code": 3000,
  "message": "Success",
  "sequence": -1,
  "data": "base64_encoded_audio_data",
  "addition": {
    "duration": "2500",
    "frontend": "timestamp_info"
  }
}
```

#### 错误响应
```json
{
  "reqid": "request_id",
  "code": 40000,
  "message": "请求参数错误",
  "sequence": -1
}
```

### 错误码说明
- **3000**: 成功
- **40000**: 请求参数错误
- **40001**: 没有可以合成的有效字符
- **40300**: 试用额度不足
- **50000**: 服务器错误

## 音色列表

### 中文通用音色
- **BV700_streaming**: 灿灿 - 支持22种情感
- **BV701_streaming**: 擎苍 - 适合有声阅读
- **BV001_streaming**: 通用女声
- **BV002_streaming**: 通用男声

### 多语种音色
- **BV421_streaming**: 天才少女 - 支持8国语言
- **BV503_streaming**: Ariana - 美式英语女声
- **BV504_streaming**: Jackson - 美式英语男声

### 方言音色
- **BV704_streaming**: 方言灿灿 - 支持8种方言
- **BV021_streaming**: 东北话-东北老铁
- **BV026_streaming**: 粤语-港剧男神

## 集成示例

### JavaScript/TypeScript
```typescript
interface VolcengineTTSRequest {
  app: {
    appid: string;
    token: string;
    cluster: string;
  };
  user: {
    uid: string;
  };
  audio: {
    voice_type: string;
    encoding: string;
    rate: number;
    speed_ratio?: number;
    volume_ratio?: number;
  };
  request: {
    reqid: string;
    text: string;
    operation: string;
  };
}

async function synthesizeText(request: VolcengineTTSRequest): Promise<string> {
  const response = await fetch('https://openspeech.bytedance.com/api/v1/tts', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer; ${request.app.token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(request)
  });
  
  const result = await response.json();
  if (result.code === 3000) {
    return result.data; // base64编码的音频数据
  }
  throw new Error(result.message);
}
```

### Python
```python
import requests
import base64
import json

def synthesize_text(text: str, voice_type: str = "BV700_streaming") -> bytes:
    url = "https://openspeech.bytedance.com/api/v1/tts"
    
    payload = {
        "app": {
            "appid": "your_app_id",
            "token": "your_access_token",
            "cluster": "volcano_tts"
        },
        "user": {
            "uid": "user_123"
        },
        "audio": {
            "voice_type": voice_type,
            "encoding": "mp3",
            "rate": 24000
        },
        "request": {
            "reqid": "unique_request_id",
            "text": text,
            "operation": "query"
        }
    }
    
    headers = {
        "Authorization": f"Bearer; {payload['app']['token']}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(url, json=payload, headers=headers)
    result = response.json()
    
    if result["code"] == 3000:
        return base64.b64decode(result["data"])
    else:
        raise Exception(result["message"])
```

## 性能特点

### 优势
- **低延迟**: 同步返回，适合实时场景
- **高质量**: 支持多种情感和语调
- **多语种**: 支持中英日等多种语言
- **易集成**: 标准HTTP接口

### 限制
- **文本长度**: 最大1024字节
- **并发限制**: 根据套餐限制QPS
- **音频格式**: wav不支持流式

## 计费说明

### 按字符计费
- **基础音色**: 5积分/100字符
- **情感音色**: 6积分/100字符
- **多语种音色**: 7积分/100字符

### 成本优化建议
1. 选择合适的音色类型
2. 去除不必要的标点符号
3. 合理设置音频参数
4. 实施缓存策略

## Cloudflare Pages集成

### 环境变量配置
```env
VOLCENGINE_APP_ID=your_app_id
VOLCENGINE_ACCESS_TOKEN=your_access_token
VOLCENGINE_CLUSTER=volcano_tts
```

### API路由实现
```typescript
// app/api/volcengine/tts/route.ts
export async function POST(request: Request) {
  const { text, voice, options } = await request.json();
  
  const ttsRequest = {
    app: {
      appid: process.env.VOLCENGINE_APP_ID,
      token: process.env.VOLCENGINE_ACCESS_TOKEN,
      cluster: process.env.VOLCENGINE_CLUSTER
    },
    user: { uid: "web_user" },
    audio: {
      voice_type: voice || "BV700_streaming",
      encoding: "mp3",
      rate: 24000,
      ...options
    },
    request: {
      reqid: crypto.randomUUID(),
      text: text,
      operation: "query"
    }
  };
  
  const audioData = await synthesizeText(ttsRequest);
  
  // 上传到R2存储或直接返回
  return new Response(audioData, {
    headers: { 'Content-Type': 'audio/mpeg' }
  });
}
```

## 最佳实践

### 1. 错误处理
```typescript
const handleTTSError = (error: any) => {
  switch (error.code) {
    case 40000:
      return "请求参数错误，请检查输入内容";
    case 40001:
      return "文本内容无效，请检查输入文本";
    case 40300:
      return "服务额度不足，请联系管理员";
    default:
      return "服务暂时不可用，请稍后重试";
  }
};
```

### 2. 请求优化
- 使用UUID生成唯一reqid
- 合理设置音频参数
- 实施请求重试机制
- 添加请求超时控制

### 3. 缓存策略
- 对相同文本进行缓存
- 使用CDN加速音频分发
- 实施本地存储优化
