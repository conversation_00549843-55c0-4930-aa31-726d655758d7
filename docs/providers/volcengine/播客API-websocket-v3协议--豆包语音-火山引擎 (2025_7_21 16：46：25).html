<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/1668014 
 saved date: Mon Jul 21 2025 16:46:25 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>播客API-websocket-v3协议--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=播客API-websocket-v3协议--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=播客API-websocket-v3协议--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><style>img[src="data:,"],source[src="data:,"]{display:none!important}</style><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 sidebarbot-Qz1r"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-5><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-13><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>语音播客大模型</span><span class=divider-DURY>/</span><span class=item-Dz0C>播客API-websocket-v3协议</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>播客API-websocket-v3协议</div><div class=info-TbRN><span>最近更新时间：2025.07.14 11:43:02</span><span>首次发布时间：2025.07.07 11:14:51</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><p><span id=c00407ce></span><div id=_1-接口功能 class="md-h1 heading-h1">1 接口功能</div><p>火山控制台开启试用：https://console.volcengine.com/speech/service/10028<p>对送入的播客主题文本进行分析，流式生成双人播客音频。<br>
支持断点续传。<p><span id=1669cae7></span><div id=_2-接口说明 class="md-h1 heading-h1">2 接口说明</div><p><span id=3c1a3afe></span><h2 id=_2-1-请求request>2.1 请求Request</h2><p><span id=1aaa2304></span><h3 id=请求路径>请求路径</h3><p><code>wss://openspeech.bytedance.com/api/v3/sami/podcasttts</code><br><span id=42b98f65></span><h3 id=建连-鉴权>建连&amp;鉴权</h3><p><span id=a0c6593e></span><h4>Request Headers</h4><table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>是否必须</p><th><p>Value示例</p><tbody><tr><td><p>X-Api-App-Id</p><td><p>使用火山引擎控制台获取的APP ID，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F target=_blank rel=noreferrer class=external>控制台使用FAQ-Q1</a></p><td><p>是</p><td><p>your-app-id</p><tr><td><p>X-Api-Access-Key</p><td><p>使用火山引擎控制台获取的Access Token，可参考 <a href=https://www.volcengine.com/docs/6561/196768#q1%EF%BC%9A%E5%93%AA%E9%87%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E5%88%B0%E4%BB%A5%E4%B8%8B%E5%8F%82%E6%95%B0appid%EF%BC%8Ccluster%EF%BC%8Ctoken%EF%BC%8Cauthorization-type%EF%BC%8Csecret-key-%EF%BC%9F target=_blank rel=noreferrer class=external>控制台使用FAQ-Q1</a></p><td><p>是</p><td><p>your-access-key</p><tr><td><p>X-Api-Resource-Id</p><td><p>表示调用服务的资源信息 ID<ul><li>播客语音合成：volc.service_type.10050</ul><td><p>是</p><td><ul><li>播客语音合成：volc.service_type.10050</ul><tr><td><p>X-Api-App-Key</p><td><p>固定值</p><td><p>是</p><td><p>aGjiRDfUWi</p><tr><td><p>X-Api-Request-Id</p><td><p>标识客户端请求ID，uuid随机字符串</p><td><p>否</p><td><p>67ee89ba-7050-4c04-a3d7-ac61a63499b3</p></table><p><span id=702d2796></span><h4>Response Headers</h4><table class=volc-viewer-table><thead><tr><th><p>Key</p><th><p>说明</p><th><p>Value示例</p><tbody><tr><td><p>X-Tt-Logid</p><td><p>服务端返回的 logid，建议用户获取和打印方便定位问题</p><td><p>2025041513355271DF5CF1A0AE0508E78C</p></table><p><span id=66d951ec></span><h3 id=websocket-二进制协议>WebSocket 二进制协议</h3><p>WebSocket 使用二进制协议传输数据。<br>
协议的组成由至少 4 个字节的可变 header、payload size 和 payload 三部分组成，其中<ul><li>header 描述消息类型、序列化方式以及压缩格式等信息；<li>payload size 是 payload 的长度；<li>payload 是具体负载内容，依据消息类型不同 payload 内容不同；</ul><p>需注意：协议中整数类型的字段都使用<strong>大端</strong>表示。<br><span id=b03f6f15></span><h5>二进制帧</h5><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th><p>说明</p><tbody><tr><td><p>0 - Left half</p><td><p>Protocol version</p><td><td><p>目前只有v1，始终填0b0001</p><tr><td><p>0 - Right half</p><td><td><p>Header size (4x)</p><td><p>目前只有4字节，始终填0b0001</p><tr><td><p>1 - Left half</p><td><p>Message type</p><td><td><p>固定为0b001</p><tr><td><p>1 - Right half</p><td><td><p>Message type specific flags</p><td><p>在sendText时，为0<br>
在finishConnection时，为0b100</p><tr><td><p>2 - Left half</p><td><p>Serialization method</p><td><td><p>0b0000：Raw（无特殊序列化方式，主要针对二进制音频数据）0b0001：JSON（主要针对文本类型消息）</p><tr><td><p>2 - Right half</p><td><td><p>Compression method</p><td><p>0b0000：无压缩0b0001：gzip</p><tr><td><p>3</p><td colspan=2><p>Reserved</p><td><p>留空（0b0000 0000）</p><tr><td><p>[4 ~ 7]</p><td colspan=2><p>[Optional field,like event number,...]</p><td><p>取决于Message type specific flags，可能有、也可能没有</p><tr><td><p>...</p><td colspan=2><p>Payload</p><td><p>可能是音频数据、文本数据、音频文本混合数据</p></table><p><span id=99690d1d></span><h6>payload请求参数</h6><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>描述</p><th><p>是否必须</p><th><p>类型</p><th><p>默认值</p><tbody><tr><td><p>action</p><td><p>生成类型：<ul><li>0：生成长文播客</ul><td><p>是</p><td><p>number</p><td><p>0</p><tr><td><p>input_text</p><td><p>待播客合成输入文本，模型截断 2.5w</p><td><p>是</p><td><p>string</p><td><p>——</p><tr><td><p>input_id</p><td><p>播客文本关联的唯一 id</p><td><p>否</p><td><p>string</p><td><p>——</p><tr><td><p>scene</p><td><p>播客场景，统计上使用<ul><li>deep_research</ul><td><p>否</p><td><p>string</p><td><p>deep_research</p><tr><td><p>use_head_music</p><td><p>是否使用开头音效</p><td><p>否</p><td><p>bool</p><td><p>false</p><tr><td><p>audio_config</p><td><p>音频参数，便于服务节省音频解码耗时</p><td><p>否</p><td><p>object</p><td><p>——</p><tr><td><p>audio_config.format</p><td><p>音频编码格式，mp3/ogg_opus/pcm/aac</p><td><p>否</p><td><p>string</p><td><p>pcm</p><tr><td><p>audio_config.sample_rate</p><td><p>音频采样率，可选值 [24000]</p><td><p>否</p><td><p>number</p><td><p>24000</p><tr><td><p>audio_config.speech_rate</p><td><p>语速，取值范围[-50,100]，100代表2.0倍速，-50代表0.5倍数</p><td><p>否</p><td><p>number</p><td><p>0</p><tr><td><p>retry_info.retry_task_id</p><td><p>前一个没获取完整的播客记录的 task_id(第一次StartSession使用的 session_id就是任务的 task_id)</p><td><p>否</p><td><p>string</p><td><p>——</p><tr><td><p>retry_info.last_finished_round_id</p><td><p>前一个获取完整的播客记录的轮次 id</p><td><p>否</p><td><p>number</p><td><p>——</p></table><p>示例<pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"input_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"test_podcast"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"input_text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"分析下当前的大模型发展"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"scene"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"deep_research"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"action"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"use_head_music"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>false</span></span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio_config"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"pcm"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"sample_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>24000</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"speech_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=867b5edf></span><h2 id=_2-2-响应response>2.2 响应Response</h2><p><span id=08d6433e></span><h3 id=建连响应>建连响应</h3><p>主要关注建连阶段 HTTP Response 的状态码和 Body<ul><li>建连成功：状态码为 200<li>建连失败：状态码不为 200，Body 中提供错误原因说明</ul><p><span id=f38fbebd></span><h3 id=websocket-传输响应>WebSocket 传输响应</h3><p><span id=fdc96a41></span><h4>二进制帧 - 正常响应帧</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th><p>说明</p><tbody><tr><td><p>0 - Left half</p><td><p>Protocol version</p><td><td><p>目前只有v1，始终填0b0001</p><tr><td><p>0 - Right half</p><td><td><p>Header size (4x)</p><td><p>目前只有4字节，始终填0b0001</p><tr><td><p>1 - Left half</p><td><p>Message type</p><td><td><p>音频帧返回：0b1011<br>
其他帧返回：0b1001</p><tr><td><p>1 - Right half</p><td><td><p>Message type specific flags</p><td><p>固定为0b0100</p><tr><td><p>2 - Left half</p><td><p>Serialization method</p><td><td><p>0b0000：Raw（无特殊序列化方式，主要针对二进制音频数据）0b0001：JSON（主要针对文本类型消息）</p><tr><td><p>2 - Right half</p><td><td><p>Compression method</p><td><p>0b0000：无压缩0b0001：gzip</p><tr><td><p>3</p><td colspan=2><p>Reserved</p><td><p>留空（0b0000 0000）</p><tr><td><p>[4 ~ 7]</p><td colspan=2><p>[Optional field,like event number,...]</p><td><p>取决于Message type specific flags，可能有、也可能没有</p><tr><td><p>...</p><td colspan=2><p>Payload</p><td><p>可能是音频数据、文本数据、音频文本混合数据</p></table><p><span id=3db9c08f></span><h5>payload响应参数</h5><table class=volc-viewer-table><thead><tr><th><p>字段</p><th><p>描述</p><th><p>类型</p><tbody><tr><td><p>data</p><td><p>返回的二进制数据包</p><td><p>byte</p><tr><td><p>event</p><td><p>返回的事件类型</p><td><p>number</p></table><p><span id=612af984></span><h4>二进制帧 - 错误响应帧</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th><p>说明</p><tbody><tr><td><p>0 - Left half</p><td><p>Protocol version</p><td><td><p>目前只有v1，始终填0b0001</p><tr><td><p>0 - Right half</p><td><td><p>Header size (4x)</p><td><p>目前只有4字节，始终填0b0001</p><tr><td><p>1</p><td><p>Message type</p><td><p>Message type specific flags</p><td><p>0b11110000</p><tr><td><p>2 - Left half</p><td><p>Serialization method</p><td><td><p>0b0000：Raw（无特殊序列化方式，主要针对二进制音频数据）0b0001：JSON（主要针对文本类型消息）</p><tr><td><p>2 - Right half</p><td><td><p>Compression method</p><td><p>0b0000：无压缩0b0001：gzip</p><tr><td><p>3</p><td colspan=2><p>Reserved</p><td><p>留空（0b0000 0000）</p><tr><td><p>[4 ~ 7]</p><td colspan=2><p>Error code</p><td><p>错误码</p><tr><td><p>...</p><td colspan=2><p>Payload</p><td><p>错误消息对象</p></table><p><span id=3cd5a26a></span><h2 id=_2-3-event定义>2.3 event定义</h2><p>在生成 podcast 阶段，不需要客户端发送上行的event帧。event类型如下：<table class=volc-viewer-table><thead><tr><th><p>Event code</p><th><p>含义</p><th><p>事件类型</p><th><p>应用阶段：上行/下行</p><tbody><tr><td><p>150</p><td><p>SessionStarted，会话任务开始</p><td><p>Session 类</p><td><p>下行</p><tr><td><p>360</p><td><p>PodcastSpeaker，播客返回新轮次内容开始，带着轮次 idx 和 speaker</p><td><p>数据类</p><td><p>下行</p><tr><td><p>361</p><td><p>PodcastTTSResponse，播客返回轮次的音频内容</p><td><p>数据类</p><td><p>下行</p><tr><td><p>362</p><td><p>PodcastTTSRoundEnd，播客返回内容当前轮次结束</p><td><p>数据类</p><td><p>下行</p><tr><td><p>152</p><td><p>SessionFinished，会话已结束（上行&amp;下行）<br>
标识语音一个完整的语音合成完成</p><td><p>Session 类</p><td><p>下行</p></table><p>在关闭连接阶段，需要客户端传递上行event帧去关闭连接。event类型如下：<table class=volc-viewer-table><thead><tr><th><p>Event code</p><th><p>含义</p><th><p>事件类型</p><th><p>应用阶段：上行/下行</p><tbody><tr><td><p>2</p><td><p>FinishConnection，结束连接</p><td><p>Connect 类</p><td><p>上行</p><tr><td><p>52</p><td><p>ConnectionFinished 结束连接成功</p><td><p>Connect 类</p><td><p>下行</p></table><p><strong>示意图（重要！！！！）</strong>：<br><img alt=Image class=volc-image-img src=data:, width=1500><br class=sf-hidden><span id=ff2a496b></span><h2 id=_2-4-不同类型帧举例说明>2.4 不同类型帧举例说明</h2><p><span id=e8b4a309></span><h3 id=startsession>StartSession</h3><p><span id=8c1de3b6></span><h4>请求 request</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>StartSession</p><td colspan=2><p>event type</p><tr><td><p>8 ~ 11</p><td><p>uint32(12)</p><td><td colspan=2><p>len(&lt;session_id&gt;)</p><tr><td><p>12 ~ 23</p><td><p>nxckjoejnkegf</p><td><td colspan=2><p>session_id</p><tr><td><p>24 ~ 27</p><td><p>uint32( ...)</p><td><td colspan=2><p>len(payload)</p><tr><td><p>28 ~ ...</p><td colspan=2><p>{}</p><td colspan=2><p><code>payload</code> 见下面的例子</p></table><p><code>payload</code><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"input_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"test_podcast"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"input_text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"分析下当前的大模型发展"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"scene"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"deep_research"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"action"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"use_head_music"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>false</span></span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio_params"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"pcm"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"sample_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>24000</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"speech_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p>断点续传的时候需要加上 retry 信息<br><code>payload</code><pre class=hljs><code class="language-JSON volc-pre-code hljs"><span class=hljs-punctuation>{</span>
    <span class=hljs-attr>"input_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"test_podcast"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"input_text"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"分析下当前的大模型发展"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"scene"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"deep_research"</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"action"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"use_head_music"</span><span class=hljs-punctuation>:</span> <span class=hljs-literal><span class=hljs-keyword>false</span></span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"audio_params"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"format"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"pcm"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"sample_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>24000</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"speech_rate"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>0</span><span class=hljs-punctuation>,</span>
    <span class=hljs-punctuation>}</span><span class=hljs-punctuation>,</span>
    <span class=hljs-attr>"retry_info"</span><span class=hljs-punctuation>:</span> <span class=hljs-punctuation>{</span>
        <span class=hljs-attr>"retry_task_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-string>"xxxxxxxxx"</span><span class=hljs-punctuation>,</span>
        <span class=hljs-attr>"last_finished_round_id"</span><span class=hljs-punctuation>:</span> <span class=hljs-number>5</span>
    <span class=hljs-punctuation>}</span>
<span class=hljs-punctuation>}</span>
</code><div class="volc-pre-code-language no-copy">JSON</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=189e205c></span><h4>响应Response</h4><p><span id=44d0f8d2></span><h5>SessionStarted</h5><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1011</p><td><p>0100</p><td><p>Audio-only response</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>SessionStarted</p><td><p>event type</p><td><tr><td><p>8 ~ 11</p><td colspan=2><p>uint32(12)</p><td><p>len(&lt;session_id&gt;)</p><td><tr><td><p>12 ~ 23</p><td colspan=2><p>nxckjoejnkegf</p><td><p>session_id</p><td><tr><td><p>24 ~ 27</p><td colspan=2><p>uint32( ...)</p><td><p>len(audio_binary)</p><td><tr><td><p>28 ~ ...</p><td colspan=2><p>{<br>
}</p><td><p>payload_json<br>
扩展保留，暂留空JSON</p><td></table><p>下面三个事件循环 ♻️,如果没有收到PodcastTTSRoundEnd（需要和PodcastSpeaker成对出现）就断掉了链接说明需要断点续传重新发起请求<br><span id=be25536d></span><h5>PodcastSpeaker</h5><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1011</p><td><p>0100</p><td><p>Audio-only response</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>PodcastSpeaker</p><td><p>event type</p><td><tr><td><p>8 ~ 11</p><td colspan=2><p>uint32(12)</p><td><p>len(&lt;session_id&gt;)</p><td><tr><td><p>12 ~ 23</p><td colspan=2><p>nxckjoejnkegf</p><td><p>session_id</p><td><tr><td><p>24 ~ 27</p><td colspan=2><p>uint32( ...)</p><td><p>len(audio_binary)</p><td><tr><td><p>28 ~ ...</p><td colspan=2><p>{<br>
"text_type": "", // 文本类型<br>
"speaker": "", // 本次说话speaker<br>
"round_id": -1, // 对话轮次，-1 是开头音乐<br>
"text": "" // 对话文本<br>
}</p><td><p>response_meta_json</p><td></table><p><span id=8ff44443></span><h5>PodcastTTSResponse</h5><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>PodcastTTSResponse</p><td colspan=2><p>event type</p><tr><td><p>8 ~ 11</p><td><p>uint32(12)</p><td><td colspan=2><p>len(&lt;session_id&gt;)</p><tr><td><p>12 ~ 23</p><td><p>nxckjoejnkegf</p><td><td colspan=2><p>session_id</p><tr><td><p>24 ~ 27</p><td><p>uint32( ...)</p><td><td colspan=2><p>len(payload)</p><tr><td><p>28 ~ ...</p><td colspan=2><p>... 音频内容</p><td colspan=2><p>payload</p></table><p><span id=00071d9e></span><h5>PodcastTTSRoundEnd</h5><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>PodcastTTSRoundEnd</p><td><p>event type</p><td><tr><td><p>8 ~ 11</p><td colspan=2><p>uint32(12)</p><td colspan=2><p>len(&lt;session_id&gt;)</p><tr><td><p>12 ~ 23</p><td colspan=2><p>nxckjoejnkegf</p><td colspan=2><p>session_id</p><tr><td><p>24 ~ 27</p><td colspan=2><p>uint32( ...)</p><td colspan=2><p>len(response_meta_json)</p><tr><td><p>28 ~ ...</p><td colspan=2><p>{<br>
"is_error": false,<br>
"error_msg": "ok"<br>
}</p><td colspan=2><p>response_meta_json</p></table><p><span id=4bdd490c></span><h3 id=finishsession>FinishSession</h3><p><span id=6b7beb11></span><h4>请求request</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>FinishSession</p><td colspan=2><p>event type</p><tr><td><p>8 ~ 11</p><td><p>uint32(12)</p><td><td colspan=2><p>len(&lt;session_id&gt;)</p><tr><td><p>12 ~ 23</p><td><p>nxckjoejnkegf</p><td><td colspan=2><p>session_id</p><tr><td><p>24 ~ 27</p><td><p>uint32( ...)</p><td><td colspan=2><p>len(payload)</p><tr><td><p>28 ~ ...</p><td colspan=2><p>{}</p><td colspan=2><p>tts_session_meta</p></table><p><span id=c0f24907></span><h4>响应response</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>SessionFinished</p><td colspan=2><p>event type</p><tr><td><p>8 ~ 11</p><td colspan=2><p>uint32(7)</p><td colspan=2><p>len(&lt;connection_id&gt;)</p><tr><td><p>12 ~ 15</p><td colspan=2><p>uint32(58)</p><td colspan=2><p>len(&lt;response_meta_json&gt;)</p><tr><td><p>28 ~ ...</p><td colspan=2><p>{<br>
"status_code": 20000000,<br>
"message": "ok"<br>
}</p><td colspan=2><p>response_meta_json<ul><li>仅含status_code和message字段</ul></table><p><span id=0ae4a5f4></span><h3 id=finishconnection>FinishConnection</h3><p><span id=479c59a9></span><h4>请求request</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>FinishConnection</p><td colspan=2><p>event type</p><tr><td><p>8 ~ 11</p><td colspan=2><p>uint32(2)</p><td colspan=2><p>len(&lt;response_meta_json&gt;)</p><tr><td><p>12 ~ 13</p><td colspan=2><p>{}</p><td colspan=2><p>tts_session_meta</p></table><p><span id=4f3476aa></span><h4>响应response</h4><table class=volc-viewer-table><thead><tr><th><p>Byte</p><th><p>Left 4-bit</p><th><p>Right 4-bit</p><th colspan=2><p>说明</p><tbody><tr><td><p>0</p><td><p>0001</p><td><p>0001</p><td><p>v1</p><td><p>4-byte header</p><tr><td><p>1</p><td><p>1001</p><td><p>0100</p><td><p>Full-client request</p><td><p>with event number</p><tr><td><p>2</p><td><p>0001</p><td><p>0000</p><td><p>JSON</p><td><p>no compression</p><tr><td><p>3</p><td><p>0000</p><td><p>0000</p><td><td><tr><td><p>4 ~ 7</p><td colspan=2><p>ConnectionFinished</p><td colspan=2><p>event type</p><tr><td><p>8 ~ 11</p><td colspan=2><p>uint32(7)</p><td colspan=2><p>len(&lt;connection_id&gt;)</p><tr><td><p>12 ~ 15</p><td colspan=2><p>uint32(58)</p><td colspan=2><p>len(&lt;response_meta_json&gt;)</p><tr><td><p>28 ~ ...</p><td colspan=2><p>{<br>
"status_code": 20000000,<br>
"message": "ok"<br>
}</p><td colspan=2><p>response_meta_json<ul><li>仅含status_code和message字段</ul></table><p><span id=a6e99449></span><div id=_3-错误码 class="md-h1 heading-h1">3 错误码</div><table class=volc-viewer-table><thead><tr><th><p>Code</p><th><p>Message</p><th><p>说明</p><tbody><tr><td><p>20000000</p><td><p>ok</p><td><p>音频合成结束的成功状态码</p><tr><td><p>45000000</p><td><p>quota exceeded for types: concurrency</p><td><p>并发限流，一般是请求并发数超过限制</p><tr><td><p>55000000</p><td><p>服务端一些error</p><td><p>服务端通用错误</p></table><p><span id=6049e8be></span><div id=_4-示例samples class="md-h1 heading-h1">4 示例Samples</div><p>Python：<br>
会自动处理断点续传拿到完整播客<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>volceengine_tts_stream.py</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><br>
Java：<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>volc-speech-java-sdk-feat_podcast.zip</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><p><p>输出 demo：<br><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>podcast.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><br>
分轮次音频：（下面的音频在第 8 轮断掉了，自动续上）<p><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_-1_head_music.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_0_zh_female_mizai_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_1_zh_male_dayi_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_2_zh_female_mizai_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_3_zh_male_dayi_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_4_zh_female_mizai_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_5_zh_male_dayi_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_6_zh_female_mizai_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_7_zh_male_dayi_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_8_zh_female_mizai_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_9_zh_male_dayi_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_10_zh_female_mizai_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div><div class=volc-attachment><div class="volc-attachment-container volc-flex-v-center"><div class="volc-flex volc-flex-1"><img class=volc-attachment-icon src=data:, alt><div class="volc-flex volc-flex-1"><div class=volc-attachment-name>round_11_zh_male_dayi_v2_saturn_bigtts.wav</div><div class=volc-attachment-desc>未知大小</div></div></div><section class="volc-attachment-down volc-attachment-link-container"><div class=volc-attachment-link><img src=data:, alt></div></section></div></div></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/1631586><div class=head-N5m8>上一篇</div><div class=info-R5lO>产品简介</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/1257543><div class=head-N5m8>下一篇</div><div class=info-R5lO>产品简介</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>