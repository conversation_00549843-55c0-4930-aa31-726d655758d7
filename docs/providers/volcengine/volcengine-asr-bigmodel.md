# 火山引擎大模型语音识别

## 模型概述

**模型ID**: `volcengine-asr-bigmodel`  
**模型类型**: 语音识别 (ASR)  
**接口类型**: HTTP REST API  
**适用场景**: 高精度语音转文字，支持多语种和方言  

## 模型特点

### 大模型优势
- **高精度**: 基于大模型技术，识别准确率更高
- **多语种**: 支持中英日韩法西葡印等多种语言
- **方言支持**: 支持粤语、四川话、上海话、东北话等
- **长音频**: 支持最长5小时的音频文件
- **智能标点**: 自动添加标点符号和语句分段

### 支持的语言和方言
- **中文普通话**: `zh`
- **粤语**: `yue`
- **四川话**: `sichuan`
- **上海话**: `shanghai`
- **英文**: `en`
- **日语**: `ja`
- **韩语**: `ko`
- **法语**: `fr`
- **西班牙语**: `es`
- **葡萄牙语**: `pt`
- **印尼语**: `id`

## API接口规范

### 任务提交接口
```
POST https://openspeech.bytedance.com/api/v3/auc/bigmodel/submit
```

### 结果查询接口
```
POST https://openspeech.bytedance.com/api/v3/auc/bigmodel/query
```

### 认证方式
```http
Authorization: Bearer; {access_token}
Resource-Id: volc.bigasr.sauc
Content-Type: application/json
```

## 任务提交

### 请求参数
```json
{
  "appid": "your_app_id",
  "reqid": "unique_request_id",
  "url": "https://your-domain.com/audio/sample.wav",
  "language": "zh",
  "use_itn": true,
  "use_capitalize": true,
  "max_lines": 1,
  "callback_url": "https://your-domain.com/api/volcengine/asr/callback"
}
```

### 参数说明
- **appid**: 应用ID
- **reqid**: 唯一请求ID
- **url**: 音频文件URL（公网可访问）
- **language**: 语言代码
- **use_itn**: 是否使用逆文本标准化（数字转换）
- **use_capitalize**: 是否使用首字母大写
- **max_lines**: 每行最大字符数（1-10000）
- **callback_url**: 回调地址（可选）

### 音频要求
- **格式支持**: wav、mp3、m4a、aac、ogg、flac
- **采样率**: 8000Hz-48000Hz
- **声道**: 单声道/立体声
- **时长**: 最长5小时
- **文件大小**: 最大500MB
- **比特率**: 建议128kbps以上

### 提交响应
```json
{
  "code": 10000,
  "message": "Success",
  "id": "task_uuid_12345"
}
```

## 结果查询

### 查询请求
```json
{
  "appid": "your_app_id",
  "id": "task_uuid_12345"
}
```

### 查询响应

#### 处理中
```json
{
  "code": 10000,
  "message": "Success",
  "id": "task_uuid_12345",
  "status": "processing"
}
```

#### 完成
```json
{
  "code": 10000,
  "message": "Success",
  "id": "task_uuid_12345",
  "status": "success",
  "result": {
    "text": "识别出的完整文本内容",
    "utterances": [
      {
        "text": "第一句话的内容",
        "start_time": 1000,
        "end_time": 3000,
        "words": [
          {
            "text": "第一",
            "start_time": 1000,
            "end_time": 1500
          },
          {
            "text": "句话",
            "start_time": 1500,
            "end_time": 2000
          }
        ]
      }
    ]
  }
}
```

#### 失败
```json
{
  "code": 40001,
  "message": "Audio download failed",
  "id": "task_uuid_12345",
  "status": "failed"
}
```

## 回调机制

### 回调请求格式
```json
{
  "id": "task_uuid_12345",
  "status": "success",
  "code": 10000,
  "message": "Success",
  "result": {
    "text": "识别出的完整文本内容",
    "utterances": [...]
  }
}
```

### 回调验证
```typescript
function verifyASRCallback(signature: string, body: string, secret: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(body)
    .digest('hex');
  return signature === expectedSignature;
}
```

## 集成示例

### JavaScript/TypeScript
```typescript
interface ASRBigModelRequest {
  appid: string;
  reqid: string;
  url: string;
  language: string;
  use_itn?: boolean;
  use_capitalize?: boolean;
  max_lines?: number;
  callback_url?: string;
}

interface ASRResult {
  text: string;
  utterances: Array<{
    text: string;
    start_time: number;
    end_time: number;
    words: Array<{
      text: string;
      start_time: number;
      end_time: number;
    }>;
  }>;
}

class VolcengineASRBigModel {
  private apiKey: string;
  private appId: string;
  private baseURL = 'https://openspeech.bytedance.com';

  constructor(apiKey: string, appId: string) {
    this.apiKey = apiKey;
    this.appId = appId;
  }

  async submitTask(request: ASRBigModelRequest): Promise<string> {
    const response = await fetch(`${this.baseURL}/api/v3/auc/bigmodel/submit`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.bigasr.sauc',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...request,
        appid: this.appId
      })
    });

    const result = await response.json();
    if (result.code === 10000) {
      return result.id;
    }
    throw new Error(result.message);
  }

  async queryResult(taskId: string): Promise<any> {
    const response = await fetch(`${this.baseURL}/api/v3/auc/bigmodel/query`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer; ${this.apiKey}`,
        'Resource-Id': 'volc.bigasr.sauc',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        appid: this.appId,
        id: taskId
      })
    });

    return await response.json();
  }

  async waitForCompletion(taskId: string, maxWaitTime = 600000): Promise<ASRResult> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const result = await this.queryResult(taskId);
      
      if (result.status === 'success') {
        return result.result;
      } else if (result.status === 'failed') {
        throw new Error(result.message);
      }
      
      // 等待5秒后重试
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    throw new Error('Task timeout');
  }

  async recognizeAudio(audioUrl: string, language = 'zh'): Promise<ASRResult> {
    const taskId = await this.submitTask({
      appid: this.appId,
      reqid: crypto.randomUUID(),
      url: audioUrl,
      language: language,
      use_itn: true,
      use_capitalize: true,
      max_lines: 1
    });

    return await this.waitForCompletion(taskId);
  }
}
```

### Python
```python
import requests
import time
import uuid
from typing import Dict, Optional

class VolcengineASRBigModel:
    def __init__(self, api_key: str, app_id: str):
        self.api_key = api_key
        self.app_id = app_id
        self.base_url = "https://openspeech.bytedance.com"
    
    def submit_task(self, audio_url: str, language: str = "zh", 
                   callback_url: Optional[str] = None) -> str:
        url = f"{self.base_url}/api/v3/auc/bigmodel/submit"
        
        payload = {
            "appid": self.app_id,
            "reqid": str(uuid.uuid4()),
            "url": audio_url,
            "language": language,
            "use_itn": True,
            "use_capitalize": True,
            "max_lines": 1
        }
        
        if callback_url:
            payload["callback_url"] = callback_url
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.bigasr.sauc",
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        result = response.json()
        
        if result["code"] == 10000:
            return result["id"]
        else:
            raise Exception(result["message"])
    
    def query_result(self, task_id: str) -> Dict:
        url = f"{self.base_url}/api/v3/auc/bigmodel/query"
        
        payload = {
            "appid": self.app_id,
            "id": task_id
        }
        
        headers = {
            "Authorization": f"Bearer; {self.api_key}",
            "Resource-Id": "volc.bigasr.sauc",
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        return response.json()
    
    def wait_for_completion(self, task_id: str, max_wait_time: int = 600) -> Dict:
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            result = self.query_result(task_id)
            
            if result["status"] == "success":
                return result["result"]
            elif result["status"] == "failed":
                raise Exception(result["message"])
            
            time.sleep(5)  # 等待5秒
        
        raise TimeoutError("Task timeout")
    
    def recognize_audio(self, audio_url: str, language: str = "zh") -> Dict:
        task_id = self.submit_task(audio_url, language)
        return self.wait_for_completion(task_id)
```

## 状态码说明

### 成功状态码
- **10000**: 成功

### 错误状态码
- **40001**: 音频下载失败
- **40002**: 音频格式不支持
- **40003**: 音频时长超限
- **40004**: 音频文件损坏
- **40005**: 语言不支持
- **50000**: 服务器内部错误

## 任务状态

### 状态类型
- **pending**: 等待处理
- **processing**: 处理中
- **success**: 成功完成
- **failed**: 处理失败

### 状态轮询建议
- 初始间隔：2秒
- 最大间隔：10秒
- 指数退避策略
- 最大等待时间：10分钟

## Cloudflare Pages集成

### API路由实现
```typescript
// app/api/volcengine/asr/submit/route.ts
export async function POST(request: Request) {
  const { audio_url, language, options } = await request.json();
  
  const asr = new VolcengineASRBigModel(
    process.env.VOLCENGINE_ACCESS_TOKEN!,
    process.env.VOLCENGINE_APP_ID!
  );
  
  const taskId = await asr.submitTask({
    appid: process.env.VOLCENGINE_APP_ID!,
    reqid: crypto.randomUUID(),
    url: audio_url,
    language: language || 'zh',
    callback_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/volcengine/asr/callback`,
    ...options
  });
  
  return Response.json({ task_id: taskId });
}

// app/api/volcengine/asr/query/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const taskId = searchParams.get('task_id');
  
  if (!taskId) {
    return Response.json({ error: 'Missing task_id' }, { status: 400 });
  }
  
  const asr = new VolcengineASRBigModel(
    process.env.VOLCENGINE_ACCESS_TOKEN!,
    process.env.VOLCENGINE_APP_ID!
  );
  
  const result = await asr.queryResult(taskId);
  return Response.json(result);
}

// app/api/volcengine/asr/callback/route.ts
export async function POST(request: Request) {
  const body = await request.json();
  
  // 处理识别结果
  if (body.status === 'success') {
    // 保存识别结果到数据库
    await saveASRResult(body.id, body.result);
  }
  
  return Response.json({ received: true });
}
```

## 最佳实践

### 1. 音频预处理
```typescript
function validateAudioFile(url: string): string[] {
  const errors: string[] = [];
  
  // 检查URL格式
  try {
    new URL(url);
  } catch {
    errors.push('无效的音频URL');
  }
  
  // 检查文件扩展名
  const supportedFormats = ['wav', 'mp3', 'm4a', 'aac', 'ogg', 'flac'];
  const extension = url.split('.').pop()?.toLowerCase();
  if (!extension || !supportedFormats.includes(extension)) {
    errors.push('不支持的音频格式');
  }
  
  return errors;
}
```

### 2. 结果后处理
```typescript
function processASRResult(result: ASRResult): {
  text: string;
  segments: Array<{text: string; start: number; end: number}>;
} {
  return {
    text: result.text,
    segments: result.utterances.map(utterance => ({
      text: utterance.text,
      start: utterance.start_time / 1000, // 转换为秒
      end: utterance.end_time / 1000
    }))
  };
}
```

### 3. 错误重试机制
```typescript
async function submitWithRetry(asr: VolcengineASRBigModel, request: ASRBigModelRequest, maxRetries = 3): Promise<string> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await asr.submitTask(request);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)));
    }
  }
  throw new Error('Max retries exceeded');
}
```

### 4. 成本优化
- 选择合适的音频质量和格式
- 使用回调机制减少轮询
- 实施音频缓存策略
- 合理设置max_lines参数
