<!DOCTYPE html> <html class=trancy-zh-CN lang=zh data-react-helmet=lang><!--
 Page saved with SingleFile 
 url: https://www.volcengine.com/docs/6561/104897 
 saved date: Mon Jul 21 2025 16:44:32 GMT+0800 (中国标准时间)
--><meta charset=utf-8><title>SSML标记语言--豆包语音-火山引擎</title><meta name=viewport content="width=device-width,initial-scale=1,shrink-to-fit=no,viewport-fit=cover,minimum-scale=1,maximum-scale=1,user-scalable=no"><meta http-equiv=x-ua-compatible content="ie=edge"><meta name=renderer content=webkit><meta name=layoutmode content=standard><meta name=imagemode content=force><meta name=wap-font-scale content=no><meta name=format-detection content="telephone=no"><meta viewport="width=device-width,maximum-scale=1,minimum-scale=1"><meta msapplication-tileimage=//portal.volccdn.com/obj/volcfe/misc/favicon.png><meta og:image=//portal.volccdn.com/obj/volcfe/misc/favicon.png><link rel=icon href=data:, sizes=192x192><meta bytedance-verification-code=BFlDDn5NtCfKBA015qLJ><meta referrer=always><meta name=keywords content=豆包语音 data-react-helmet=true><meta name=description content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-react-helmet=true><meta name=sharecontent data-msg-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-msg-title=SSML标记语言--豆包语音-火山引擎 data-msg-content=火山引擎官方文档中心，产品文档、快速入门、用户指南等内容，你关心的都在这里，包含火山引擎主要产品的使用手册、API或SDK手册、常见问题等必备资料，我们会不断优化，为用户带来更好的使用体验 data-msg-callback data-line-img=https://portal.volccdn.com/obj/volcfe/misc/favicon.png data-line-title=SSML标记语言--豆包语音-火山引擎 data-line-callback data-react-helmet=true><meta name=referrer content=no-referrer><link rel=icon href=data:, sizes=32x32><meta http-equiv=content-security-policy content="default-src 'none'; font-src 'self' data:; img-src 'self' data:; style-src 'unsafe-inline'; media-src 'self' data:; script-src 'unsafe-inline' data:; object-src 'self' data:; frame-src 'self' data:;"><body class="volcfe-hide-feelgood-survey volcfe-sidebar-mobile-bottom-fix"><plasmo-csui></plasmo-csui><link apple-touch-icon-precomposed=//portal.volccdn.com/obj/volcfe/misc/favicon.png><noscript>You need to enable JavaScript to run this app.</noscript><link rel=canonical href=https://www.volcengine.comundefined/ data-react-helmet=true><div id=root><div class=wrap-ZYMs><div class="root-h4Ln volcfe-flex" id=withRoot><div id=volcfe-nav-scroll-padding class=volcfe-nav-pc></div><div id=appbar data-version=4.0.4><div data-hidden=false id=volcfe-nav><div class="volcfe-flex-middle volcfe-nav-content volcfe-nav-theme-light"><div class=volcfe-nav-right><div class=volcfe-nav-search-wrap><div class="volcfe-flex-middle volcfe-nav-search"></div></div><div id=volcfe-nav-right><div class=volcfe-nav-usermenu-wrap data-active=false><div class="volcfe-nav-usermenu-mob-nav volcfe-nav-mobile" data-theme=light></div></div></div></div></div></div></div><div class="content-y9Pp volcfe-flex" id=app-content><div class=box-k7TH><div class=box-YLbM><div class=mshowbtn-dxyW> 导航</div><div class="sidebar-SoP1 false"><div class=content-ldPN><div class=searchwrap-i_F4><div role=combobox aria-haspopup=listbox aria-autocomplete=list aria-expanded=false tabindex=0 class="arco-select arco-select-single arco-select-show-search arco-select-size-default search-RqeY" aria-controls=arco-select-popup-0><div title class=arco-select-view><div aria-hidden=true class=arco-select-suffix><span class=arco-select-suffix-icon></span></div></div></div></div><div class=box-VqUF><div role=menu class="arco-menu arco-menu-light arco-menu-vertical menu-sYoy"><div class=arco-menu-inner><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-1><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-1><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-0><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-2><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-3><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-35><span class="arco-menu-icon-suffix is-open"></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-35><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-4><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-5><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-6><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-8><span class=arco-menu-icon-suffix></span></div><div class="arco-menu-inline-content arco-menu-inline-exit-done" id=arco-menu-0-submenu-inline-8><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-7><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-9><span class="arco-menu-icon-suffix is-open"></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class="arco-menu-inline-header arco-menu-selected" aria-controls=arco-menu-0-submenu-inline-13><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-13><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-11><span class="arco-menu-icon-suffix is-open"></span></div><div class="arco-menu-inline-content arco-menu-inline-enter-done" id=arco-menu-0-submenu-inline-11><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-10><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=true class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-12><span class="arco-menu-icon-suffix is-open"></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-20><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-20><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-14><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-15><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-16><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-17><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-18><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-19><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-23><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-23><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-21><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-22><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-24><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-34><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-34><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-30><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-30><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-26><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-26><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-25><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-28><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-28><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-27><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-29><span class=arco-menu-icon-suffix></span></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-33><span class=arco-menu-icon-suffix></span></div><div class=arco-menu-inline-content id=arco-menu-0-submenu-inline-33><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-31><span class=arco-menu-icon-suffix></span></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-32><span class=arco-menu-icon-suffix></span></div></div></div></div></div></div></div></div><div class="arco-menu-inline item-JA2c"><div tabindex=0 aria-expanded=false class=arco-menu-inline-header aria-controls=arco-menu-0-submenu-inline-36><span class=arco-menu-icon-suffix></span></div></div></div></div></div><div class=mclosebtn-Hz9J></div></div></div><div class="content-XH4l contentdoc-qaP7"><div class=box-sIN6><ul class=breadcrumb-ev36><li class=item-Dz0C>文档首页</li><span class=divider-DURY>/</span><span class=item-Dz0C>豆包语音</span><span class=divider-DURY>/</span><span class=item-Dz0C>开发参考</span><span class=divider-DURY>/</span><span class=item-Dz0C>音频生成</span><span class=divider-DURY>/</span><span class=item-Dz0C>SSML标记语言</span></ul><div class=inputbox-XiQh><div class="arco-input-group-wrapper arco-input-group-wrapper-default arco-input-has-suffix arco-input-search input-jHpV"><span class=arco-input-group><span class="arco-input-inner-wrapper arco-input-inner-wrapper-default"><input placeholder=在本产品文档中搜索 class="arco-input arco-input-size-default" value><span class=arco-input-group-suffix></span></span></span></div></div></div><div class=title-wrap-E0Mf><div><div class=title-M_b0>SSML标记语言</div><div class=info-TbRN><span>最近更新时间：2025.07.03 13:10:32</span><span>首次发布时间：2022.03.15 14:48:47</span></div></div><div class=wrap-ZgQx><div class=toolbar-aZII><div id=editor-foldbox></div><div class=divider-Xg3C></div><a class=favorite-ckJs href=https://www.volcengine.com/docs/favorite>我的收藏</a><div class=likewrap-DbBF><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>有用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div><div class=wideshow-AGRc><div class=btnwrap-Sj2x><div class=btn-BTHl><span>无用</span></div></div></div><div class="smalshow-SBCL sf-hidden"></div></div></div></div></div><div></div><div class=box-srhj><div class=contentfeedback-PaAn></div><div><div class="volc-md-viewer content-yaZD"><p><span id=_1-关于ssml></span><div id=_1-关于ssml class="md-h1 heading-h1">1. 关于SSML</div><p>语音合成标记语言（SSML：Speech Synthesis Markup Language），它是<a href=https://www.volcengine.com/docs/6561/104897 rel=noreferrer>W3C</a>的语音接口框架的一部分，通过SSML，可以对语音合成的效果进行定制化。<br><span id=_2-必读></span><div id=_2-必读 class="md-h1 heading-h1">2. 必读</div><div class="volc-custom-block-warning volc-custom-block"><p class=custom-block-title>注意<ul><li>接口传参时，请选择 text_type=ssml<li>所有文本 需放在 <code>&lt;speak&gt;&lt;/speak&gt;</code>标签之内<li>不同语种模型可使用的标签不同，请严格按照下表进行请求，否则会系统报错<li>当前仅支持中文普通话音色SSML调用，方言及小语种音色SSML调用后续会进行支持<li>使用ssml标签时合成字符不要超过150（包含标签本身），否则出现badcase概率会大大增加</ul></div><p><span id=_3-能力目录></span><div id=_3-能力目录 class="md-h1 heading-h1">3. 能力目录</div><table class=volc-viewer-table><thead><tr><th rowspan=2 colspan=2><p><strong>标签</strong></p><th rowspan=2><p><strong>分类值</strong></p><th rowspan=2><p><strong>作用</strong></p><th colspan=3><p><strong>支持的模型语种</strong></p><tr><th><p>中文/中英混</p><th><p>英文</p><th><p>日语</p><tbody><tr><td><p>根元素</p><td><p>speak</p><td><ul><li></ul><td><p>根元素</p><td><p>✅</p><td><p>✅</p><td><p>✅</p><tr><td rowspan=3><p>韵律停顿</p><td rowspan=2><p>break</p><td><p>strength</p><td><p>按照预设等级调整停顿时长</p><td><p>✅</p><td><td><tr><td><p>time</p><td><p>指定秒数调整停顿时长</p><td><p>✅</p><td><p>✅</p><td><p>✅</p><tr><td><p>word</p><td><ul><li></ul><td><p>指定分词位置</p><td><p>✅</p><td><td><tr><td><p>音频拼接</p><td><p>audio</p><td><ul><li></ul><td><p>拼接第三方音频文件</p><td><p>✅</p><td><p>✅</p><td><p>✅</p><tr><td rowspan=2><p>调节语速语调重音</p><td><p>prosody</p><td><ul><li></ul><td><p>局部文本变速、变调、变音量。</p><td><p>✅</p><td><p>✅</p><td><p>✅</p><tr><td><p>tobi</p><td><ul><li></ul><td><p>控制英语语调、重音、停顿时长</p><td><td><p>✅</p><td><tr><td rowspan=2><p>指定读音</p><td rowspan=2><p>phoneme</p><td><p>alphabet=py</p><td><p>通过中文拼音指定中文发音</p><td><p>✅</p><td><td><tr><td><p>alphabet=ipa</p><td><p>通过国际音标指定英文发音。常用于实现英语自然拼读、指定多音单词发音。</p><td><p>✅</p><td><p>✅</p><td><tr><td rowspan=7><p>指定说法</p><td rowspan=7><p>say-as</p><td><p>interpret-as="score"</p><td><p>冒号按照比例含义播报</p><td><p>✅</p><td><td><tr><td><p>interpret-as="time"</p><td><p>冒号按照时间含义播报</p><td><p>✅</p><td><td><tr><td><p>interpret-as="digits"</p><td><p>数字按照单个数字播报</p><td><p>✅</p><td><td><tr><td><p>interpret-as="number"</p><td><p>数字按照整体数字播报</p><td><p>✅</p><td><td><tr><td><p>interpret-as="telephone"</p><td><p>数字按照电话播报</p><td><p>✅</p><td><td><tr><td><p>interpret-as="address"</p><td><p>文本 按照地址播报</p><td><p>✅</p><td><td><tr><td><p>interpret-as="poetry"</p><td><p>文本按照古诗风格播报（仅特定模型有效）</p><td><p>✅</p><td><td><tr><td><p>指定情感</p><td><p>emotion</p><td><ul><li></ul><td><p>指定文本情感</p><td><p>✅</p><td><td></table><p><span id=_4-ssml支持-标签></span><div id=_4-ssml支持-标签 class="md-h1 heading-h1">4. SSML支持 标签</div><p><span id=_4-1-speak-根元素></span><h2 id=_4-1-speak-根元素>4.1 speak 根元素</h2><p><strong>描述</strong><br>
SSML的根元素，<strong>不论使用哪个ssml标签，都要有<code>&lt;speak&gt;&lt;/speak&gt;</code></strong>。<br><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>这句话是没有任何SSML语法的效果。<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><strong>注意事项</strong><ul><li><code>&lt;speak&gt;&lt;/speak&gt;</code>可以<strong>独立使用</strong>，不加其他任何标签，此时的<strong>没有任何特殊合成效果</strong><li><code>&lt;speak&gt;&lt;/speak&gt;</code>在每次合成请求中，<strong>只能有且只有一次出现</strong><li><code>&lt;speak&gt;&lt;/speak&gt;</code>一定在需要合成的所有文本（及标点）的<strong>最外面</strong></ul><p><strong>错误示范</strong><ul><li>❌错误示范1❌：一次请求包含2个及以上的<code>&lt;speak&gt;&lt;/speak&gt;</code></ul><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span> 南京市<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span> <span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span><span class=hljs-tag>&lt;<span class=hljs-name>word</span>&gt;</span>长江大桥<span class=hljs-tag>&lt;/<span class=hljs-name>word</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>❌错误示范2❌： <code>&lt;speak&gt;</code>标签外还有文本内容</ul><pre class=hljs><code class="language-Html volc-pre-code hljs">南京市 <span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span><span class=hljs-tag>&lt;<span class=hljs-name>word</span>&gt;</span>长江大桥<span class=hljs-tag>&lt;/<span class=hljs-name>word</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-2-break-调整停顿时长></span><h2 id=_4-2-break-调整停顿时长>4.2 break 调整停顿时长</h2><p><strong>描述</strong><br>
用于在文本中插入停顿，可以自定义停顿的时间长度。<br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p>参数</p><th><p>可选值</p><tbody><tr><td><p>strength</p><td><p>"x-weak", "weak", "medium", "strong", "x-strong"</p><tr><td><p>time</p><td><p>具体停顿时长，秒的绝对值，上限10s，可以精确到小数点后1位<ul><li>标签中间不能添加文本
<ul><li><ins>❌错误示范❌：</ins><ins><code>&lt;break time="2.5s"&gt;测试&lt;/break&gt;</code></ins></ul><li>当<code>&lt;break time="0s"&gt;&lt;/break&gt;</code>时，默认按照句号节奏停顿，如不需停顿，请删去该标签</ul></table><p><strong>注意事项</strong><ul><li>strength &amp; time <strong>不能同时指定</strong></ul><p><strong>示例</strong><ol><li>不添加break标签效果：</ol><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  今天天气很好
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV001为例</ul><ol start=2><li>使用 strength 示例</ol><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  今天<span class=hljs-tag>&lt;<span class=hljs-name>break</span> <span class=hljs-attr>strength</span>=<span class=hljs-string>"x-strong"</span>/&gt;</span>天气很好
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV001为例</ul><ol start=3><li>使用 time 示例</ol><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  今天天气<span class=hljs-tag>&lt;<span class=hljs-name>break</span> <span class=hljs-attr>time</span>=<span class=hljs-string>"2.5s"</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>break</span>&gt;</span>很好
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV001为例</ul><p><span id=_4-3-word-指定词语结构></span><h2 id=_4-3-word-指定词语结构>4.3 word 指定词语结构</h2><p><strong>描述</strong><br>
用于说明词语结构，使指定的词组不被拆分，只能加在<strong>中文</strong>上。<br><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  南京市<span class=hljs-tag>&lt;<span class=hljs-name>word</span>&gt;</span>长江大桥<span class=hljs-tag>&lt;/<span class=hljs-name>word</span>&gt;</span>。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV001为例</ul><p><strong>注意事项</strong><br><code>&lt;word&gt;&lt;/word&gt;</code>内不支持数字、非中文语种。<br><span id=_4-4-audio-拼接音频链接></span><h2 id=_4-4-audio-拼接音频链接>4.4 audio 拼接音频链接</h2><p><strong>描述</strong><br>
将TTS合成的语音与外部的音频文件进行拼接，拼接后作为一段完整音频返回。<br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p>名称</p><th><p>类型</p><th><p>是否必须</p><th><p>值/描述</p><tbody><tr><td><p>src</p><td><p>string</p><td><p>是</p><td><p>外部音频的url地址<ul><li>支持http和https<li>仅支持mp3格式<li>对拼接的音频<strong>没有</strong>采样率限制，TTS侧会做resample<li>文件大小<strong>不超过</strong>10M。音频越大，下载延迟越高。<li>标签中间不能添加文本。
<ul><li><ins>❌错误示范❌：</ins><ins><code>&lt;audio src="++ ++*音频url*++++"&gt;测试&lt;/audio&gt;</code></ins></ul></ul></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
苹果的英文是<span class=hljs-tag>&lt;<span class=hljs-name>audio</span> <span class=hljs-attr>src</span>=<span class=hljs-string>"https://sa-bucket.tos-cn-beijing.volces.com/apple.mp3"</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>audio</span>&gt;</span>你学会了吗？
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-5-调整语速、语调、音量></span><h2 id=_4-5-调整语速、语调、音量>4.5 调整语速、语调、音量</h2><p><span id=_4-5-1-句级别-变速、变调、变音量></span><h3 id=_4-5-1-句级别-变速、变调、变音量>4.5.1 句级别 变速、变调、变音量</h3><p><strong>描述</strong><br>
支持请求中局部文字的变速、变调、变音量。所有音色均支持。<br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p>名称</p><th><p>值</p><th><p>是否必须</p><th><p>描述</p><tbody><tr><td><p>speed</p><td><p>区间：0.5 - 2.0<br>
精度：小数点后一位</p><td><p>否</p><td><ul><li>改变速度<li>1.0为原速</ul><tr><td><p>volume</p><td><p>区间：0.5 - 2.0<br>
精度：小数点后一位</p><td><p>否</p><td><ul><li>改变音量<li>1.0为原音量</ul><tr><td><p>pitch</p><td><p>区间：0.5 - 2.0<br>
精度：小数点后一位</p><td><p>否</p><td><ul><li>改变音调<li>1.0为原调</ul></table><p><strong>注意事项</strong><ul><li>speed、volume、pitch顺序可以调换，三者必须有一。</ul><p><strong>示例</strong><pre class=hljs><code class="language-html volc-pre-code hljs language-xml">//示例1
<span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    这是原来的样子。
    <span class=hljs-tag>&lt;<span class=hljs-name>prosody</span> <span class=hljs-attr>speed</span>=<span class=hljs-string>"1.5"</span> &gt;</span>这是升速到1.5倍的样子<span class=hljs-tag>&lt;/<span class=hljs-name>prosody</span>&gt;</span> 
    <span class=hljs-tag>&lt;<span class=hljs-name>prosody</span> <span class=hljs-attr>speed</span>=<span class=hljs-string>"0.5"</span> &gt;</span>这是降速到0.5倍的样子<span class=hljs-tag>&lt;/<span class=hljs-name>prosody</span>&gt;</span>
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>

//示例2
<span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    这是原来的样子。
    <span class=hljs-tag>&lt;<span class=hljs-name>prosody</span> <span class=hljs-attr>volume</span>=<span class=hljs-string>"1.5"</span> &gt;</span>这是提高音量到1.5倍的样子<span class=hljs-tag>&lt;/<span class=hljs-name>prosody</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>prosody</span> <span class=hljs-attr>volume</span>=<span class=hljs-string>"0.5"</span> &gt;</span>这是降低音量到0.5倍的样子<span class=hljs-tag>&lt;/<span class=hljs-name>prosody</span>&gt;</span>
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>

//示例3
<span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    这是原来的样子。
    <span class=hljs-tag>&lt;<span class=hljs-name>prosody</span> <span class=hljs-attr>pitch</span>=<span class=hljs-string>"1.5"</span> &gt;</span>这是升调到1.5倍的样子<span class=hljs-tag>&lt;/<span class=hljs-name>prosody</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>prosody</span> <span class=hljs-attr>pitch</span>=<span class=hljs-string>"0.5"</span> &gt;</span>这是降调到0.5倍的样子<span class=hljs-tag>&lt;/<span class=hljs-name>prosody</span>&gt;</span>
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-5-2-英语-语调、-重音、停顿时长-控制></span><h3 id=_4-5-2-英语-语调、-重音、停顿时长-控制>4.5.2 &lt;英语&gt;语调、 重音、停顿时长 控制</h3><p><strong>描述</strong><br>
&lt; tobi &gt;用于指定<strong>英文</strong>音高重音、短语重音和边界调，只适用于<strong>英文音色</strong>。<br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p><strong>参数</strong></p><th><p><strong>值</strong></p><th><p><strong>描述</strong></p><tbody><tr><td><p>phrase_accent</p><td><ul><li>L-<li>H-</ul><td><ul><li>短语重音，控制单词的语调上扬、下降。<li>L-/H-，分别表示低/高。</ul><tr><td><p>boundary_tone</p><td><ul><li>L%<li>H%</ul><td><ul><li>边界调，控制单词的停顿时长。必须配合phrase_accent一起使用，不可单独出现。<li>L%/H%，分别表示低/高</ul><tr><td><p>accent_type</p><td><ul><li>L*<li>H*</ul><td><ul><li>音高重音，控制句子中的重音出现在哪个单词上。<li>L*/H*，分别表示低/高</ul></table><p><strong>注意事项</strong><ul><li>参数phrase_accent, accent_type，二者至少要有一个。<li>当需要参数boundary_tone时，必须提供参数phrase_accent。<li>&lt;tobi 后略&gt;xxx标签内<strong>有且只能有一个单词。</strong></ul><table class=volc-viewer-table><thead><tr><th><th><p><strong>写法</strong></p><th><p><strong>示例</strong></p><th><p><strong>是否可以合成</strong></p><tbody><tr><td rowspan=2><p><strong>✅ 正确示例</strong></p><td><p>单个单词</p><td><p>Yes. Whereare you from?</p><td><p>是，正常合成</p><tr><td><p>单个单词有空格</p><td><p>Yes. Whereare you from?</p><td><p>是，正常合成</p><tr><td rowspan=3><p><strong>❌ 错误示例</strong></p><td><p>多个单词</p><td><p>Yes.Where areyou from?</p><td><p>否，会报错</p><tr><td><p>单个单词有标点</p><td><p>Yes.Where，you from?</p><td><p>是，但标签会失效</p><tr><td><p>多个单词且单词内没有空格</p><td><p>Yes. Whereareare you from?</p><td><p>是，但TTS会将<strong>Whereare</strong>判断为一个单词进行发音预测，无意义</p></table><p><strong>示例：</strong><pre class=hljs><code class="language-html volc-pre-code hljs language-xml"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  We have a <span class=hljs-tag>&lt;<span class=hljs-name>tobi</span> <span class=hljs-attr>phrase_accent</span>=<span class=hljs-string>"L-"</span> <span class=hljs-attr>boundary_tone</span>=<span class=hljs-string>"L%"</span> <span class=hljs-attr>accent_type</span>=<span class=hljs-string>"L*"</span> &gt;</span><span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"laɪv"</span>&gt;</span>live<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>tobi</span>&gt;</span> show of a football match this Friday night.
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-6-phoneme-指定字词发音></span><h2 id=_4-6-phoneme-指定字词发音>4.6 phoneme 指定字词发音</h2><p><span id=_4-6-1-py-指定中文发音></span><h3 id=_4-6-1-py-指定中文发音>4.6.1 py 指定中文发音</h3><p><strong>描述</strong><br>
为标签内的文本指定发音，只能加在<strong>中文</strong>上。<br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p>名称</p><th><p>类型</p><th><p>值</p><th><p>是否必须</p><th><p>描述</p><tbody><tr><td><p>alphabet</p><td><p>String</p><td><p>py</p><td><p>是</p><td><p>代表拼音</p><tr><td><p>ph</p><td><p>String</p><td><p>标签内文本对应的拼音串String</p><td><p>是</p><td><p>当alphabet="py"时，ph用于指定拼音，多个拼音用<strong>空格</strong>分割。<ul><li>1~4 分别顺序对应汉语四个声调，5对应轻声。比如，词语“爸爸”，拼音应写为“ba4 ba5”<li>标签指定的拼音个数，必须与标签内中文字数相等
<ul><li><ins>❌错误示范❌：</ins><ins><code>&lt;phoneme alphabet="py" ph="yi1"&gt;依依&lt;/phoneme&gt;惜别。</code></ins></ul><li>标签内不能有标点符号、空格等非汉字内容
<ul><li><ins>❌错误示范❌：</ins><ins><code>&lt;phoneme alphabet="py" ph="ba4 ba5"&gt;爸爸！&lt;/phoneme&gt;快来！</code></ins></ul><li>拼音的ü需要写成v
<ul><li><ins>❌错误示范❌：</ins><ins><code>&lt;phoneme alphabet="py" ph="lü3"&gt;吕&lt;/phoneme&gt;</code></ins><li><ins>✅正确示范✅：</ins><ins><code>&lt;phoneme alphabet="py" ph="lv3"&gt;吕&lt;/phoneme&gt;</code></ins></ul><li>如果词语连读，导致发音控制不明显，增加<code>&lt;break&gt;</code>标签，增大字间停顿。但<code>&lt;phoneme&gt;</code>标签内不能嵌套<code>&lt;break&gt;</code>，需要每个字单独使用<code>&lt;phoneme&gt;</code></ul></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  《<span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"py"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"xi1 xi1"</span>&gt;</span>茜茜<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>公主》是奥地利拍摄的历史题材的德语三部曲电影。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV001为例</ul><p><span id=_4-6-2-ipa-指定「英文」发音></span><h3 id=_4-6-2-ipa-指定「英文」发音>4.6.2 ipa 指定「英文」发音</h3><p><strong>描述</strong><br>
通过<strong>国际音标</strong>指定英文发音，实现指定同型不同音的单词发音，也可以实现自然拼读效果。<br><strong>✨ 特别说明✨</strong><br>
我们调研了牛津、剑桥、有道等常用词典，发现均使用了类似的教学IPA体系对产品内的英语进行标注，但彼此间又有一定出入。为了保证<strong>发音指定严谨</strong>，火山引擎目前采用的是<strong>学术IPA集</strong>。下方提供了<a href=https://en.wikipedia.org/wiki/International_Phonetic_Alphabet target=_blank rel=noreferrer class=external>学术IPA</a>、教学IPA、<a href=http://www.speech.cs.cmu.edu/cgi-bin/cmudict target=_blank rel=noreferrer class=external>CMU</a>的对照关系表，使用前需要确认传入的IPA是否正确哦~<br>
举例：<ul><li>学术IPA集里 /ɹ/，在常见词典多使用 /r/ 进行标注。需要注意区分。<li>学术IPA集里/ɛ/，在常见词典多使用 /e/ 进行标注，只有牛津使用的是标准的学术IPA /ɛ/。需要注意区分。<li>学术IPA集里/ɚ/，在常见词典多使用 /ər/ 进行标注，只有剑桥使用的是标准的学术IPA /ɚ/。需要注意区分。</ul><p><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p>名称</p><th><p>类型</p><th><p>值</p><th><p>是否必须</p><th><p>描述</p><tbody><tr><td><p>alphabet</p><td><p>String</p><td><p>ipa</p><td><p>是</p><td><p>代表国际音标，注意小写</p><tr><td><p>ph</p><td><p>String</p><td><p>标签内文本对应的音标串String</p><td><p>是</p><td><p>当alphabet="ipa"时，ph用于指定国际音标。<ul><li>标签内不限定字母是否为一个真正的单词，以“ph”的值为准。
<ul><li>比如 <code>：&lt;phoneme alphabet="ipa" ph="ə"&gt;qweasd&lt;/phoneme&gt;</code></ul><li>标签内不能有标点符号、空格等非英语字母内容。<li><ins>❌错误示范❌：</ins><ins><code>&lt;phoneme alphabet="py" ph="ba4 ba5"&gt;爸爸！&lt;/phoneme&gt;快来！</code></ins></ul></table><p><strong>示例1</strong> <strong>- 音标</strong>（仅支持部分音色）<pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"æ"</span>&gt;</span>a<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>，apple.是苹果的意思.
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><strong>示例2</strong> <strong>- 单词</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  We have a <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"laɪv"</span>&gt;</span>live<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span> show of a football match this Friday night.
  We used to <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"lɪv"</span>&gt;</span>live<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>in London .
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><strong>示例3 - 词重音</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"fəˈnæns"</span>&gt;</span>finance<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span> .
    <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"ˈfaɪˈnæns"</span>&gt;</span>finance<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>.
    <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"ˈfaɪnæns"</span>&gt;</span>finance<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>.
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><strong>示例4 - 自然拼读</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"æ"</span>&gt;</span>a<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>break</span> <span class=hljs-attr>time</span>=<span class=hljs-string>"0.5s"</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>break</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>phoneme</span> <span class=hljs-attr>alphabet</span>=<span class=hljs-string>"ipa"</span> <span class=hljs-attr>ph</span>=<span class=hljs-string>"æ"</span>&gt;</span>a<span class=hljs-tag>&lt;/<span class=hljs-name>phoneme</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>break</span> <span class=hljs-attr>time</span>=<span class=hljs-string>"0.5s"</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>break</span>&gt;</span>
    apple
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><table class=volc-viewer-table><thead><tr><th rowspan=2><th rowspan=2><p>学术IPA<br>
（ssml支持的）</p><th rowspan=2><p>教学IPA<br>
（市面常见的）</p><th rowspan=2><p>CMU</p><th colspan=4><p>范例</p><tr><th><p>单词</p><th><p>学术IPA</p><th><p>教学IPA</p><th><p>CMU</p><tbody><tr><td rowspan=16><p>元音</p><td><p>/i/</p><td><p>/iː/</p><td><p>IY</p><td><p>me</p><td><p>/mi/</p><td><p>/miː/</p><td><p>M IY</p><tr><td><p>/ɪ/</p><td><p>/ɪ/</p><td><p>IH</p><td><p>fit</p><td><p>/fɪt/</p><td><p>/fɪt/</p><td><p>F IH T</p><tr><td><p>/ɛ/</p><td><p>/e/</p><td><p>EH</p><td><p>met</p><td><p>/mɛt/</p><td><p>/met/</p><td><p>M EH T</p><tr><td><p>/æ/</p><td><p>/æ/</p><td><p>AE</p><td><p>act</p><td><p>/ækt/</p><td><p>/ækt/</p><td><p>AE K T</p><tr><td><p>/ɑ/<br>
ɑ</p><td><p>/aː/</p><td><p>AA</p><td><p>father</p><td><p>/'faðɚ/</p><td><p>/'fa:ðər/</p><td><p>F AA DH ER</p><tr><td><p>/ɔ/</p><td><p>/ɔː/</p><td><p>AO</p><td><p>claw</p><td><p>/klɔ/</p><td><p>/klɔː/</p><td><p>K L AO</p><tr><td><p>/ʊ/</p><td><p>/ʊ/</p><td><p>UH</p><td><p>put</p><td><p>/pʊt/</p><td><p>/pʊt/</p><td><p>P UH T</p><tr><td><p>/u/</p><td><p>/uː/</p><td><p>UW</p><td><p>too</p><td><p>/tu/</p><td><p>/tuː/</p><td><p>T UW</p><tr><td><p>/ʌ/</p><td><p>/ʌ/</p><td><p>AH</p><td><p>fund</p><td><p>/fʌnd/</p><td><p>/fʌnd/</p><td><p>F AH N D</p><tr><td><p>/ə/</p><td><p>/ə/</p><td><p>AH</p><td><p>about</p><td><p>/ə'baʊt/</p><td><p>/ə'baʊt/</p><td><p>AH B AW T</p><tr><td><p>/ɚ/</p><td><p>/ər/</p><td><p>ER</p><td><p>better</p><td><p>/'bɛtɚ/</p><td><p>/'betər/</p><td><p>B EH T ER</p><tr><td><p>/ɝ/<br>
/aɪ/</p><td><p>/ɜːr/<br>
/aɪ/</p><td><p>ER<br>
AY</p><td><p>flirt<br>
cry</p><td><p>/'flɝt/<br>
/kraɪ/</p><td><p>/'flɜːrt/<br>
/kraɪ/</p><td><p>F L ER T<br>
K R AY</p><tr><td><p>/aʊ/</p><td><p>/aʊ/</p><td><p>AW</p><td><p>out</p><td><p>/aʊt/</p><td><p>/aʊt/</p><td><p>AW T</p><tr><td><p>/eɪ/</p><td><p>/eɪ/</p><td><p>EY</p><td><p>train</p><td><p>/treɪn/</p><td><p>/treɪn/</p><td><p>T R EY N</p><tr><td><p>/ɔɪ/</p><td><p>/ɔɪ/</p><td><p>OY</p><td><p>boy</p><td><p>/bɔɪ/</p><td><p>/bɔɪ/</p><td><p>B OY</p><tr><td><p>/oʊ/</p><td><p>/oʊ/</p><td><p>OW</p><td><p>boat</p><td><p>/boʊt/</p><td><p>/boʊt/</p><td><p>B OW T</p><tr><td rowspan=23><p>辅音</p><td><p>/p/</p><td><p>/p/</p><td><p>P</p><td><p>pen</p><td><p>/pɛn/</p><td><p>/pen/</p><td><p>P EH N</p><tr><td><p>/b/</p><td><p>/b/</p><td><p>B</p><td><p>bad</p><td><p>/bæd/</p><td><p>/bæd/</p><td><p>B AE D</p><tr><td><p>/t/</p><td><p>/t/</p><td><p>T</p><td><p>tea</p><td><p>/ti/</p><td><p>/tiː/</p><td><p>T IY</p><tr><td><p>/d/</p><td><p>/d/</p><td><p>D</p><td><p>did</p><td><p>/dɪd/</p><td><p>/dɪd/</p><td><p>D IH D</p><tr><td><p>/k/</p><td><p>/k/</p><td><p>K</p><td><p>cat</p><td><p>/kæt/</p><td><p>/kæt/</p><td><p>K AE T</p><tr><td><p>/ɡ/<br>
/tʃ/</p><td><p>/g/<br>
/tʃ/</p><td><p>G<br>
CH</p><td><p>get<br>
chain</p><td><p>/gɛt/<br>
/tʃeɪn/</p><td><p>/get/<br>
/tʃeɪn/</p><td><p>G EH T<br>
CH EY N</p><tr><td><p>/dʒ/</p><td><p>/dʒ/ 或 /ʤ/</p><td><p>JH</p><td><p>jam</p><td><p>/dʒæm/</p><td><p>/dʒæm/</p><td><p>JH AE M</p><tr><td><p>/f/</p><td><p>/f/</p><td><p>F</p><td><p>fall</p><td><p>/fɔl/</p><td><p>/fɔːl/</p><td><p>F AO L</p><tr><td><p>/v/</p><td><p>/v/</p><td><p>V</p><td><p>van</p><td><p>/væn/</p><td><p>/væn/</p><td><p>V AE N</p><tr><td><p>/θ/</p><td><p>/θ/</p><td><p>TH</p><td><p>thin</p><td><p>/θɪn/</p><td><p>/θɪn/</p><td><p>TH IH N</p><tr><td><p>/ð/</p><td><p>/ð/</p><td><p>DH</p><td><p>this</p><td><p>/ðɪs/</p><td><p>/ðɪs/</p><td><p>DH IH S</p><tr><td><p>/s/</p><td><p>/s/</p><td><p>S</p><td><p>see</p><td><p>/si/</p><td><p>/siː/</p><td><p>S IY</p><tr><td><p>/z/</p><td><p>/z/</p><td><p>Z</p><td><p>zoo</p><td><p>/zu/</p><td><p>/zuː/</p><td><p>Z UW</p><tr><td><p>/ʃ/</p><td><p>/ʃ/</p><td><p>SH</p><td><p>shoe</p><td><p>/ʃu/</p><td><p>/ʃuː/</p><td><p>SH UW</p><tr><td><p>/ʒ/</p><td><p>/ʒ/</p><td><p>ZH</p><td><p>vision</p><td><p>/'vɪʒn/</p><td><p>/'vɪʒn/</p><td><p>V IH ZH N</p><tr><td><p>/h/</p><td><p>/h/</p><td><p>HH</p><td><p>hat</p><td><p>/hæt/</p><td><p>/hæt/</p><td><p>H AE T</p><tr><td><p>/m/</p><td><p>/m/</p><td><p>M</p><td><p>man</p><td><p>/mæn/</p><td><p>/mæn/</p><td><p>M AE N</p><tr><td><p>/n/</p><td><p>/n/</p><td><p>N</p><td><p>now</p><td><p>/naʊ/</p><td><p>/naʊ/</p><td><p>N AW</p><tr><td><p>/ŋ/</p><td><p>/ŋ/</p><td><p>NG</p><td><p>sing</p><td><p>/sɪŋ/</p><td><p>/sɪŋ/</p><td><p>S IH NG</p><tr><td><p>/j/</p><td><p>/j/</p><td><p>Y</p><td><p>yes</p><td><p>/jɛs/</p><td><p>/jes/</p><td><p>Y EH S</p><tr><td><p>/w/</p><td><p>/w/</p><td><p>W</p><td><p>wet</p><td><p>/wɛt/</p><td><p>/wet/</p><td><p>W EH T</p><tr><td><p>/ɹ/</p><td><p>/r/</p><td><p>R</p><td><p>red</p><td><p>/ɹɛd/</p><td><p>/red/</p><td><p>R EH D</p><tr><td><p>/l/</p><td><p>/l/</p><td><p>L</p><td><p>leg</p><td><p>/lɛg/</p><td><p>/leg/</p><td><p>L EH G</p></table><p><span id=_4-7-say-as-根据「信息类型」指定读法></span><h2 id=_4-7-say-as-根据「信息类型」指定读法>4.7 say-as 根据「信息类型」指定读法</h2><p><strong>描述</strong><br>
根据信息类型指定标签参数，使之按照该类型常见的发音方式进行合成。<br><strong>语法说明</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"string"</span>&gt;</span>text<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><p><span id=_4-7-1-score-冒号按照「比例-比分」播报></span><h3 id=_4-7-1-score-冒号按照「比例-比分」播报>4.7.1 score 冒号按照「比例/比分」播报</h3><p><strong>描述</strong><table class=volc-viewer-table><thead><tr><th><p>示例</p><th><p>播报方式</p><tbody><tr><td><p>1:2</p><td><p>一比二</p><tr><td><p>1:11</p><td><p>一比十一</p><tr><td><p>1:2:3</p><td><p>一比二比三</p><tr><td><p>0:4:0</p><td><p>零比四比零</p><tr><td><p>12:15</p><td><p>十二比十五</p><tr><td><p>A:B:C</p><td><p>A比B比C</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  这场比赛最终以<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"score"</span>&gt;</span>12:15<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>告终。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>

</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果： 以BV010为例</ul><p><span id=_4-7-2-time-冒号按照「时间」播报></span><h3 id=_4-7-2-time-冒号按照「时间」播报>4.7.2 time 冒号按照「时间」播报</h3><p><strong>描述</strong><table class=volc-viewer-table><thead><tr><th><p>格式</p><th><p>示例</p><th><p>播报方式</p><tbody><tr><td rowspan=3><p>H:mm</p><td><p>5:00</p><td><p>五点</p><tr><td><p>5:01</p><td><p>五点零一分</p><tr><td><p>5:40</p><td><p>五点四十分</p><tr><td rowspan=6><p>HH:mm</p><td><p>05:00</p><td><p>五点</p><tr><td><p>12:00</p><td><p>十二点</p><tr><td><p>12:01</p><td><p>十二点零一分</p><tr><td><p>12:15</p><td><p>十二点十五分</p><tr><td><p>12:45</p><td><p>十二点四十五分</p><tr><td><p>22:31</p><td><p>二十二点三十一分</p><tr><td rowspan=2><p>H:mm:ss</p><td><p>5:00:00</p><td><p>五点</p><tr><td><p>5:01:00</p><td><p>五点零一分</p><tr><td rowspan=4><p>HH:mm:ss</p><td><p>05:00:00</p><td><p>五点</p><tr><td><p>05:01:00</p><td><p>五点零一分</p><tr><td><p>12:01:15</p><td><p>十二点零一分十五秒</p><tr><td><p>12:41:08</p><td><p>十二点四十一分零八秒</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  我们约了<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"time"</span>&gt;</span>12:15<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>在公园门口见面。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV010为例</ul><p><span id=_4-7-3-digits-数字按照「单个数字」播报></span><h3 id=_4-7-3-digits-数字按照「单个数字」播报>4.7.3 digits 数字按照「单个数字」播报</h3><p><strong>描述</strong><table class=volc-viewer-table><thead><tr><th><p>文本</p><th><p>播报</p><tbody><tr><td><p>123，45</p><td><p>一二三，四五</p><tr><td><p>123 456</p><td><p>一二三 四五六</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
我的游戏ID是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"digits"</span>&gt;</span>123 456 789<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
他给我的笔记上写的数字是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"digits"</span>&gt;</span>467,995<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>，我推测这可能是游戏内隐藏奖励的坐标。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV700为例</ul><p><span id=_4-7-4-number-数字按照「整体数字」播报></span><h3 id=_4-7-4-number-数字按照「整体数字」播报>4.7.4 number 数字按照「整体数字」播报</h3><p><strong>描述</strong><br>
只能包含数字、负号、小数点以及英文逗号且符合数字格式，否则会合成失败<table class=volc-viewer-table><thead><tr><th><p>文本</p><th><p>播报</p><tbody><tr><td><p>1,000,000</p><td><p>一百万</p><tr><td><p>-12.34</p><td><p>负十二点三四</p><tr><td><p>1234</p><td><p>一千二百三十四</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
X的值是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"number"</span>&gt;</span>-12.34<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
Y的值是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"number"</span>&gt;</span>467,995<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：（以BV700为例）</ul><p><span id=_4-7-5-telephone-数字按照「电话」播报></span><h3 id=_4-7-5-telephone-数字按照「电话」播报>4.7.5 telephone 数字按照「电话」播报</h3><p><strong>描述</strong><br>
支持空格或横杠分割的数字串作为电话播报，"1"读作“幺”<table class=volc-viewer-table><thead><tr><th><p>文本</p><th><p>播报</p><tbody><tr><td><p>123-4567-4567</p><td><p>幺二三 四五六七 四五六七</p><tr><td><p>110</p><td><p>幺幺零</p><tr><td><p>468 6153</p><td><p>四六八 六幺五三</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    办公室电话是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"telephone"</span>&gt;</span>80004001-121<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
    我的电话是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"telephone"</span>&gt;</span>123 4567 4567<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
    没事儿不要乱拨<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"telephone"</span>&gt;</span>110<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
    他今天给我转账了110元。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV700为例</ul><p><span id=_4-7-6-address-文本按照「地址」播报></span><h3 id=_4-7-6-address-文本按照「地址」播报>4.7.6 address 文本按照「地址」播报</h3><p><strong>描述</strong><br>
标签包含的文本按照地址播报，对其中的数字按照以下方式读取<ul><li>1-2位数字按照整体数字读取</ul><table class=volc-viewer-table><thead><tr><th><p>文本</p><th><p>播报</p><tbody><tr><td><p>1号楼</p><td><p>一号楼</p><tr><td><p>12单元</p><td><p>十二单元</p></table><ul><li>3+位数字按照单个数字读取，且"1"读作"幺"</ul><table class=volc-viewer-table><thead><tr><th><p>文本</p><th><p>播报</p><tbody><tr><td><p>103号房</p><td><p>幺零三号房</p><tr><td><p>4406号房间</p><td><p>四四零六号房间</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
我的住址是<span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"address"</span>&gt;</span>碧水家园12号楼1单元6601号房<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV700为例</ul><p><span id=_4-7-7-characters-文本按照「单个符号」播报></span><h3 id=_4-7-7-characters-文本按照「单个符号」播报>4.7.7. characters 文本按照「单个符号」播报</h3><p><strong>描述</strong><br>
使用该标签后，标签内部文本中的每一个符号顺序播报，在通用场景「business=default」与数学场景下「business=math」效果不同<ul><li><strong>通用场景下</strong>：英文字符以及数字均会按照单个读取，且文本中如有下述符号的也会按照指定发音朗读</ul><table class=volc-viewer-table><thead><tr><th><p>文本符号</p><th><p>播报方式</p><tbody><tr><td><p>.</p><td><p>点</p><tr><td><ul><li></ul><td><p>杠</p><tr><td><p>_</p><td><p>下划线</p></table><ul><li><strong>数学场景（math）下</strong>：仅英文字符会单个读取</ul><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"characters"</span>&gt;</span>
        ab+xy=apple
    <span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV034为列</ul><p><span id=_4-7-8-poetry-按照「古诗风格」播报></span><h3 id=_4-7-8-poetry-按照「古诗风格」播报>4.7.8 poetry 按照「古诗风格」播报</h3><p><strong>描述</strong><br>
标签内容按照古诗词韵律、字词发音方式进行合成。<br><strong>注意事项</strong><br>
请提前验证目标音色在poetry标签下的合成效果。<ul><li>不同音色，训练语料中古诗词的比例不同，会导致古诗文合成的韵律效果各不相同。<li>不同体裁的古诗文韵律结构、特殊字词的发音不同。同一音色对不同体裁古诗文的支持程度也不相同。</ul><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
  <span class=hljs-tag>&lt;<span class=hljs-name>say-as</span> <span class=hljs-attr>interpret-as</span>=<span class=hljs-string>"poetry"</span>&gt;</span>窗前明月光，疑是地上霜。<span class=hljs-tag>&lt;/<span class=hljs-name>say-as</span>&gt;</span>。
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV010为例</ul><p><span id=_4-8-sub-替换别名></span><h2 id=_4-8-sub-替换别名>4.8 sub 替换别名</h2><p><strong>描述</strong><br>
使用该标签后，可将内嵌文本替换为指定文本，后续将按照替换后的文本进行处理以及播报，目前暂时<strong>仅中文音色适用</strong><br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p><strong>参数</strong></p><th><p><strong>值</strong></p><th><p><strong>描述</strong></p><tbody><tr><td><p>alias</p><td><p>string</p><td><p>所替换的文本</p></table><p><strong>示例</strong><pre class=hljs><code class="language-Html volc-pre-code hljs"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span><span class=hljs-tag>&lt;<span class=hljs-name>sub</span> <span class=hljs-attr>alias</span>=<span class=hljs-string>"Speech Synthesis Markup Language"</span>&gt;</span>SSML<span class=hljs-tag>&lt;/<span class=hljs-name>sub</span>&gt;</span><span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">Html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV034为列</ul><p><span id=_4-9-指定文本播报的情感及强度-emotion></span><h2 id=_4-9-指定文本播报的情感及强度-emotion>4.9 指定文本播报的情感及强度 - emotion</h2><p><strong>描述</strong><br>
可对文本制定情感并进行播报，<strong>但仅对支持情感能力的音色生效，且不同音色支持的情感种类不同</strong>。对于不支持的情感，音频能合成成功，但不带情感。此外部分音色（主要是小说风格音色）的情感通过接口参数调用，建议调用前咨询相关同学。<br><strong>属性</strong><table class=volc-viewer-table><thead><tr><th><p><strong>参数</strong></p><th><p><strong>值</strong></p><th><p><strong>描述</strong></p><tbody><tr><td><p>style</p><td><p>","分割的情感组合字符串，最多支持两个情感组合</p><td><p>指定文本情感<br>
两个情感表示组合情感，建议搭配style_ratio进行情感分配，否则默认1.0，只对第一个情感生效</p><tr><td><p>style_ratio</p><td><p>单个情感: [0 , 2.0]<br>
两个情感: [0 , 1.0]</p><td><p>不传默认1.0<br>
单个情感：表示情感强度<br>
两个情感：表示情感分配，第一个情感强度style_ratio，第二个1-style_ratio</p></table><p><strong>示例</strong><pre class=hljs><code class="language-html volc-pre-code hljs language-xml"><span class=hljs-tag>&lt;<span class=hljs-name>speak</span>&gt;</span>
    <span class=hljs-tag>&lt;<span class=hljs-name>emotion</span> <span class=hljs-attr>style</span>=<span class=hljs-string>'sorry'</span> <span class=hljs-attr>style_ratio</span>=<span class=hljs-string>'0.5'</span>&gt;</span> 
        很抱歉，我还不支持这项功能，但我会努力学习的！
    <span class=hljs-tag>&lt;/<span class=hljs-name>emotion</span>&gt;</span> 
    <span class=hljs-tag>&lt;<span class=hljs-name>emotion</span> <span class=hljs-attr>style</span>=<span class=hljs-string>'sorry'</span> <span class=hljs-attr>style_ratio</span>=<span class=hljs-string>'1.0'</span>&gt;</span> 
        很抱歉，我还不支持这项功能，但我会努力学习的！
    <span class=hljs-tag>&lt;/<span class=hljs-name>emotion</span>&gt;</span>
       <span class=hljs-tag>&lt;<span class=hljs-name>emotion</span> <span class=hljs-attr>style</span>=<span class=hljs-string>'sorry'</span> <span class=hljs-attr>style_ratio</span>=<span class=hljs-string>'2.0'</span>&gt;</span> 
        很抱歉，我还不支持这项功能，但我会努力学习的！
    <span class=hljs-tag>&lt;/<span class=hljs-name>emotion</span>&gt;</span>    
<span class=hljs-tag>&lt;/<span class=hljs-name>speak</span>&gt;</span>
</code><div class="volc-pre-code-language no-copy">html</div><div class="volc-pre-abs volc-flex-v-center"><div></div><div class=volc-copy-code-btn></div></div></pre><ul><li>音频效果：以BV700为例</ul><p><span id=_5-关于标签嵌套的说明></span><div id=_5-关于标签嵌套的说明 class="md-h1 heading-h1">5. 关于标签嵌套的说明</div><ul><li><strong>phoneme 标签</strong><ul><li>不可嵌套其它标签</ul><li><strong>word 标签</strong><ul><li>可嵌套一层phoneme标签</ul><li><strong>break 标签</strong><ul><li>不可嵌套其它标签</ul><li><strong>audio 标签</strong><ul><li>不可嵌套其它标签</ul></ul></div></div></div><div></div><div class=box-wbPz><a class="item-im6x prev-t2hq" href=https://www.volcengine.com/docs/6561/1108211><div class=head-N5m8>上一篇</div><div class=info-R5lO>音色列表</div></a><a class="item-im6x next-T3UO" href=https://www.volcengine.com/docs/6561/109880><div class=head-N5m8>下一篇</div><div class=info-R5lO>产品概述</div></a></div></div></div></div><div id=app-modal data-bytereplay-mask=ignore><div><div class="arco-modal-wrapper arco-modal-wrapper-align-center"></div></div></div></div><div id=volcfe-sidebar-wrap class=volcfe-sidebar-theme-mini data-version=8.0.5><div class=volcfe-sidebar-pc><div><div class="volcfe-sidebar-cell-wrap volcfe-sidebar-mini"><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div><div data-fg_entry_style_display><div><div class=volcfe-sidebar-first-level-item-wrap><div class=volcfe-sidebar-icon-small></div></div></div></div></div><div><div class=feedbackentrywrap-lE0Q id=feedback-side><div class=feedbackentry-btFq></div></div></div></div></div></div><div class=volcfe-sidebar-mobile></div></div></div></div><browser-mcp-container data-wxt-shadow-root><template shadowrootmode=open></template></browser-mcp-container><div class=drawer-container_678c6><div class="drawer_678c6 a_678c6 contact-drawer_241e9"></div></div><div data-version=1.4.4 class="volcfe-ai-hibot__popup-container volcfe-ai-hibot__popup-container--hide"></div><plasmo-csui id=aitdk-csui><template shadowrootmode=open><div id=plasmo-shadow-container><div id=plasmo-inline class=plasmo-csui-container><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"></div></div></div></template></plasmo-csui><xt-button id=trancy-button><div><template shadowrootmode=open><div class="rd-translator-btn rd-theme"></div></template></div></xt-button><script data-template-shadow-root>(()=>{document.currentScript.remove();processNode(document);function processNode(node){node.querySelectorAll("template[shadowrootmode]").forEach(element=>{let shadowRoot = element.parentElement.shadowRoot;if (!shadowRoot) {try {shadowRoot=element.parentElement.attachShadow({mode:element.getAttribute("shadowrootmode"),delegatesFocus:element.getAttribute("shadowrootdelegatesfocus")!=null,clonable:element.getAttribute("shadowrootclonable")!=null,serializable:element.getAttribute("shadowrootserializable")!=null});shadowRoot.innerHTML=element.innerHTML;element.remove()} catch (error) {} if (shadowRoot) {processNode(shadowRoot)}}})}})()</script>