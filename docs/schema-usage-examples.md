# Schema 组件使用指南

## 概述

Schema 组件用于生成符合 schema.org 标准的 JSON-LD 结构化数据，主要用于 SEO 优化。该组件支持多种类型的结构化数据：
- **SoftwareApplication** - 游戏和应用程序
- **Article** - 博客文章和新闻
- **WebPage** - 一般网页

## 基本使用

### 1. 游戏应用页面 (SoftwareApplication)

```tsx
import Schema, { GameSchema } from '@/components/seo';

// 使用通用组件
export default function GamePage() {
  return (
    <>
      <Schema
        title="超级马里奥兄弟"
        description="经典的平台跳跃游戏，与马里奥一起冒险拯救公主"
        image="https://example.com/mario.jpg"
        url="https://example.com/games/super-mario"
        schemaType="SoftwareApplication"
        applicationCategory="GameApplication"
        operatingSystem="Nintendo Switch, PC"
        breadcrumb={[
          { name: "游戏", url: "/games" },
          { name: "平台游戏", url: "/games/platform" }
        ]}
      />
      {/* 页面内容 */}
    </>
  );
}

// 或使用专门的游戏组件
export function GamePageAlt() {
  return (
    <>
      <GameSchema
        title="超级马里奥兄弟"
        description="经典的平台跳跃游戏"
        image="https://example.com/mario.jpg"
        url="https://example.com/games/super-mario"
        applicationCategory="GameApplication"
        operatingSystem="Nintendo Switch, PC"
        ratingValue={4.8}
        ratingCount={1500}
        breadcrumb={[
          { name: "游戏", url: "/games" },
          { name: "平台游戏", url: "/games/platform" }
        ]}
      />
      {/* 页面内容 */}
    </>
  );
}
```

### 2. 博客文章页面 (Article)

```tsx
import { ArticleSchema } from '@/components/seo';

export default function BlogPost() {
  return (
    <>
      <ArticleSchema
        title="React 18 新特性详解"
        description="深入了解 React 18 带来的并发特性、自动批处理和新的 Hooks"
        image="https://example.com/react18.jpg"
        url="https://example.com/blog/react-18-features"
        authorName="张三"
        datePublished="2024-01-15T10:00:00Z"
        dateModified="2024-01-20T14:30:00Z"
        wordCount={2500}
        readingTime={12}
        tags={["React", "JavaScript", "前端开发", "Web开发"]}
        breadcrumb={[
          { name: "博客", url: "/blog" },
          { name: "前端技术", url: "/blog/frontend" },
          { name: "React", url: "/blog/frontend/react" }
        ]}
      />
      {/* 文章内容 */}
    </>
  );
}
```

### 3. 一般网页 (WebPage)

```tsx
import { WebPageSchema } from '@/components/seo';

export default function AboutPage() {
  return (
    <>
      <WebPageSchema
        title="关于我们"
        description="了解我们的团队、使命和愿景"
        image="https://example.com/about.jpg"
        url="https://example.com/about"
        breadcrumb={[]}
      />
      {/* 页面内容 */}
    </>
  );
}
```

### 2. 包含发布信息的页面

```tsx
<Schema
  title="最新游戏发布"
  description="我们最新发布的动作冒险游戏"
  image="https://example.com/new-game.jpg"
  url="https://example.com/games/new-release"
  datePublished="2024-01-15T10:00:00Z"
  dateModified="2024-01-20T14:30:00Z"
  breadcrumb={[
    { name: "游戏", url: "/games" },
    { name: "新发布", url: "/games/new" }
  ]}
/>
```

### 3. 包含评分的游戏

```tsx
<Schema
  title="热门射击游戏"
  description="获得高评分的第一人称射击游戏"
  image="https://example.com/shooter.jpg"
  url="https://example.com/games/shooter"
  ratingValue={4.5}
  ratingCount={1250}
  operatingSystem="Windows, macOS, Linux"
  breadcrumb={[
    { name: "游戏", url: "/games" },
    { name: "射击游戏", url: "/games/shooter" }
  ]}
/>
```

## 简化版组件

对于不需要面包屑的简单页面，可以使用 `SimpleSchema` 组件：

```tsx
import { SimpleSchema } from '@/components/seo';

// 默认为 WebPage 类型
<SimpleSchema
  title="关于我们"
  description="了解我们的团队和使命"
  image="https://example.com/about.jpg"
  url="https://example.com/about"
/>

// 指定为 Article 类型
<SimpleSchema
  title="技术博客"
  description="分享最新的技术见解"
  image="https://example.com/blog.jpg"
  url="https://example.com/blog"
  schemaType="Article"
/>
```

## 新增的 Article 类型特性

### Article Schema 专有属性

```tsx
<ArticleSchema
  title="文章标题"
  description="文章摘要"
  image="https://example.com/article.jpg"
  url="https://example.com/articles/my-article"

  // Article 特有属性
  authorName="作者姓名"           // 作者信息
  wordCount={1500}              // 文章字数
  readingTime={8}               // 预计阅读时间（分钟）
  tags={["技术", "编程"]}        // 文章标签

  // 通用属性
  datePublished="2024-01-01T10:00:00Z"
  dateModified="2024-01-15T14:30:00Z"
  ratingValue={4.5}
  ratingCount={100}

  breadcrumb={[
    { name: "博客", url: "/blog" },
    { name: "技术", url: "/blog/tech" }
  ]}
/>
```

### 新闻文章示例

```tsx
<ArticleSchema
  title="2024年前端开发趋势预测"
  description="分析即将到来的前端技术发展方向和新兴框架"
  image="https://example.com/frontend-trends-2024.jpg"
  url="https://example.com/news/frontend-trends-2024"
  authorName="李四"
  datePublished="2024-01-01T09:00:00Z"
  wordCount={3200}
  readingTime={15}
  tags={["前端开发", "技术趋势", "JavaScript", "框架"]}
  breadcrumb={[
    { name: "新闻", url: "/news" },
    { name: "技术新闻", url: "/news/tech" }
  ]}
/>
```

### 教程文章示例

```tsx
<ArticleSchema
  title="从零开始学习 TypeScript"
  description="完整的 TypeScript 入门教程，包含实例和最佳实践"
  image="https://example.com/typescript-tutorial.jpg"
  url="https://example.com/tutorials/typescript-basics"
  authorName="王五"
  datePublished="2024-01-10T14:00:00Z"
  dateModified="2024-01-20T16:30:00Z"
  wordCount={4500}
  readingTime={22}
  tags={["TypeScript", "教程", "编程", "JavaScript"]}
  ratingValue={4.8}
  ratingCount={250}
  breadcrumb={[
    { name: "教程", url: "/tutorials" },
    { name: "编程语言", url: "/tutorials/programming" },
    { name: "TypeScript", url: "/tutorials/programming/typescript" }
  ]}
/>
```

## 高级用法

### 1. 动态生成面包屑

```tsx
function GameDetailPage({ gameId, category }: { gameId: string; category: string }) {
  const breadcrumb = [
    { name: "游戏", url: "/games" },
    { name: category, url: `/games/${category}` },
  ];

  return (
    <Schema
      title={`游戏 ${gameId}`}
      description="游戏详情页面"
      image={`https://example.com/games/${gameId}.jpg`}
      url={`https://example.com/games/${gameId}`}
      breadcrumb={breadcrumb}
    />
  );
}
```

### 2. 在 Next.js 页面中使用

```tsx
// app/games/[id]/page.tsx
import Schema from '@/components/seo';

interface PageProps {
  params: { id: string };
}

export default async function GamePage({ params }: PageProps) {
  const game = await getGameById(params.id);
  
  return (
    <>
      <Schema
        title={game.title}
        description={game.description}
        image={game.coverImage}
        url={`${process.env.NEXT_PUBLIC_WEB_URL}/games/${params.id}`}
        datePublished={game.publishedAt}
        dateModified={game.updatedAt}
        ratingValue={game.averageRating}
        ratingCount={game.reviewCount}
        breadcrumb={[
          { name: "游戏", url: "/games" },
          { name: game.category, url: `/games/category/${game.categorySlug}` }
        ]}
      />
      {/* 页面内容 */}
    </>
  );
}
```

## 最佳实践

### 1. 图片优化
- 使用高质量的图片（至少 1200x630 像素）
- 确保图片 URL 是绝对路径
- 图片应该与内容相关

### 2. URL 规范
- 始终使用完整的绝对 URL
- 确保 URL 与实际页面地址一致
- 避免使用重定向的 URL

### 3. 日期格式
- 使用 ISO 8601 格式：`2024-01-15T10:00:00Z`
- 或简化格式：`2024-01-15`
- 确保日期的准确性

### 4. 面包屑设计
- 保持面包屑层级清晰
- 避免过深的层级（建议不超过 5 层）
- 确保每个面包屑项目都有有效的 URL

## 错误处理

组件内置了错误处理机制：

```tsx
// 如果生成 schema 时出错，组件会：
// 1. 在控制台输出错误信息
// 2. 返回空的 JSON-LD 脚本，避免页面崩溃
// 3. 在开发环境中提供详细的错误信息
```

## 验证工具

推荐使用以下工具验证生成的结构化数据：

1. [Google Rich Results Test](https://search.google.com/test/rich-results)
2. [Schema.org Validator](https://validator.schema.org/)
3. [JSON-LD Playground](https://json-ld.org/playground/)

## 注意事项

1. **环境变量**：确保设置了 `NEXT_PUBLIC_WEB_URL` 和 `NEXT_PUBLIC_PROJECT_NAME`
2. **性能**：组件是异步的，适合在服务端渲染中使用
3. **SEO**：结构化数据有助于搜索引擎理解页面内容，但不保证在搜索结果中显示富媒体片段
4. **更新**：当页面内容发生变化时，记得更新对应的 schema 数据
