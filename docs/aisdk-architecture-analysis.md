# ShipAny AI SDK 架构深度分析

## 📋 概述

本文档详细分析了 `/Users/<USER>/My-Project/shipany_2507/aisdk/` 目录中的AI SDK功能和架构，涵盖整体架构、视频生成流程、存储机制、用户访问方式和数据保留策略等关键方面。

## 🎯 1. 整体架构分析

### 1.1 核心功能和作用

AI SDK 是一个统一的多模态AI内容生成框架，主要功能包括：

```typescript
// aisdk/index.ts
export * from "./generate-video";
```

- **视频生成**：通过 `generateVideo` 函数提供统一的视频生成接口
- **多提供商支持**：目前主要集成了 Kling AI 作为视频生成提供商
- **标准化接口**：提供了 `VideoModelV1` 标准接口，便于扩展其他AI提供商

### 1.2 主要模块和组件

#### 核心生成模块 (`generate-video/`)
- `generate-video.ts`：主要的视频生成逻辑
- `generate-video-result.ts`：结果类型定义
- `index.ts`：模块导出

#### 提供商实现 (`kling/`)
- `kling-provider.ts`：Kling AI 提供商实现
- `kling-video-model.ts`：视频模型具体实现
- `text2video.ts`：文本转视频API客户端
- `client.ts`：JWT认证和基础客户端

#### 类型系统 (`types/`, `provider/`)
- 定义了标准化的视频模型接口
- 提供了警告和错误处理类型

### 1.3 模块协作关系

```mermaid
graph TD
    A[generateVideo] --> B[VideoModelV1]
    B --> C[KlingVideoModel]
    C --> D[KlingClient]
    D --> E[Kling API]
    E --> F[视频URL]
    F --> G[下载&转换]
    G --> H[Uint8Array/Base64]
```

## 🎬 2. 视频生成流程

### 2.1 完整的数据流程

```typescript
// aisdk/kling/kling-video-model.ts
async doGenerate({
  prompt,
  n,
  providerOptions,
  headers,
  abortSignal,
}: Parameters<VideoModelV1["doGenerate"]>[0]): Promise<
  Awaited<ReturnType<VideoModelV1["doGenerate"]>>
> {
  const warnings: Array<VideoModelV1CallWarning> = [];
  let videos: Array<Uint8Array> = [];
  const videoUrls: Array<string> = [];
  // ...
}
```

### 2.2 步骤详解

#### 第一阶段：任务创建
1. 用户通过API发送视频生成请求
2. 系统调用 `generateVideo` 函数
3. 创建 Kling AI 客户端并生成JWT token
4. 向 Kling API 提交文本转视频任务

#### 第二阶段：轮询等待
- 系统每30秒轮询一次任务状态
- 最多尝试20次（总计10分钟）
- 监控任务状态：`pending` → `processing` → `succeed`/`failed`

#### 第三阶段：视频下载
- 任务成功后获取视频URL列表
- 并行下载所有视频文件
- 将视频转换为 `Uint8Array` 格式

#### 第四阶段：数据处理
- 支持 base64 和 Uint8Array 两种格式
- 提供懒加载转换机制

### 2.3 关键配置参数

```typescript
// 轮询配置
const maxAttempts = 20;           // 最大尝试次数
const pollingInterval = 30000;    // 轮询间隔（30秒）

// 视频生成参数
{
  model: "kling-v1" | "kling-v1-6",
  aspect_ratio: "16:9" | "9:16" | "1:1",
  duration: 5 | 10,
  mode: "std" | "pro"
}
```

## 💾 3. 存储机制

### 3.1 存储架构

系统使用 **AWS S3 兼容的对象存储**：

```bash
# .env.example
STORAGE_ENDPOINT = ""      # 存储服务端点
STORAGE_REGION = ""        # 存储区域
STORAGE_ACCESS_KEY = ""    # 访问密钥
STORAGE_SECRET_KEY = ""    # 密钥
STORAGE_BUCKET = ""        # 存储桶名称
STORAGE_DOMAIN = ""        # CDN域名
```

### 3.2 存储策略

```typescript
// lib/storage.ts
async uploadFile({
  body,                    // 文件内容Buffer
  key,                     // 存储路径
  contentType,             // MIME类型
  bucket,                  // 存储桶
  onProgress,              // 上传进度回调
  disposition = "inline",  // 访问模式
})
```

### 3.3 存储特点

1. **目录结构**：使用 `shipany/` 作为根目录前缀
2. **文件命名**：`${provider}_video_${batch}_${index}.mp4` 格式
3. **访问控制**：
   - `inline`：直接在浏览器中播放
   - `attachment`：强制下载
4. **CDN支持**：通过 `STORAGE_DOMAIN` 配置CDN域名加速访问

## 🔐 4. 用户访问方式

### 4.1 API接口设计

基于现有图片生成API的模式，视频生成API应该遵循以下设计：

#### 请求格式
```http
POST /api/demo/gen-video
Content-Type: application/json

{
  "prompt": "一只可爱的小猫在花园里玩耍",
  "provider": "kling",
  "model": "kling-v1",
  "options": {
    "aspect_ratio": "16:9",
    "duration": 5,
    "mode": "std"
  }
}
```

#### 响应格式
```json
{
  "code": 200,
  "data": [
    {
      "location": "https://s3.amazonaws.com/bucket/shipany/kling_video_uuid_0.mp4",
      "url": "https://cdn.domain.com/shipany/kling_video_uuid_0.mp4",
      "filename": "kling_video_uuid_0.mp4",
      "provider": "kling",
      "bucket": "shipany-storage",
      "key": "shipany/kling_video_uuid_0.mp4"
    }
  ]
}
```

### 4.2 权限控制机制

```typescript
// 用户认证检查
const user_uuid = await getUserUuid();
if (!user_uuid) {
  return respErr("no auth");
}

// 积分扣除
await decreaseCredits({
  user_uuid,
  trans_type: CreditsTransType.VideoGeneration,
  credits: CreditsAmount.VideoGenerationCost,
});
```

#### 访问控制层级
1. **身份验证**：基于 NextAuth.js 的用户认证
2. **积分系统**：每次生成消耗用户积分
3. **使用限制**：通过积分余额控制使用频率
4. **用户分层**：免费用户 vs 付费用户不同配额

## 📊 5. 数据保留策略

### 5.1 数据库设计

```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at timestamptz,
    -- 其他用户字段
);

-- 积分记录表
CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at timestamptz,
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,
    expired_at timestamptz
);
```

### 5.2 保留策略分析

#### 文件存储策略
- **存储位置**：S3兼容对象存储
- **文件命名**：包含时间戳和用户标识
- **清理机制**：当前无自动清理，需要手动管理

#### 使用记录策略
- **积分记录**：每次生成都有对应的积分扣除记录
- **审计追踪**：通过 `trans_no` 可追踪每次操作

#### 用户分层策略
- **免费用户**：有限的存储配额和生成次数
- **付费用户**：通过 `orders` 表管理订阅状态，享受更高配额

### 5.3 建议的保留策略

1. **短期存储**（7天）：免费用户生成的内容
2. **中期存储**（30天）：付费用户的临时内容
3. **长期存储**（永久）：付费用户的重要内容
4. **自动清理**：定期清理过期的临时文件

## 🔧 6. 技术特点总结

### 6.1 架构优势
1. **模块化设计**：清晰的分层架构，易于扩展新的AI提供商
2. **异步处理**：支持长时间运行的视频生成任务
3. **错误处理**：完善的警告和错误处理机制
4. **类型安全**：完整的TypeScript类型定义
5. **云原生**：基于S3的分布式存储架构

### 6.2 业务特点
1. **用户管理**：集成的认证和积分系统
2. **多格式支持**：base64和Uint8Array双格式
3. **进度监控**：实时任务状态跟踪
4. **扩展性**：标准化接口便于添加新提供商

### 6.3 性能特点
1. **并发处理**：支持多视频并行生成
2. **懒加载**：按需进行格式转换
3. **CDN加速**：通过CDN提升访问速度
4. **容错机制**：完善的重试和超时处理

## 📈 7. 扩展建议

### 7.1 功能扩展
- 添加更多AI提供商（如OpenAI、Runway等）
- 支持图片转视频功能
- 添加视频编辑和后处理功能
- 实现批量生成和队列管理

### 7.2 性能优化
- 实现智能缓存机制
- 添加生成进度实时推送
- 优化大文件上传和下载
- 实现分片上传支持

### 7.3 运维改进
- 添加自动文件清理机制
- 实现存储使用量监控
- 添加生成质量评估
- 完善日志和监控系统

---

**文档版本**: v1.0  
**最后更新**: 2025-01-19  
**分析范围**: `/Users/<USER>/My-Project/shipany_2507/aisdk/`
