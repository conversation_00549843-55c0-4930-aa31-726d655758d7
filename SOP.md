# 网站部署标准操作程序 (SOP)

## 项目概述

### 技术栈分析
- **框架**: Next.js 15 (React 18)
- **语言**: TypeScript
- **样式**: TailwindCSS + Shadcn/UI
- **国际化**: next-intl (支持英文/中文)
- **认证**: NextAuth.js (Google/GitHub OAuth)
- **数据库**: Supabase (PostgreSQL)
- **支付**: Stripe
- **部署**: Vercel/Cloudflare/Docker

### 项目结构
```
app/[locale]/(default)/page.tsx  # 主页面组件
i18n/                           # 国际化配置
├── messages/                   # 通用翻译文件
│   ├── en.json                # 英文翻译
│   └── zh.json                # 中文翻译
└── pages/landing/             # 落地页内容
    ├── en.json                # 英文落地页
    └── zh.json                # 中文落地页
components/blocks/             # 页面组件块
services/                      # 业务逻辑服务
models/                        # 数据模型
```

## 第一部分：环境准备

### 1.1 代码库设置
```bash
# 克隆项目
git clone <your-repository-url>
cd shipany_2507

# 安装依赖
pnpm install

# 复制环境变量模板
cp .env.example .env.local
```

### 1.2 环境变量配置
编辑 `.env.local` 文件，配置以下必需变量：

#### 基础配置
```env
NEXT_PUBLIC_WEB_URL="https://yourdomain.com"
NEXT_PUBLIC_PROJECT_NAME="Your Project Name"
AUTH_SECRET="your-auth-secret"  # 使用 openssl rand -base64 32 生成
```

#### Supabase 数据库配置
```env
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

#### 认证提供商配置
```env
# Google OAuth
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"
NEXT_PUBLIC_AUTH_GOOGLE_ID="your-google-client-id"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED="true"

# GitHub OAuth
AUTH_GITHUB_ID="your-github-client-id"
AUTH_GITHUB_SECRET="your-github-client-secret"
NEXT_PUBLIC_AUTH_GITHUB_ENABLED="true"
```

#### Stripe 支付配置
```env
STRIPE_PUBLIC_KEY="pk_live_..."
STRIPE_PRIVATE_KEY="sk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
NEXT_PUBLIC_PAY_SUCCESS_URL="https://yourdomain.com/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL="https://yourdomain.com/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL="https://yourdomain.com/#pricing"
```

## 第二部分：第三方服务设置

### 2.1 Supabase 项目设置

#### 创建 Supabase 项目
1. 访问 [supabase.com](https://supabase.com)
2. 点击 "New Project"
3. 选择组织和区域
4. 设置数据库密码
5. 等待项目创建完成

#### 数据库初始化
1. 在 Supabase Dashboard 中打开 SQL Editor
2. 执行 `data/install.sql` 中的 SQL 脚本：
   ```sql
   -- 创建用户表
   CREATE TABLE users (
       id SERIAL PRIMARY KEY,
       uuid VARCHAR(255) UNIQUE NOT NULL,
       email VARCHAR(255) NOT NULL,
       -- ... 其他字段
   );
   
   -- 创建订单表
   CREATE TABLE orders (
       id SERIAL PRIMARY KEY,
       order_no VARCHAR(255) UNIQUE NOT NULL,
       -- ... 其他字段
   );
   ```

#### 获取 API 密钥
1. 在项目设置中找到 "API" 部分
2. 复制以下密钥到环境变量：
   - `anon public` → `SUPABASE_ANON_KEY`
   - `service_role` → `SUPABASE_SERVICE_ROLE_KEY`
   - Project URL → `SUPABASE_URL`

### 2.2 Google OAuth 设置

#### 创建 Google Cloud 项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com)
2. 创建新项目或选择现有项目
3. 启用 Google+ API

#### 配置 OAuth 同意屏幕
1. 导航到 "APIs & Services" > "OAuth consent screen"
2. 选择用户类型（External）
3. 填写应用信息：
   - 应用名称
   - 用户支持邮箱
   - 开发者联系信息
4. 添加授权域名：`yourdomain.com`

#### 创建 OAuth 客户端
1. 导航到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "OAuth client ID"
3. 选择应用类型：Web application
4. 配置重定向 URI：
   ```
   https://yourdomain.com/api/auth/callback/google
   ```
5. 保存客户端 ID 和密钥到环境变量

### 2.3 GitHub OAuth 设置

#### 创建 GitHub OAuth App
1. 访问 GitHub Settings > Developer settings > OAuth Apps
2. 点击 "New OAuth App"
3. 填写应用信息：
   - Application name: Your App Name
   - Homepage URL: `https://yourdomain.com`
   - Authorization callback URL: `https://yourdomain.com/api/auth/callback/github`
4. 保存 Client ID 和 Client Secret

### 2.4 Stripe 支付设置

#### 创建 Stripe 账户
1. 访问 [stripe.com](https://stripe.com) 注册账户
2. 完成账户验证流程

#### 获取 API 密钥
1. 在 Stripe Dashboard 中找到 "Developers" > "API keys"
2. 复制以下密钥：
   - Publishable key → `STRIPE_PUBLIC_KEY`
   - Secret key → `STRIPE_PRIVATE_KEY`

#### 配置 Webhook
1. 在 "Developers" > "Webhooks" 中创建新端点
2. 端点 URL: `https://yourdomain.com/api/stripe/webhook`
3. 选择事件类型：
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `customer.subscription.updated`
4. 保存 Webhook 签名密钥到 `STRIPE_WEBHOOK_SECRET`

## 第三部分：内容管理

### 3.1 多语言内容结构

#### 通用翻译文件位置
- `i18n/messages/en.json` - 英文通用翻译
- `i18n/messages/zh.json` - 中文通用翻译

#### 落地页内容文件位置
- `i18n/pages/landing/en.json` - 英文落地页
- `i18n/pages/landing/zh.json` - 中文落地页

### 3.2 落地页内容配置

#### 主要内容区块
落地页由以下组件构成（按显示顺序）：
1. `hero` - 英雄区块
2. `branding` - 品牌展示
3. `introduce` - 产品介绍
4. `benefit` - 产品优势
5. `usage` - 使用方法
6. `feature` - 功能特性
7. `showcase` - 案例展示
8. `stats` - 数据统计
9. `pricing` - 价格方案
10. `testimonial` - 用户评价
11. `faq` - 常见问题
12. `cta` - 行动号召

#### 内容更新流程
1. 编辑对应语言的 JSON 文件
2. 修改文本内容、图片路径、链接等
3. 保存文件后重新部署

### 3.3 添加新语言支持

#### 步骤
1. 在 `i18n/messages/` 中创建新语言文件（如 `ja.json`）
2. 在 `i18n/pages/landing/` 中创建对应文件
3. 更新 `i18n/locale.ts` 中的语言配置
4. 更新 `middleware.ts` 中的匹配规则

## 第四部分：部署配置

### 4.1 Vercel 部署

#### 准备工作
1. 确保代码已推送到 Git 仓库
2. 在 [vercel.com](https://vercel.com) 创建账户

#### 部署步骤
1. 在 Vercel 中导入 Git 仓库
2. 配置环境变量（复制 `.env.local` 内容）
3. 设置构建命令：`pnpm build`
4. 设置输出目录：`.next`
5. 点击部署

#### 自定义域名
1. 在项目设置中添加自定义域名
2. 配置 DNS 记录指向 Vercel
3. 等待 SSL 证书自动配置

### 4.2 Cloudflare Pages 部署

#### 准备配置文件
1. 复制 `wrangler.toml.example` 为 `wrangler.toml`
2. 修改项目名称和配置

#### 部署命令
```bash
# 构建项目
pnpm cf:build

# 预览部署
pnpm cf:preview

# 正式部署
pnpm cf:deploy
```



## 第六部分：监控和维护

### 6.1 分析工具配置
```env
# Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# OpenPanel
NEXT_PUBLIC_OPENPANEL_CLIENT_ID="your-client-id"
```

### 6.2 错误监控
- 配置 Sentry 或其他错误监控服务
- 设置日志收集和告警

### 6.3 性能优化
- 启用 CDN 加速
- 配置缓存策略
- 优化图片和资源加载

## 检查清单

### 部署前检查
- [ ] 所有环境变量已正确配置
- [ ] 数据库表已创建
- [ ] OAuth 应用已配置
- [ ] Stripe webhook 已设置
- [ ] 域名 DNS 已配置

### 部署后验证
- [ ] 网站可正常访问
- [ ] 用户注册/登录功能正常
- [ ] 支付流程可用
- [ ] 多语言切换正常
- [ ] 移动端适配良好

### 安全检查
- [ ] 生产环境密钥已更新
- [ ] HTTPS 已启用
- [ ] 敏感信息未暴露
- [ ] 数据库访问权限正确

---

**注意事项**：
- 生产环境请使用强密码和安全的 API 密钥
- 定期备份数据库
- 监控应用性能和错误日志
- 及时更新依赖包以修复安全漏洞
