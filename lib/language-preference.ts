/**
 * 语言偏好存储工具函数
 * 用于管理用户的语言偏好设置
 */

import { cacheGet, cacheSet, cacheRemove } from "@/lib/cache";
import { CacheKey } from "@/services/constant";
import { getTimestamp } from "@/lib/time";

/**
 * 语言偏好设置的类型
 */
export interface LanguagePreference {
  /** 用户选择的语言 */
  selectedLanguage: string;
  /** 浏览器检测到的语言 */
  detectedLanguage: string;
  /** 用户是否明确拒绝了语言切换建议 */
  declined: boolean;
  /** 设置时间戳 */
  timestamp: number;
}

/**
 * 语言切换询问记录的类型
 */
export interface LanguageSwitchAsked {
  /** 被询问的语言组合 (from-to) */
  languagePair: string;
  /** 询问时间戳 */
  timestamp: number;
  /** 用户的选择 ('accepted' | 'declined') */
  choice: 'accepted' | 'declined';
}

/**
 * 保存用户的语言偏好
 * @param preference 语言偏好设置
 * @param expiresInDays 过期天数，默认30天，-1表示永不过期
 */
export function saveLanguagePreference(
  preference: LanguagePreference, 
  expiresInDays: number = 30
): void {
  const expiresAt = expiresInDays === -1 ? -1 : getTimestamp() + (expiresInDays * 24 * 60 * 60);
  cacheSet(CacheKey.LanguagePreference, JSON.stringify(preference), expiresAt);
}

/**
 * 获取用户的语言偏好
 * @returns 语言偏好设置，如果不存在则返回null
 */
export function getLanguagePreference(): LanguagePreference | null {
  const cached = cacheGet(CacheKey.LanguagePreference);
  if (!cached) {
    return null;
  }

  try {
    return JSON.parse(cached) as LanguagePreference;
  } catch (error) {
    console.error("Failed to parse language preference:", error);
    // 如果解析失败，清除缓存
    removeLanguagePreference();
    return null;
  }
}

/**
 * 移除用户的语言偏好
 */
export function removeLanguagePreference(): void {
  cacheRemove(CacheKey.LanguagePreference);
}

/**
 * 记录语言切换询问
 * @param fromLanguage 当前语言
 * @param toLanguage 建议切换到的语言
 * @param choice 用户的选择
 * @param expiresInDays 过期天数，默认7天
 */
export function recordLanguageSwitchAsked(
  fromLanguage: string,
  toLanguage: string,
  choice: 'accepted' | 'declined',
  expiresInDays: number = 7
): void {
  const languagePair = `${fromLanguage}-${toLanguage}`;
  const record: LanguageSwitchAsked = {
    languagePair,
    timestamp: getTimestamp(),
    choice,
  };

  const expiresAt = getTimestamp() + (expiresInDays * 24 * 60 * 60);
  cacheSet(CacheKey.LanguageSwitchAsked, JSON.stringify(record), expiresAt);
}

/**
 * 检查是否已经询问过特定的语言切换
 * @param fromLanguage 当前语言
 * @param toLanguage 建议切换到的语言
 * @returns 如果已经询问过，返回询问记录，否则返回null
 */
export function getLanguageSwitchAsked(
  fromLanguage: string,
  toLanguage: string
): LanguageSwitchAsked | null {
  const cached = cacheGet(CacheKey.LanguageSwitchAsked);
  if (!cached) {
    return null;
  }

  try {
    const record = JSON.parse(cached) as LanguageSwitchAsked;
    const expectedPair = `${fromLanguage}-${toLanguage}`;
    
    if (record.languagePair === expectedPair) {
      return record;
    }
    
    return null;
  } catch (error) {
    console.error("Failed to parse language switch asked record:", error);
    return null;
  }
}

/**
 * 清除语言切换询问记录
 */
export function clearLanguageSwitchAsked(): void {
  cacheRemove(CacheKey.LanguageSwitchAsked);
}

/**
 * 检查是否应该显示语言切换建议
 * @param currentLanguage 当前语言
 * @param suggestedLanguage 建议的语言
 * @returns 是否应该显示建议
 */
export function shouldShowLanguageSuggestion(
  currentLanguage: string,
  suggestedLanguage: string
): boolean {
  // 检查用户是否已经有明确的语言偏好
  const preference = getLanguagePreference();
  if (preference) {
    // 如果用户已经明确选择了当前语言，不再建议切换
    if (preference.selectedLanguage === currentLanguage) {
      return false;
    }
    
    // 如果用户之前拒绝了从检测语言切换到建议语言，不再建议
    if (preference.declined && 
        preference.detectedLanguage === suggestedLanguage &&
        preference.selectedLanguage === currentLanguage) {
      return false;
    }
  }

  // 检查是否最近已经询问过这个语言切换
  const askedRecord = getLanguageSwitchAsked(currentLanguage, suggestedLanguage);
  if (askedRecord) {
    // 如果用户之前拒绝了，不再询问
    if (askedRecord.choice === 'declined') {
      return false;
    }
    
    // 如果用户之前接受了但现在又回到了原语言，可能需要重新询问
    // 这里我们选择不重复询问，避免打扰用户
    return false;
  }

  return true;
}

/**
 * 保存用户接受语言切换的偏好
 * @param currentLanguage 当前语言
 * @param selectedLanguage 用户选择的语言
 * @param detectedLanguage 浏览器检测到的语言
 */
export function saveLanguageSwitchAccepted(
  currentLanguage: string,
  selectedLanguage: string,
  detectedLanguage: string
): void {
  const preference: LanguagePreference = {
    selectedLanguage,
    detectedLanguage,
    declined: false,
    timestamp: getTimestamp(),
  };

  saveLanguagePreference(preference);
  recordLanguageSwitchAsked(currentLanguage, selectedLanguage, 'accepted');
}

/**
 * 保存用户拒绝语言切换的偏好
 * @param currentLanguage 当前语言
 * @param suggestedLanguage 建议的语言
 * @param detectedLanguage 浏览器检测到的语言
 */
export function saveLanguageSwitchDeclined(
  currentLanguage: string,
  suggestedLanguage: string,
  detectedLanguage: string
): void {
  const preference: LanguagePreference = {
    selectedLanguage: currentLanguage,
    detectedLanguage,
    declined: true,
    timestamp: getTimestamp(),
  };

  saveLanguagePreference(preference);
  recordLanguageSwitchAsked(currentLanguage, suggestedLanguage, 'declined');
}

/**
 * 调试函数：获取所有语言偏好相关的信息
 * @returns 语言偏好的详细信息
 */
export function getLanguagePreferenceDebugInfo() {
  return {
    preference: getLanguagePreference(),
    switchAsked: cacheGet(CacheKey.LanguageSwitchAsked),
    timestamp: getTimestamp(),
  };
}
