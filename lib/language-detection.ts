/**
 * 语言检测工具函数
 * 用于检测浏览器语言并与应用支持的语言进行匹配
 */

import { locales } from "@/i18n/locale";

/**
 * 语言映射表 - 将浏览器语言代码映射到应用支持的语言
 */
const LANGUAGE_MAPPING: Record<string, string> = {
  // 英语变体
  "en": "en",
  "en-US": "en",
  "en-GB": "en",
  "en-CA": "en",
  "en-AU": "en",
  
  // 中文变体
  "zh": "zh",
  "zh-CN": "zh",
  "zh-TW": "zh",
  "zh-HK": "zh",
  "zh-MO": "zh",
  "zh-SG": "zh",
};

/**
 * 获取浏览器的首选语言
 * @returns 浏览器的首选语言代码，如果无法获取则返回null
 */
export function getBrowserLanguage(): string | null {
  if (typeof window === "undefined") {
    return null;
  }

  // 优先使用 navigator.language
  if (navigator.language) {
    return navigator.language;
  }

  // 备选方案：使用 navigator.languages 的第一个
  if (navigator.languages && navigator.languages.length > 0) {
    return navigator.languages[0];
  }

  return null;
}

/**
 * 获取浏览器的所有语言偏好（按优先级排序）
 * @returns 语言代码数组
 */
export function getBrowserLanguages(): string[] {
  if (typeof window === "undefined") {
    return [];
  }

  const languages: string[] = [];

  // 添加 navigator.language
  if (navigator.language) {
    languages.push(navigator.language);
  }

  // 添加 navigator.languages
  if (navigator.languages) {
    navigator.languages.forEach(lang => {
      if (!languages.includes(lang)) {
        languages.push(lang);
      }
    });
  }

  return languages;
}

/**
 * 将浏览器语言代码映射到应用支持的语言
 * @param browserLang 浏览器语言代码
 * @returns 应用支持的语言代码，如果不支持则返回null
 */
export function mapBrowserLanguageToAppLanguage(browserLang: string): string | null {
  // 直接匹配
  if (LANGUAGE_MAPPING[browserLang]) {
    return LANGUAGE_MAPPING[browserLang];
  }

  // 尝试匹配语言的主要部分（去掉地区代码）
  const mainLang = browserLang.split("-")[0];
  if (LANGUAGE_MAPPING[mainLang]) {
    return LANGUAGE_MAPPING[mainLang];
  }

  return null;
}

/**
 * 检查应用是否支持指定的语言
 * @param language 语言代码
 * @returns 是否支持该语言
 */
export function isLanguageSupported(language: string): boolean {
  return locales.includes(language as any);
}

/**
 * 获取应用支持的浏览器语言
 * 按浏览器语言偏好顺序返回第一个支持的语言
 * @returns 应用支持的语言代码，如果都不支持则返回null
 */
export function getSupportedBrowserLanguage(): string | null {
  const browserLanguages = getBrowserLanguages();
  
  for (const browserLang of browserLanguages) {
    const appLang = mapBrowserLanguageToAppLanguage(browserLang);
    if (appLang && isLanguageSupported(appLang)) {
      return appLang;
    }
  }

  return null;
}

/**
 * 检查浏览器语言是否与当前页面语言不同
 * @param currentLocale 当前页面的语言
 * @returns 如果浏览器语言与当前语言不同且应用支持浏览器语言，则返回建议的语言，否则返回null
 */
export function shouldSuggestLanguageSwitch(currentLocale: string): string | null {
  const supportedBrowserLang = getSupportedBrowserLanguage();
  
  // 如果浏览器语言不被支持，或者与当前语言相同，则不建议切换
  if (!supportedBrowserLang || supportedBrowserLang === currentLocale) {
    return null;
  }

  return supportedBrowserLang;
}

/**
 * 获取语言的显示名称
 * @param language 语言代码
 * @returns 语言的显示名称
 */
export function getLanguageDisplayName(language: string): string {
  const displayNames: Record<string, string> = {
    "en": "English",
    "zh": "中文",
  };

  return displayNames[language] || language;
}

/**
 * 调试函数：获取语言检测的详细信息
 * @param currentLocale 当前页面语言
 * @returns 语言检测的详细信息
 */
export function getLanguageDetectionInfo(currentLocale: string) {
  const browserLanguage = getBrowserLanguage();
  const browserLanguages = getBrowserLanguages();
  const supportedBrowserLang = getSupportedBrowserLanguage();
  const suggestedLang = shouldSuggestLanguageSwitch(currentLocale);

  return {
    browserLanguage,
    browserLanguages,
    supportedBrowserLang,
    currentLocale,
    suggestedLang,
    appSupportedLanguages: locales,
    languageMapping: LANGUAGE_MAPPING,
  };
}
