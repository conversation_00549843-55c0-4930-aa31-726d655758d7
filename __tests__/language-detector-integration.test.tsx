/**
 * 语言检测器组件的集成测试
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useParams, useRouter, usePathname } from "next/navigation";
import LanguageDetector from "@/components/language/detector";

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useParams: jest.fn(),
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock next-intl
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string, params?: Record<string, string>) => {
    const translations: Record<string, string> = {
      "title": "Switch Language?",
      "description": "We detected that you might prefer to use {suggestedLanguage}. Would you like to switch to {suggestedLanguage}?",
      "switch_button": "Switch to {suggestedLanguage}",
      "cancel_button": "Keep {currentLanguage}",
    };

    let text = translations[key] || key;

    // 简单的参数替换
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        text = text.replace(new RegExp(`{${paramKey}}`, 'g'), paramValue);
      });
    }

    return text;
  },
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Mock navigator
const mockNavigator = (language: string, languages: string[]) => {
  Object.defineProperty(window, "navigator", {
    value: {
      language,
      languages,
    },
    writable: true,
  });
};

describe("LanguageDetector Integration", () => {
  const mockPush = jest.fn();
  const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
  const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

  beforeEach(() => {
    localStorageMock.clear();
    mockPush.mockClear();
    
    mockUseParams.mockReturnValue({ locale: "en" });
    mockUseRouter.mockReturnValue({ push: mockPush } as any);
    mockUsePathname.mockReturnValue("/en/dashboard");
  });

  it("should show language switch dialog when browser language differs", async () => {
    // 设置浏览器语言为中文，当前页面为英文
    mockNavigator("zh-CN", ["zh-CN", "en-US"]);

    render(<LanguageDetector detectionDelay={100} />);

    // 等待检测完成并显示弹框
    await waitFor(() => {
      expect(screen.getByText("Switch Language?")).toBeInTheDocument();
    });

    expect(screen.getByText(/We detected that you might prefer to use 中文/)).toBeInTheDocument();
  });

  it("should not show dialog when browser language same as current", async () => {
    // 设置浏览器语言为英文，当前页面也为英文
    mockNavigator("en-US", ["en-US", "zh-CN"]);

    render(<LanguageDetector detectionDelay={100} />);

    // 等待检测完成
    await waitFor(() => {
      // 应该没有弹框显示
      expect(screen.queryByText("Switch Language?")).not.toBeInTheDocument();
    }, { timeout: 500 });
  });

  it("should handle language switch when user accepts", async () => {
    mockNavigator("zh-CN", ["zh-CN", "en-US"]);

    render(<LanguageDetector detectionDelay={100} />);

    // 等待弹框显示
    await waitFor(() => {
      expect(screen.getByText("Switch Language?")).toBeInTheDocument();
    });

    // 点击切换按钮
    const switchButton = screen.getByText(/Switch to 中文/);
    fireEvent.click(switchButton);

    // 验证路由跳转
    expect(mockPush).toHaveBeenCalledWith("/zh/dashboard");
  });

  it("should handle language switch decline", async () => {
    mockNavigator("zh-CN", ["zh-CN", "en-US"]);

    render(<LanguageDetector detectionDelay={100} />);

    // 等待弹框显示
    await waitFor(() => {
      expect(screen.getByText("Switch Language?")).toBeInTheDocument();
    });

    // 点击保持当前语言按钮
    const cancelButton = screen.getByText(/Keep English/);
    fireEvent.click(cancelButton);

    // 验证弹框关闭
    await waitFor(() => {
      expect(screen.queryByText("Switch Language?")).not.toBeInTheDocument();
    });

    // 验证没有路由跳转
    expect(mockPush).not.toHaveBeenCalled();
  });

  it("should not show dialog again after user declined", async () => {
    mockNavigator("zh-CN", ["zh-CN", "en-US"]);

    // 第一次渲染
    const { unmount } = render(<LanguageDetector detectionDelay={100} />);

    // 等待弹框显示并拒绝
    await waitFor(() => {
      expect(screen.getByText("Switch Language?")).toBeInTheDocument();
    });

    const cancelButton = screen.getByText(/Keep English/);
    fireEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText("Switch Language?")).not.toBeInTheDocument();
    });

    // 卸载组件
    unmount();

    // 重新渲染组件（模拟页面刷新）
    render(<LanguageDetector detectionDelay={100} />);

    // 等待一段时间，确保不会再次显示弹框
    await waitFor(() => {
      expect(screen.queryByText("Switch Language?")).not.toBeInTheDocument();
    }, { timeout: 500 });
  });

  it("should use custom language switch handler when provided", async () => {
    const customHandler = jest.fn();
    mockNavigator("zh-CN", ["zh-CN", "en-US"]);

    render(
      <LanguageDetector 
        detectionDelay={100} 
        onLanguageSwitch={customHandler}
      />
    );

    // 等待弹框显示
    await waitFor(() => {
      expect(screen.getByText("Switch Language?")).toBeInTheDocument();
    });

    // 点击切换按钮
    const switchButton = screen.getByText(/Switch to 中文/);
    fireEvent.click(switchButton);

    // 验证自定义处理器被调用
    expect(customHandler).toHaveBeenCalledWith("en", "zh");
    
    // 验证默认路由跳转没有发生
    expect(mockPush).not.toHaveBeenCalled();
  });

  it("should show debug info when debug mode is enabled", async () => {
    mockNavigator("zh-CN", ["zh-CN", "en-US"]);

    render(<LanguageDetector debug={true} detectionDelay={100} />);

    // 等待检测完成
    await waitFor(() => {
      expect(screen.getByText("Language Detection Debug")).toBeInTheDocument();
    });

    expect(screen.getByText("Current: en")).toBeInTheDocument();
    expect(screen.getByText("Suggested: zh")).toBeInTheDocument();
    expect(screen.getByText("Show Dialog: Yes")).toBeInTheDocument();
  });

  it("should not show dialog for unsupported browser languages", async () => {
    // 设置浏览器语言为不支持的语言
    mockNavigator("fr-FR", ["fr-FR", "de-DE"]);

    render(<LanguageDetector detectionDelay={100} />);

    // 等待检测完成
    await waitFor(() => {
      expect(screen.queryByText("Switch Language?")).not.toBeInTheDocument();
    }, { timeout: 500 });
  });
});
