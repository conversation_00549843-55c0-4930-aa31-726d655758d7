/**
 * 语言检测功能的单元测试
 */

import {
  getBrowserLanguage,
  getBrowserLanguages,
  mapBrowserLanguageToAppLanguage,
  isLanguageSupported,
  getSupportedBrowserLanguage,
  shouldSuggestLanguageSwitch,
  getLanguageDisplayName,
  getLanguageDetectionInfo,
} from "@/lib/language-detection";

import {
  saveLanguagePreference,
  getLanguagePreference,
  removeLanguagePreference,
  recordLanguageSwitchAsked,
  getLanguageSwitchAsked,
  clearLanguageSwitchAsked,
  shouldShowLanguageSuggestion,
  saveLanguageSwitchAccepted,
  saveLanguageSwitchDeclined,
} from "@/lib/language-preference";

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Mock navigator
const mockNavigator = (language: string, languages: string[]) => {
  Object.defineProperty(window, "navigator", {
    value: {
      language,
      languages,
    },
    writable: true,
  });
};

describe("Language Detection", () => {
  beforeEach(() => {
    localStorageMock.clear();
  });

  describe("getBrowserLanguage", () => {
    it("should return navigator.language when available", () => {
      mockNavigator("zh-CN", ["zh-CN", "en-US"]);
      expect(getBrowserLanguage()).toBe("zh-CN");
    });

    it("should return first language from navigator.languages when navigator.language is not available", () => {
      mockNavigator("", ["zh-CN", "en-US"]);
      expect(getBrowserLanguage()).toBe("zh-CN");
    });

    it("should return null when no language is available", () => {
      mockNavigator("", []);
      expect(getBrowserLanguage()).toBeNull();
    });
  });

  describe("getBrowserLanguages", () => {
    it("should return all unique languages", () => {
      mockNavigator("zh-CN", ["zh-CN", "en-US", "zh-CN"]);
      const languages = getBrowserLanguages();
      expect(languages).toEqual(["zh-CN", "en-US"]);
    });

    it("should include navigator.language even if not in navigator.languages", () => {
      mockNavigator("fr-FR", ["zh-CN", "en-US"]);
      const languages = getBrowserLanguages();
      expect(languages).toEqual(["fr-FR", "zh-CN", "en-US"]);
    });
  });

  describe("mapBrowserLanguageToAppLanguage", () => {
    it("should map zh-CN to zh", () => {
      expect(mapBrowserLanguageToAppLanguage("zh-CN")).toBe("zh");
    });

    it("should map en-US to en", () => {
      expect(mapBrowserLanguageToAppLanguage("en-US")).toBe("en");
    });

    it("should map zh to zh", () => {
      expect(mapBrowserLanguageToAppLanguage("zh")).toBe("zh");
    });

    it("should return null for unsupported language", () => {
      expect(mapBrowserLanguageToAppLanguage("fr-FR")).toBeNull();
    });

    it("should map main language when full locale is not found", () => {
      expect(mapBrowserLanguageToAppLanguage("en-GB")).toBe("en");
    });
  });

  describe("isLanguageSupported", () => {
    it("should return true for supported languages", () => {
      expect(isLanguageSupported("en")).toBe(true);
      expect(isLanguageSupported("zh")).toBe(true);
    });

    it("should return false for unsupported languages", () => {
      expect(isLanguageSupported("fr")).toBe(false);
      expect(isLanguageSupported("de")).toBe(false);
    });
  });

  describe("getSupportedBrowserLanguage", () => {
    it("should return first supported language", () => {
      mockNavigator("zh-CN", ["zh-CN", "en-US"]);
      expect(getSupportedBrowserLanguage()).toBe("zh");
    });

    it("should return null when no supported language found", () => {
      mockNavigator("fr-FR", ["fr-FR", "de-DE"]);
      expect(getSupportedBrowserLanguage()).toBeNull();
    });

    it("should skip unsupported languages and return first supported one", () => {
      mockNavigator("fr-FR", ["fr-FR", "de-DE", "en-US"]);
      expect(getSupportedBrowserLanguage()).toBe("en");
    });
  });

  describe("shouldSuggestLanguageSwitch", () => {
    it("should suggest switch when browser language differs from current", () => {
      mockNavigator("zh-CN", ["zh-CN", "en-US"]);
      expect(shouldSuggestLanguageSwitch("en")).toBe("zh");
    });

    it("should not suggest switch when browser language same as current", () => {
      mockNavigator("en-US", ["en-US", "zh-CN"]);
      expect(shouldSuggestLanguageSwitch("en")).toBeNull();
    });

    it("should not suggest switch when browser language not supported", () => {
      mockNavigator("fr-FR", ["fr-FR", "de-DE"]);
      expect(shouldSuggestLanguageSwitch("en")).toBeNull();
    });
  });

  describe("getLanguageDisplayName", () => {
    it("should return correct display names", () => {
      expect(getLanguageDisplayName("en")).toBe("English");
      expect(getLanguageDisplayName("zh")).toBe("中文");
    });

    it("should return language code for unknown languages", () => {
      expect(getLanguageDisplayName("fr")).toBe("fr");
    });
  });
});

describe("Language Preference", () => {
  beforeEach(() => {
    localStorageMock.clear();
  });

  describe("saveLanguagePreference and getLanguagePreference", () => {
    it("should save and retrieve language preference", () => {
      const preference = {
        selectedLanguage: "zh",
        detectedLanguage: "zh",
        declined: false,
        timestamp: 1234567890,
      };

      saveLanguagePreference(preference);
      const retrieved = getLanguagePreference();

      expect(retrieved).toEqual(preference);
    });

    it("should return null when no preference saved", () => {
      expect(getLanguagePreference()).toBeNull();
    });

    it("should handle corrupted data gracefully", () => {
      localStorageMock.setItem("LANGUAGE_PREFERENCE", "-1:invalid-json");
      expect(getLanguagePreference()).toBeNull();
    });
  });

  describe("recordLanguageSwitchAsked and getLanguageSwitchAsked", () => {
    it("should record and retrieve language switch asked", () => {
      recordLanguageSwitchAsked("en", "zh", "accepted");
      const record = getLanguageSwitchAsked("en", "zh");

      expect(record).toBeTruthy();
      expect(record?.choice).toBe("accepted");
      expect(record?.languagePair).toBe("en-zh");
    });

    it("should return null for different language pair", () => {
      recordLanguageSwitchAsked("en", "zh", "accepted");
      const record = getLanguageSwitchAsked("zh", "en");

      expect(record).toBeNull();
    });

    it("should return null when no record exists", () => {
      const record = getLanguageSwitchAsked("en", "zh");
      expect(record).toBeNull();
    });
  });

  describe("shouldShowLanguageSuggestion", () => {
    it("should show suggestion when no preference exists", () => {
      expect(shouldShowLanguageSuggestion("en", "zh")).toBe(true);
    });

    it("should not show suggestion when user already chose current language", () => {
      saveLanguagePreference({
        selectedLanguage: "en",
        detectedLanguage: "zh",
        declined: false,
        timestamp: Date.now(),
      });

      expect(shouldShowLanguageSuggestion("en", "zh")).toBe(false);
    });

    it("should not show suggestion when user previously declined", () => {
      saveLanguagePreference({
        selectedLanguage: "en",
        detectedLanguage: "zh",
        declined: true,
        timestamp: Date.now(),
      });

      expect(shouldShowLanguageSuggestion("en", "zh")).toBe(false);
    });

    it("should not show suggestion when user previously declined the switch", () => {
      recordLanguageSwitchAsked("en", "zh", "declined");
      expect(shouldShowLanguageSuggestion("en", "zh")).toBe(false);
    });

    it("should not show suggestion when user previously accepted the switch", () => {
      recordLanguageSwitchAsked("en", "zh", "accepted");
      expect(shouldShowLanguageSuggestion("en", "zh")).toBe(false);
    });
  });

  describe("saveLanguageSwitchAccepted", () => {
    it("should save accepted preference and record", () => {
      saveLanguageSwitchAccepted("en", "zh", "zh");

      const preference = getLanguagePreference();
      expect(preference?.selectedLanguage).toBe("zh");
      expect(preference?.declined).toBe(false);

      const record = getLanguageSwitchAsked("en", "zh");
      expect(record?.choice).toBe("accepted");
    });
  });

  describe("saveLanguageSwitchDeclined", () => {
    it("should save declined preference and record", () => {
      saveLanguageSwitchDeclined("en", "zh", "zh");

      const preference = getLanguagePreference();
      expect(preference?.selectedLanguage).toBe("en");
      expect(preference?.declined).toBe(true);

      const record = getLanguageSwitchAsked("en", "zh");
      expect(record?.choice).toBe("declined");
    });
  });

  describe("removeLanguagePreference and clearLanguageSwitchAsked", () => {
    it("should remove preference", () => {
      saveLanguagePreference({
        selectedLanguage: "zh",
        detectedLanguage: "zh",
        declined: false,
        timestamp: Date.now(),
      });

      removeLanguagePreference();
      expect(getLanguagePreference()).toBeNull();
    });

    it("should clear switch asked record", () => {
      recordLanguageSwitchAsked("en", "zh", "accepted");
      clearLanguageSwitchAsked();
      expect(getLanguageSwitchAsked("en", "zh")).toBeNull();
    });
  });
});
