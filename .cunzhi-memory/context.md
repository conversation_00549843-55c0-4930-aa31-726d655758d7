# 项目上下文信息

- GRSAI提供商集成项目已完成，包括：1)14个AI模型配置(文本/图像/视频/多模态) 2)完整的API调用逻辑和异步任务处理 3)积分系统集成和合理定价 4)文件自动转存到R2存储 5)前端组件和测试页面(/ai-model-test) 6)数据库表结构和视图 7)按million tokens计费的文本模型
- 网站主题重新设计为"AI一站式工具平台"，包含对话AI模型、图片生成、视频生成、语音合成、语音复刻等功能模块，支持中英文双语
- 用户要求在 components/ai-dashboard-branch/ 目录下实现响应式布局框架，包括：1) ResponsiveContainer 组件处理布局容器 2) useResponsiveStyles 和 useDeviceLayout hooks 3) 将布局逻辑、样式配置和业务逻辑完全分离 4) 严格限制改动范围在该目录下
