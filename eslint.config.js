import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

export default [
  ...compat.extends("next/core-web-vitals"),
  {
    rules: {
      "import/no-anonymous-default-export": "off",
      "react/display-name": "off",
      "react-hooks/exhaustive-deps": "off",
      "react-hooks/rules-of-hooks": "off",
      "react/no-unescaped-entities": "off",
      "jsx-a11y/alt-text": "off",
      "@next/next/no-img-element": "off",
      "@next/next/no-html-link-for-pages": "off"
    }
  },
  {
    plugins: {
      "cloudflare-edge": {
        rules: {
          "require-edge-runtime": {
            meta: {
              type: "problem",
              docs: {
                description: "require export const runtime = 'edge' in page.tsx and route.ts files for Cloudflare Pages deployment",
                category: "Possible Errors",
                recommended: true,
              },
              fixable: "code",
              schema: [],
            },
            create(context) {
              const filename = context.getFilename();
              const isPageOrRoute = /\/(page\.tsx|route\.ts)$/.test(filename);
              
              if (!isPageOrRoute) {
                return {};
              }

              let hasRuntimeExport = false;
              let hasEdgeRuntime = false;

              return {
                ExportNamedDeclaration(node) {
                  if (node.declaration?.type === "VariableDeclaration") {
                    const declaration = node.declaration.declarations[0];
                    if (declaration?.id?.name === "runtime") {
                      hasRuntimeExport = true;
                      if (declaration.init?.value === "edge") {
                        hasEdgeRuntime = true;
                      }
                    }
                  }
                },
                "Program:exit"() {
                  if (!hasRuntimeExport) {
                    context.report({
                      node: context.getSourceCode().ast,
                      message: "Missing 'export const runtime = \"edge\"' for Cloudflare Pages deployment",
                      fix(fixer) {
                        const sourceCode = context.getSourceCode();
                        const firstImport = sourceCode.ast.body.find(node => node.type === "ImportDeclaration");
                        const insertAfter = firstImport || sourceCode.ast.body[0];
                        const insertPosition = insertAfter ? sourceCode.getTokenAfter(insertAfter).range[0] : 0;
                        
                        return fixer.insertTextAfterRange(
                          [insertPosition - 1, insertPosition - 1],
                          '\nexport const runtime = "edge";\n'
                        );
                      }
                    });
                  } else if (!hasEdgeRuntime) {
                    context.report({
                      node: context.getSourceCode().ast,
                      message: "Runtime should be set to 'edge' for Cloudflare Pages deployment",
                    });
                  }
                }
              };
            }
          }
        }
      }
    },
    rules: {
      "cloudflare-edge/require-edge-runtime": "error"
    },
    files: ["app/**/page.tsx", "app/**/route.ts"]
  }
];
