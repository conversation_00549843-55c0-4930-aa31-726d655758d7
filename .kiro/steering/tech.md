# Technology Stack

## Framework & Runtime
- **Next.js 15.2.3**: React framework with App Router
- **React 18**: UI library with Server Components
- **TypeScript**: Type-safe development
- **Node.js**: Runtime environment
- **Edge Runtime**: For API routes requiring fast response times

## Database & Storage
- **Supabase**: PostgreSQL database with real-time features
- **AWS S3**: File storage for generated content
- **Supabase Auth**: User authentication and session management

## UI & Styling
- **Tailwind CSS 4.1.4**: Utility-first CSS framework
- **shadcn/ui**: Component library built on Radix UI
- **Radix UI**: Headless UI primitives
- **Framer Motion**: Animation library
- **Lucide React**: Icon library

## AI & External Services
- **AI SDK**: Vercel's AI SDK for model integration
- **GRSAI Provider**: Custom AI service provider
- **OpenAI**: Text generation models
- **Multiple AI Providers**: DeepSeek, Replicate, OpenRouter support

## Authentication & Payments
- **NextAuth.js 5.0**: Authentication framework
- **Stripe**: Payment processing
- **Google One Tap**: Streamlined Google authentication

## Internationalization
- **next-intl**: Internationalization framework
- **Locale Detection**: Automatic language detection
- **Multi-language Support**: EN, ZH, JA, KO, RU, FR, DE, AR, ES, IT

## Development Tools
- **Jest**: Testing framework
- **ESLint**: Code linting
- **TypeScript**: Static type checking
- **Bundle Analyzer**: Build analysis

## Deployment Options
- **Vercel**: Primary deployment platform
- **Cloudflare Pages**: Alternative deployment with Workers
- **Docker**: Containerized deployment
- **Standalone Output**: Self-hosted deployment

## Common Commands

### Development
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

### Testing
```bash
pnpm test         # Run tests
pnpm test:watch   # Run tests in watch mode
pnpm test:coverage # Run tests with coverage
```

### Analysis & Deployment
```bash
pnpm analyze      # Analyze bundle size
pnpm cf:build     # Build for Cloudflare
pnpm cf:preview   # Preview Cloudflare deployment
pnpm cf:deploy    # Deploy to Cloudflare
```

### Docker
```bash
pnpm docker:build # Build Docker image
```

## Environment Configuration
- Copy `.env.example` to `.env.local` for development
- Configure Supabase, authentication providers, Stripe, and storage
- Set up analytics (Google Analytics, OpenPanel, Plausible)
- Configure AI provider API keys