# Project Structure

## Root Level Organization
- **Configuration Files**: Package management, build tools, deployment configs
- **Environment Files**: `.env.example`, `.env.local`, `.env.production`
- **Documentation**: `README.md`, `docs/` folder for detailed documentation

## Core Application Structure

### `/app` - Next.js App Router
```
app/
├── [locale]/           # Internationalized routes
│   ├── (admin)/       # Admin dashboard routes (grouped)
│   ├── (default)/     # Main application routes (grouped)
│   └── auth/          # Authentication pages
├── api/               # API routes
│   ├── ai/           # AI-related endpoints
│   ├── auth/         # NextAuth endpoints
│   └── checkout/     # Payment endpoints
└── globals.css        # Global styles
```

### `/components` - Reusable UI Components
```
components/
├── ui/               # shadcn/ui base components
├── blocks/           # Complex UI blocks (hero, pricing, etc.)
├── ai/              # AI-specific components
├── dashboard/       # Dashboard-specific components
├── console/         # Admin console components
└── [feature]/       # Feature-specific components
```

### `/services` - Business Logic Layer
- **API Integration**: External service clients (GRSAI, Stripe)
- **Business Logic**: Credit management, user operations
- **Data Access**: Database operations and caching

### `/models` - Data Models & Database
- **Database Client**: Supabase client configuration
- **Type Definitions**: Database schema types
- **Query Functions**: Reusable database queries

### `/types` - TypeScript Definitions
```
types/
├── blocks/          # UI block type definitions
├── pages/           # Page-specific types
├── slots/           # Reusable component slot types
└── [feature].d.ts   # Feature-specific types
```

### `/i18n` - Internationalization
```
i18n/
├── messages/        # Translation files (en.json, zh.json)
├── pages/          # Page-specific translations
├── locale.ts       # Locale configuration
└── routing.ts      # Internationalized routing
```

## Key Architectural Patterns

### Route Organization
- **Route Groups**: Use `(groupName)` for logical organization without affecting URLs
- **Dynamic Routes**: `[locale]` for internationalization, `[slug]` for dynamic content
- **API Routes**: Separate by feature area (`/api/ai/`, `/api/auth/`)

### Component Architecture
- **Atomic Design**: UI components in `/components/ui/`
- **Block Components**: Complex components in `/components/blocks/`
- **Feature Components**: Domain-specific components grouped by feature
- **Layout Components**: Reusable layout patterns

### Data Flow
- **Server Components**: Default for data fetching and rendering
- **Client Components**: Use `"use client"` directive sparingly
- **API Layer**: Centralized in `/services/` for external integrations
- **Database Layer**: Models in `/models/` with typed queries

### File Naming Conventions
- **Components**: PascalCase for component files (`AIGenerator.tsx`)
- **Pages**: lowercase with hyphens (`ai-dashboard/`)
- **API Routes**: lowercase with hyphens (`/api/ai/generate/`)
- **Types**: kebab-case with `.d.ts` extension
- **Services**: kebab-case (e.g., `grsai-provider.ts`)

### Import Patterns
- **Absolute Imports**: Use `@/` prefix for all internal imports
- **Path Aliases**: Configured in `tsconfig.json` and `components.json`
- **Barrel Exports**: Use `index.ts` files for clean imports

## Special Directories

### `/aisdk` - Custom AI SDK
- Custom AI provider implementations
- Video generation utilities
- Provider abstractions

### `/auth` - Authentication Configuration
- NextAuth.js configuration
- Provider setup
- Session management

### `/hooks` - Custom React Hooks
- Reusable stateful logic
- UI interaction hooks
- Data fetching hooks

### `/lib` - Utility Functions
- Helper functions
- Common utilities
- Shared business logic

### `/public` - Static Assets
- Images organized by purpose (`/imgs/features/`, `/imgs/users/`)
- Icons and logos
- Static files (robots.txt, sitemap.xml)

## Development Guidelines

### Component Creation
1. Start with UI components in `/components/ui/`
2. Build feature components in appropriate feature folders
3. Create page components in `/app/[locale]/` structure
4. Add types in `/types/` with proper organization

### API Development
1. Create API routes in `/app/api/[feature]/`
2. Implement business logic in `/services/`
3. Define data models in `/models/`
4. Add proper TypeScript types

### Internationalization
1. Add translations to `/i18n/messages/`
2. Create page-specific translations in `/i18n/pages/`
3. Use `next-intl` hooks in components
4. Test with different locales