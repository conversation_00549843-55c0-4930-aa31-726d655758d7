export const runtime = 'edge';

export default function TermsOfServicePage() {
  const brandName = process.env.NEXT_PUBLIC_BRAND_NAME || 'Your Brand';
  const brandDomain = process.env.NEXT_PUBLIC_BRAND_DOMAIN || 'yourdomain.com';
  const supportEmail = process.env.NEXT_PUBLIC_SUPPORT_EMAIL || '<EMAIL>';
  const serviceDescription = process.env.NEXT_PUBLIC_SERVICE_DESCRIPTION || 'our platform';

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <article className="prose prose-lg dark:prose-invert mx-auto">
        <h1>Terms of Service</h1>

        <h2>Introduction and Acceptance of Terms</h2>

        <p>
          Welcome to <strong>{brandName}</strong>, {serviceDescription} designed for building and launching innovative solutions quickly and efficiently. By accessing or using our service, you agree to be bound by these Terms of Service. If you do not agree with any of these terms, please do not use our service.
        </p>

        <h2>Use of the Service</h2>

        <p>
          {brandName} provides users with a comprehensive platform and tools to build and launch their projects using our services and infrastructure. You agree to use the service in accordance with all applicable local, state, national, and international laws and regulations.
        </p>

        <h2>User Accounts and Registration</h2>

        <ol>
          <li><strong>Account Creation</strong>: To access certain features of the service, you need to create an account. You must provide accurate and complete information during the registration process.</li>

          <li><strong>Account Security</strong>: You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</li>

          <li><strong>User Responsibilities</strong>: You agree to notify us immediately of any unauthorized use of your account or any other breach of security.</li>
        </ol>

        <h2>Content and Intellectual Property Rights</h2>

        <p>
          All content provided through {brandName}, including but not limited to software, services, documentation, and related materials, is protected under copyright law. The copyright owner of {brandName} is <strong>{brandDomain}</strong>.
        </p>

        <ul>
          <li>You acknowledge that you do not own the underlying technology or intellectual property that makes up the {brandName} service, and you agree to respect the intellectual property rights of {brandDomain} and any third parties.</li>
          <li>While you retain ownership of your custom implementations and modifications, the core {brandName} platform and services remain the property of {brandDomain}.</li>
        </ul>

        <h2>Prohibited Activities</h2>

        <p>You agree not to engage in any of the following prohibited activities while using {brandName}:</p>

        <ul>
          <li>Unauthorized access to or distribution of our proprietary content and services</li>
          <li>Reselling or redistributing {brandName} services without authorization</li>
          <li>Interfering with or disrupting the security or performance of the service</li>
          <li>Using the service for any illegal or unauthorized purpose</li>
          <li>Attempting to bypass any security features of the service</li>
        </ul>

        <h2>Privacy and Data Collection</h2>

        <p>{brandName} collects the following types of data:</p>

        <ul>
          <li><strong>Account Information</strong>: Information you provide when creating an account</li>
          <li><strong>Usage Details</strong>: Data related to your activity on our service</li>
          <li><strong>Device Information</strong>: Information about the device you use to access our service</li>
          <li><strong>Cookies</strong>: Data that helps us enhance your experience with our service</li>
          <li><strong>Payment and Billing Information</strong>: Data necessary to process payments</li>
        </ul>

        <p>
          For more details on data collection practices, please refer to our separate{' '}
          <a href="/privacy-policy">Privacy Policy</a>.
        </p>

        <h2>Pricing and Payments</h2>

        <ul>
          <li>All purchases are final and non-refundable unless otherwise required by law</li>
          <li>Prices are subject to change with notice to users</li>
          <li>You agree to pay all charges associated with your selected plan</li>
          <li>Payment terms are based on your selected payment method and plan</li>
        </ul>

        <h2>Termination</h2>

        <p>
          We reserve the right to terminate or suspend your access to the service at our sole discretion, without notice, for conduct that we believe violates these Terms or is harmful to other users of the service, us, or third parties.
        </p>

        <h2>Disclaimer of Warranties</h2>

        <p>
          The service is provided on an "as is" and "as available" basis. We make no warranties or representations about the accuracy, reliability, or availability of the service and disclaim all warranties to the fullest extent permitted by law.
        </p>

        <h2>Limitation of Liability</h2>

        <p>
          To the fullest extent permitted by law, {brandDomain} shall not be liable for any direct, indirect, incidental, special, consequential, or punitive damages arising from the use of or inability to use the service.
        </p>

        <h2>Indemnification</h2>

        <p>
          You agree to indemnify and hold harmless {brandDomain}, its affiliates, and their respective officers, directors, employees, and agents from any claims, damages, losses, liabilities, and expenses (including attorneys' fees) arising from your use of the service or violation of these Terms.
        </p>

        <h2>Governing Law and Dispute Resolution</h2>

        <p>
          These Terms shall be governed by and construed in accordance with the laws of the jurisdiction in which {brandDomain} operates, without regard to its conflict of law provisions. Any disputes arising from these Terms or the service will be resolved through binding arbitration in accordance with applicable laws.
        </p>

        <h2>Changes to These Terms</h2>

        <p>
          We reserve the right to update or modify these Terms at any time. Changes will be effective immediately upon posting on our website. Your continued use of the service after any changes signifies your acceptance of the new terms.
        </p>

        <h2>Contact Information</h2>

        <p>
          If you have any questions about these Terms, please contact us at{' '}
          <a href={`mailto:${supportEmail}`}>{supportEmail}</a>.
        </p>

        <hr />

        <p>
          By using {brandName}, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. Thank you for choosing {brandName}!
        </p>
      </article>
    </div>
  );
}
