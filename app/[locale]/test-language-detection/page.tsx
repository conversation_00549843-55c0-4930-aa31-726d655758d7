"use client";

import React, { useState } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  getLanguageDetectionInfo,
  getBrowserLanguage,
  getSupportedBrowserLanguage,
  shouldSuggestLanguageSwitch 
} from "@/lib/language-detection";
import {
  getLanguagePreference,
  getLanguagePreferenceDebugInfo,
  removeLanguagePreference,
  clearLanguageSwitchAsked
} from "@/lib/language-preference";
import { DebugLanguageDetector } from "@/components/language/detector";
export const runtime = "edge";

export default function TestLanguageDetectionPage() {
  const params = useParams();
  const currentLocale = params.locale as string;
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [preferenceInfo, setPreferenceInfo] = useState<any>(null);

  const refreshDebugInfo = () => {
    const info = getLanguageDetectionInfo(currentLocale);
    const prefInfo = getLanguagePreferenceDebugInfo();
    setDebugInfo(info);
    setPreferenceInfo(prefInfo);
  };

  const clearAllPreferences = () => {
    removeLanguagePreference();
    clearLanguageSwitchAsked();
    refreshDebugInfo();
    alert("所有语言偏好已清除！刷新页面以重新测试。");
  };

  React.useEffect(() => {
    refreshDebugInfo();
  }, [currentLocale]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">语言检测功能测试</h1>
        <p className="text-muted-foreground">
          当前语言: <Badge variant="outline">{currentLocale}</Badge>
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 浏览器语言信息 */}
        <Card>
          <CardHeader>
            <CardTitle>浏览器语言信息</CardTitle>
            <CardDescription>检测到的浏览器语言设置</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>navigator.language:</strong> {getBrowserLanguage() || "未检测到"}
            </div>
            <div>
              <strong>支持的浏览器语言:</strong> {getSupportedBrowserLanguage() || "无"}
            </div>
            <div>
              <strong>建议切换到:</strong> {shouldSuggestLanguageSwitch(currentLocale) || "无建议"}
            </div>
          </CardContent>
        </Card>

        {/* 用户偏好信息 */}
        <Card>
          <CardHeader>
            <CardTitle>用户偏好信息</CardTitle>
            <CardDescription>保存的用户语言偏好</CardDescription>
          </CardHeader>
          <CardContent>
            {preferenceInfo?.preference ? (
              <div className="space-y-2">
                <div><strong>选择的语言:</strong> {preferenceInfo.preference.selectedLanguage}</div>
                <div><strong>检测到的语言:</strong> {preferenceInfo.preference.detectedLanguage}</div>
                <div><strong>是否拒绝:</strong> {preferenceInfo.preference.declined ? "是" : "否"}</div>
                <div><strong>时间戳:</strong> {new Date(preferenceInfo.preference.timestamp * 1000).toLocaleString()}</div>
              </div>
            ) : (
              <p className="text-muted-foreground">暂无保存的偏好</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 详细调试信息 */}
      {debugInfo && (
        <Card>
          <CardHeader>
            <CardTitle>详细调试信息</CardTitle>
            <CardDescription>完整的语言检测信息</CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-4 justify-center">
        <Button onClick={refreshDebugInfo} variant="outline">
          刷新调试信息
        </Button>
        <Button onClick={clearAllPreferences} variant="destructive">
          清除所有偏好
        </Button>
        <Button onClick={() => window.location.reload()} variant="secondary">
          刷新页面
        </Button>
      </div>

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">如何测试语言检测功能：</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>确保浏览器语言设置与当前页面语言不同</li>
              <li>清除所有偏好设置</li>
              <li>刷新页面，应该会看到语言切换弹框</li>
              <li>选择"切换"或"保持"来测试不同的用户选择</li>
              <li>再次刷新页面，验证不会重复显示弹框</li>
            </ol>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">浏览器语言设置方法：</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>Chrome:</strong> 设置 → 高级 → 语言</li>
              <li><strong>Firefox:</strong> 首选项 → 常规 → 语言</li>
              <li><strong>Safari:</strong> 偏好设置 → 高级 → 语言</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 调试模式的语言检测器 */}
      <DebugLanguageDetector />
    </div>
  );
}
