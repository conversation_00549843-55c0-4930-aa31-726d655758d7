'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Play, Upload, Mic, Volume2 } from 'lucide-react';

interface TTSResult {
  reqid: string;
  audio_data: string;
  duration: string;
  format: string;
}

interface AsyncTTSResult {
  task_id: string;
  status: string;
  audio_url?: string;
  subtitle_url?: string;
  duration?: number;
  file_size?: number;
}

interface ASRResult {
  task_id: string;
  model_type: string;
  status: string;
  result?: {
    text: string;
    utterances: Array<{
      text: string;
      start_time: number;
      end_time: number;
    }>;
  };
}

interface VoiceCloneResult {
  speaker_id: string;
  status: string;
  create_time?: number;
  version?: string;
  demo_audio?: string;
  ready_for_use?: boolean;
}

export default function VolcengineTestPage() {
  // TTS状态
  const [ttsText, setTtsText] = useState('你好，这是火山引擎语音合成测试。');
  const [ttsVoice, setTtsVoice] = useState('BV700_streaming');
  const [ttsLoading, setTtsLoading] = useState(false);
  const [ttsResult, setTtsResult] = useState<TTSResult | null>(null);

  // 异步TTS状态
  const [asyncTtsText, setAsyncTtsText] = useState('这是一段较长的文本，用于测试火山引擎的异步语音合成功能。异步合成适合处理长文本内容，可以生成高质量的音频文件。');
  const [asyncTtsVoice, setAsyncTtsVoice] = useState('BV701_streaming');
  const [asyncTtsLoading, setAsyncTtsLoading] = useState(false);
  const [asyncTtsResult, setAsyncTtsResult] = useState<AsyncTTSResult | null>(null);
  const [asyncTaskId, setAsyncTaskId] = useState('');
  const [asyncPolling, setAsyncPolling] = useState(false);

  // ASR状态
  const [asrUrl, setAsrUrl] = useState('');
  const [asrModel, setAsrModel] = useState('fast');
  const [asrLanguage, setAsrLanguage] = useState('zh');
  const [asrLoading, setAsrLoading] = useState(false);
  const [asrResult, setAsrResult] = useState<ASRResult | null>(null);
  const [asrTaskId, setAsrTaskId] = useState('');
  const [asrPolling, setAsrPolling] = useState(false);

  // 声音复刻状态
  const [cloneSpeakerId, setCloneSpeakerId] = useState('');
  const [cloneFile, setCloneFile] = useState<File | null>(null);
  const [cloneText, setCloneText] = useState('');
  const [cloneModelType, setCloneModelType] = useState('1');
  const [cloneLoading, setCloneLoading] = useState(false);
  const [cloneResult, setCloneResult] = useState<VoiceCloneResult | null>(null);

  // 错误状态
  const [error, setError] = useState('');

  // 语音合成
  const handleTTS = async () => {
    if (!ttsText.trim()) {
      setError('请输入要合成的文本');
      return;
    }

    setTtsLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/volcengine/tts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: ttsText,
          voice_type: ttsVoice,
          options: {
            encoding: 'mp3',
            rate: 24000
          }
        })
      });

      const data = await response.json();
      
      if (data.code === 0) {
        setTtsResult(data.data);
      } else {
        setError(data.message || '语音合成失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setTtsLoading(false);
    }
  };

  // 异步语音合成提交
  const handleAsyncTTSSubmit = async () => {
    if (!asyncTtsText.trim()) {
      setError('请输入要合成的文本');
      return;
    }

    setAsyncTtsLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/volcengine/tts-async/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: asyncTtsText,
          voice_type: asyncTtsVoice,
          options: {
            format: 'mp3',
            sample_rate: 24000
          }
        })
      });

      const data = await response.json();
      
      if (data.code === 0) {
        setAsyncTaskId(data.data.task_id);
        setAsyncTtsResult({ ...data.data, status: 'submitted' });
        // 自动开始轮询
        startAsyncTTSPolling(data.data.task_id);
      } else {
        setError(data.message || '异步任务提交失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setAsyncTtsLoading(false);
    }
  };

  // 查询异步TTS结果
  const handleAsyncTTSQuery = async () => {
    if (!asyncTaskId) {
      setError('请先提交异步任务');
      return;
    }

    setAsyncTtsLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/volcengine/tts-async/query?task_id=${asyncTaskId}`);
      const data = await response.json();

      if (data.code === 0) {
        setAsyncTtsResult(data.data);
      } else {
        setError(data.message || '查询失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setAsyncTtsLoading(false);
    }
  };

  // 开始自动轮询异步TTS结果
  const startAsyncTTSPolling = async (taskId: string) => {
    setAsyncPolling(true);
    const maxAttempts = 60; // 最多轮询5分钟
    let attempts = 0;

    const poll = async () => {
      if (attempts >= maxAttempts) {
        setAsyncPolling(false);
        setError('任务超时，请手动查询结果');
        return;
      }

      try {
        const response = await fetch(`/api/volcengine/tts-async/query?task_id=${taskId}`);
        const data = await response.json();

        if (data.code === 0) {
          setAsyncTtsResult(data.data);

          if (data.data.status === 'success' || data.data.status === 'failed') {
            setAsyncPolling(false);
            return;
          }
        }

        attempts++;
        setTimeout(poll, 5000); // 5秒后重试
      } catch (err) {
        console.error('轮询错误:', err);
        attempts++;
        setTimeout(poll, 5000);
      }
    };

    poll();
  };

  // 语音识别提交
  const handleASRSubmit = async () => {
    if (!asrUrl.trim()) {
      setError('请输入音频文件URL');
      return;
    }

    setAsrLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/volcengine/asr/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          audio_url: asrUrl,
          model_type: asrModel,
          language: asrLanguage
        })
      });

      const data = await response.json();
      
      if (data.code === 0) {
        setAsrTaskId(data.data.task_id);
        setAsrResult({ ...data.data, status: 'submitted' });
        // 自动开始轮询
        startASRPolling(data.data.task_id, asrModel);
      } else {
        setError(data.message || 'ASR任务提交失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setAsrLoading(false);
    }
  };

  // 查询ASR结果
  const handleASRQuery = async () => {
    if (!asrTaskId) {
      setError('请先提交ASR任务');
      return;
    }

    setAsrLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/volcengine/asr/query?task_id=${asrTaskId}&model_type=${asrModel}`);
      const data = await response.json();

      if (data.code === 0) {
        setAsrResult(data.data);
      } else {
        setError(data.message || '查询失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setAsrLoading(false);
    }
  };

  // 开始自动轮询ASR结果
  const startASRPolling = async (taskId: string, modelType: string) => {
    setAsrPolling(true);
    const maxAttempts = 120; // 最多轮询10分钟
    let attempts = 0;

    const poll = async () => {
      if (attempts >= maxAttempts) {
        setAsrPolling(false);
        setError('任务超时，请手动查询结果');
        return;
      }

      try {
        const response = await fetch(`/api/volcengine/asr/query?task_id=${taskId}&model_type=${modelType}`);
        const data = await response.json();

        if (data.code === 0) {
          setAsrResult(data.data);

          if (data.data.status === 'success' || data.data.status === 'failed') {
            setAsrPolling(false);
            return;
          }
        }

        attempts++;
        setTimeout(poll, 5000); // 5秒后重试
      } catch (err) {
        console.error('轮询错误:', err);
        attempts++;
        setTimeout(poll, 5000);
      }
    };

    poll();
  };

  // 声音复刻上传
  const handleVoiceCloneUpload = async () => {
    if (!cloneSpeakerId.trim() || !cloneFile) {
      setError('请输入Speaker ID并选择音频文件');
      return;
    }

    setCloneLoading(true);
    setError('');
    
    try {
      const formData = new FormData();
      formData.append('audio', cloneFile);
      formData.append('speaker_id', cloneSpeakerId);
      formData.append('reference_text', cloneText);
      formData.append('language', '0'); // 中文
      formData.append('model_type', cloneModelType);

      const response = await fetch('/api/volcengine/voice-clone/upload', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.code === 0) {
        setCloneResult(data.data);
      } else {
        setError(data.message || '声音复刻上传失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setCloneLoading(false);
    }
  };

  // 查询声音复刻状态
  const handleVoiceCloneStatus = async () => {
    if (!cloneSpeakerId.trim()) {
      setError('请输入Speaker ID');
      return;
    }

    setCloneLoading(true);
    setError('');
    
    try {
      const response = await fetch(`/api/volcengine/voice-clone/status?speaker_id=${cloneSpeakerId}`);
      const data = await response.json();
      
      if (data.code === 0) {
        setCloneResult(data.data);
      } else {
        setError(data.message || '状态查询失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setCloneLoading(false);
    }
  };

  // 播放音频
  const playAudio = (audioData: string, format: string = 'mp3') => {
    try {
      const audioBlob = new Blob([Uint8Array.from(atob(audioData), c => c.charCodeAt(0))], {
        type: `audio/${format}`
      });
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);
      audio.play();
    } catch (err) {
      setError('音频播放失败');
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">火山引擎语音服务测试</h1>
        <p className="text-muted-foreground">测试火山引擎豆包语音服务的各项功能</p>
      </div>

      {error && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="tts" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="tts" className="flex items-center gap-2">
            <Volume2 className="w-4 h-4" />
            短文本合成
          </TabsTrigger>
          <TabsTrigger value="async-tts" className="flex items-center gap-2">
            <Volume2 className="w-4 h-4" />
            长文本合成
          </TabsTrigger>
          <TabsTrigger value="asr" className="flex items-center gap-2">
            <Mic className="w-4 h-4" />
            语音识别
          </TabsTrigger>
          <TabsTrigger value="voice-clone" className="flex items-center gap-2">
            <Upload className="w-4 h-4" />
            声音复刻
          </TabsTrigger>
        </TabsList>

        {/* 短文本语音合成 */}
        <TabsContent value="tts">
          <Card>
            <CardHeader>
              <CardTitle>短文本语音合成</CardTitle>
              <CardDescription>
                实时语音合成，适合短文本（最大1024字节）
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="tts-text">合成文本</Label>
                <Textarea
                  id="tts-text"
                  value={ttsText}
                  onChange={(e) => setTtsText(e.target.value)}
                  placeholder="输入要合成的文本..."
                  className="mt-1"
                  maxLength={1024}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  {ttsText.length}/1024 字符
                </p>
              </div>

              <div>
                <Label htmlFor="tts-voice">音色选择</Label>
                <Select value={ttsVoice} onValueChange={setTtsVoice}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BV700_streaming">灿灿 (支持情感)</SelectItem>
                    <SelectItem value="BV701_streaming">擎苍 (有声阅读)</SelectItem>
                    <SelectItem value="BV001_streaming">通用女声</SelectItem>
                    <SelectItem value="BV002_streaming">通用男声</SelectItem>
                    <SelectItem value="BV421_streaming">天才少女 (多语种)</SelectItem>
                    <SelectItem value="BV503_streaming">Ariana (英语)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button 
                onClick={handleTTS} 
                disabled={ttsLoading || !ttsText.trim()}
                className="w-full"
              >
                {ttsLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    合成中...
                  </>
                ) : (
                  <>
                    <Volume2 className="w-4 h-4 mr-2" />
                    开始合成
                  </>
                )}
              </Button>

              {ttsResult && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-green-800">合成成功</h4>
                    <Badge variant="secondary">
                      时长: {ttsResult.duration}ms
                    </Badge>
                  </div>
                  <p className="text-sm text-green-700 mb-3">
                    请求ID: {ttsResult.reqid}
                  </p>
                  <Button
                    onClick={() => playAudio(ttsResult.audio_data, ttsResult.format)}
                    size="sm"
                    variant="outline"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    播放音频
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 长文本异步合成 */}
        <TabsContent value="async-tts">
          <Card>
            <CardHeader>
              <CardTitle>长文本异步合成</CardTitle>
              <CardDescription>
                异步处理长文本（最大10万字符），支持回调和轮询
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="async-tts-text">合成文本</Label>
                <Textarea
                  id="async-tts-text"
                  value={asyncTtsText}
                  onChange={(e) => setAsyncTtsText(e.target.value)}
                  placeholder="输入要合成的长文本..."
                  className="mt-1 min-h-[120px]"
                  maxLength={100000}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  {asyncTtsText.length}/100,000 字符
                </p>
              </div>

              <div>
                <Label htmlFor="async-tts-voice">音色选择</Label>
                <Select value={asyncTtsVoice} onValueChange={setAsyncTtsVoice}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BV701_streaming">擎苍 (推荐长文本)</SelectItem>
                    <SelectItem value="BV700_streaming">灿灿</SelectItem>
                    <SelectItem value="BV001_streaming">通用女声</SelectItem>
                    <SelectItem value="BV002_streaming">通用男声</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleAsyncTTSSubmit}
                  disabled={asyncTtsLoading || asyncPolling || !asyncTtsText.trim()}
                  className="flex-1"
                >
                  {asyncTtsLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      提交中...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      提交任务
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleAsyncTTSQuery}
                  disabled={asyncTtsLoading || asyncPolling || !asyncTaskId}
                  variant="outline"
                  className="flex-1"
                >
                  {asyncPolling ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      轮询中...
                    </>
                  ) : (
                    '手动查询'
                  )}
                </Button>
              </div>

              {asyncTaskId && (
                <div className="p-3 bg-blue-50 rounded border border-blue-200">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-blue-800">
                      任务ID: <code className="bg-blue-100 px-1 rounded">{asyncTaskId}</code>
                    </p>
                    {asyncPolling && (
                      <Badge variant="secondary" className="animate-pulse">
                        自动轮询中...
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {asyncTtsResult && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-green-800">任务状态</h4>
                    <Badge variant={asyncTtsResult.status === 'success' ? 'default' : 'secondary'}>
                      {asyncTtsResult.status}
                    </Badge>
                  </div>
                  
                  {asyncTtsResult.audio_url && (
                    <div className="space-y-2">
                      <p className="text-sm text-green-700">
                        音频文件: <a href={asyncTtsResult.audio_url} target="_blank" rel="noopener noreferrer" className="underline">下载链接</a>
                      </p>
                      {asyncTtsResult.subtitle_url && (
                        <p className="text-sm text-green-700">
                          字幕文件: <a href={asyncTtsResult.subtitle_url} target="_blank" rel="noopener noreferrer" className="underline">下载链接</a>
                        </p>
                      )}
                      <p className="text-sm text-green-700">
                        时长: {asyncTtsResult.duration}ms | 文件大小: {asyncTtsResult.file_size} bytes
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 语音识别 */}
        <TabsContent value="asr">
          <Card>
            <CardHeader>
              <CardTitle>语音识别</CardTitle>
              <CardDescription>
                支持多种语言和方言的语音转文字服务
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="asr-url">音频文件URL</Label>
                <Input
                  id="asr-url"
                  value={asrUrl}
                  onChange={(e) => setAsrUrl(e.target.value)}
                  placeholder="输入公网可访问的音频文件URL..."
                  className="mt-1"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="asr-model">识别模型</Label>
                  <Select value={asrModel} onValueChange={setAsrModel}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bigmodel">大模型识别 (高精度)</SelectItem>
                      <SelectItem value="standard">标准版识别</SelectItem>
                      <SelectItem value="fast">极速版识别</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="asr-language">语言</Label>
                  <Select value={asrLanguage} onValueChange={setAsrLanguage}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="zh">中文普通话</SelectItem>
                      <SelectItem value="yue">粤语</SelectItem>
                      <SelectItem value="en">英语</SelectItem>
                      <SelectItem value="ja">日语</SelectItem>
                      <SelectItem value="ko">韩语</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleASRSubmit}
                  disabled={asrLoading || asrPolling || !asrUrl.trim()}
                  className="flex-1"
                >
                  {asrLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      提交中...
                    </>
                  ) : (
                    <>
                      <Mic className="w-4 h-4 mr-2" />
                      提交识别
                    </>
                  )}
                </Button>

                <Button
                  onClick={handleASRQuery}
                  disabled={asrLoading || asrPolling || !asrTaskId}
                  variant="outline"
                  className="flex-1"
                >
                  {asrPolling ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      轮询中...
                    </>
                  ) : (
                    '手动查询'
                  )}
                </Button>
              </div>

              {asrTaskId && (
                <div className="p-3 bg-blue-50 rounded border border-blue-200">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-blue-800">
                      任务ID: <code className="bg-blue-100 px-1 rounded">{asrTaskId}</code>
                    </p>
                    {asrPolling && (
                      <Badge variant="secondary" className="animate-pulse">
                        自动轮询中...
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {asrResult && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-green-800">识别结果</h4>
                    <Badge variant={asrResult.status === 'success' ? 'default' : 'secondary'}>
                      {asrResult.status}
                    </Badge>
                  </div>
                  
                  {asrResult.result && (
                    <div className="space-y-3">
                      <div>
                        <h5 className="font-medium text-green-800 mb-1">完整文本:</h5>
                        <p className="text-green-700 bg-white p-3 rounded border">
                          {asrResult.result.text}
                        </p>
                      </div>
                      
                      {asrResult.result.utterances && asrResult.result.utterances.length > 0 && (
                        <div>
                          <h5 className="font-medium text-green-800 mb-2">分段结果:</h5>
                          <div className="space-y-2">
                            {asrResult.result.utterances.map((utterance, index) => (
                              <div key={index} className="bg-white p-2 rounded border text-sm">
                                <div className="flex justify-between items-start mb-1">
                                  <span className="text-green-700">{utterance.text}</span>
                                  <span className="text-green-600 text-xs">
                                    {utterance.start_time}s - {utterance.end_time}s
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 声音复刻 */}
        <TabsContent value="voice-clone">
          <Card>
            <CardHeader>
              <CardTitle>声音复刻</CardTitle>
              <CardDescription>
                上传音频文件训练个性化音色
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="clone-speaker-id">Speaker ID</Label>
                <Input
                  id="clone-speaker-id"
                  value={cloneSpeakerId}
                  onChange={(e) => setCloneSpeakerId(e.target.value)}
                  placeholder="输入唯一的音色标识符..."
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="clone-file">音频文件</Label>
                <Input
                  id="clone-file"
                  type="file"
                  accept=".wav,.mp3,.ogg,.m4a,.aac,.pcm"
                  onChange={(e) => setCloneFile(e.target.files?.[0] || null)}
                  className="mt-1"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  支持格式: wav, mp3, ogg, m4a, aac, pcm (最大10MB)
                </p>
              </div>

              <div>
                <Label htmlFor="clone-text">参考文本 (可选)</Label>
                <Textarea
                  id="clone-text"
                  value={cloneText}
                  onChange={(e) => setCloneText(e.target.value)}
                  placeholder="输入音频对应的文本内容，用于提高复刻质量..."
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="clone-model-type">复刻版本</Label>
                <Select value={cloneModelType} onValueChange={setCloneModelType}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">复刻1.0 (基础版)</SelectItem>
                    <SelectItem value="1">复刻2.0 (增强版)</SelectItem>
                    <SelectItem value="2">DiT标准版 (音色复刻)</SelectItem>
                    <SelectItem value="3">DiT还原版 (音色+风格)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={handleVoiceCloneUpload} 
                  disabled={cloneLoading || !cloneSpeakerId.trim() || !cloneFile}
                  className="flex-1"
                >
                  {cloneLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      上传中...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      上传训练
                    </>
                  )}
                </Button>

                <Button 
                  onClick={handleVoiceCloneStatus} 
                  disabled={cloneLoading || !cloneSpeakerId.trim()}
                  variant="outline"
                  className="flex-1"
                >
                  查询状态
                </Button>
              </div>

              {cloneResult && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-green-800">复刻状态</h4>
                    <Badge variant={cloneResult.ready_for_use ? 'default' : 'secondary'}>
                      {cloneResult.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2 text-sm text-green-700">
                    <p>Speaker ID: {cloneResult.speaker_id}</p>
                    {cloneResult.version && <p>版本: {cloneResult.version}</p>}
                    {cloneResult.create_time && (
                      <p>创建时间: {new Date(cloneResult.create_time).toLocaleString()}</p>
                    )}
                    {cloneResult.demo_audio && (
                      <p>
                        试听音频: <a href={cloneResult.demo_audio} target="_blank" rel="noopener noreferrer" className="underline">播放</a>
                      </p>
                    )}
                    {cloneResult.ready_for_use && (
                      <p className="text-green-800 font-medium">✅ 可用于语音合成</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
