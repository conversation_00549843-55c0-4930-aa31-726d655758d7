import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";

export const runtime = "edge";

export default function ComponentsDemoPage() {
  const demoCategories = [
    {
      title: "Layout Components",
      description: "Header, Footer, Navigation and Breadcrumb components",
      href: "/components-demo/layout",
      icon: "RiLayoutLine",
      components: ["Header", "Footer", "Crumb"],
      color: "bg-blue-50 border-blue-200 text-blue-800"
    },
    {
      title: "Content Display",
      description: "Hero sections, features, showcases and content blocks",
      href: "/components-demo/content",
      icon: "RiArticleLine",
      components: ["Hero", "Feature", "Feature1", "Feature2", "Feature3", "Showcase", "Showcase1", "Blog", "Testimonial", "Stats", "Branding"],
      color: "bg-green-50 border-green-200 text-green-800"
    },
    {
      title: "Interactive Components",
      description: "CTAs, FAQs, Pricing tables, Forms and Tables",
      href: "/components-demo/interactive",
      icon: "RiCursorLine",
      components: ["CTA", "FAQ", "Pricing", "Form", "Table"],
      color: "bg-purple-50 border-purple-200 text-purple-800"
    },
    {
      title: "Data Visualization",
      description: "Data cards and charts for displaying metrics",
      href: "/components-demo/data",
      icon: "RiBarChartLine",
      components: ["Data-cards", "Data-charts"],
      color: "bg-orange-50 border-orange-200 text-orange-800"
    },
    {
      title: "Editors",
      description: "Rich text and Markdown editors",
      href: "/components-demo/editors",
      icon: "RiEditLine",
      components: ["Editor", "MDEditor"],
      color: "bg-red-50 border-red-200 text-red-800"
    },
    {
      title: "Utility Components",
      description: "Toolbars, empty states and other utilities",
      href: "/components-demo/tools",
      icon: "RiToolsLine",
      components: ["Toolbar", "Empty"],
      color: "bg-gray-50 border-gray-200 text-gray-800"
    },
    {
      title: "Blog Detail",
      description: "Detailed blog post view with content rendering",
      href: "/components-demo/blog-detail",
      icon: "RiFileTextLine",
      components: ["Blog-detail"],
      color: "bg-indigo-50 border-indigo-200 text-indigo-800"
    }
  ];

  return (
    <div className="container mx-auto py-16 px-4">
      <div className="text-center mb-12">
        <Badge variant="outline" className="mb-4">
          Components Demo
        </Badge>
        <h1 className="text-4xl font-bold mb-4">
          ShipAny Block Components Showcase
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Explore all the available block components in the ShipAny template. 
          Each category contains interactive examples with different configurations and use cases.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {demoCategories.map((category, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
            <CardHeader>
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 rounded-lg bg-primary/10">
                  <Icon name={category.icon} className="w-6 h-6 text-primary" />
                </div>
                <CardTitle className="text-xl">{category.title}</CardTitle>
              </div>
              <CardDescription className="text-base">
                {category.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <p className="text-sm font-medium text-muted-foreground mb-2">
                  Components ({category.components.length}):
                </p>
                <div className="flex flex-wrap gap-1">
                  {category.components.map((component, idx) => (
                    <Badge 
                      key={idx} 
                      variant="secondary" 
                      className={`text-xs ${category.color}`}
                    >
                      {component}
                    </Badge>
                  ))}
                </div>
              </div>
              <Link href={category.href as any}>
                <Button className="w-full" variant="outline">
                  View Examples
                  <Icon name="RiArrowRightLine" className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-16 text-center">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <Icon name="RiInformationLine" className="w-5 h-5" />
              About These Components
            </CardTitle>
          </CardHeader>
          <CardContent className="text-left space-y-3">
            <p className="text-muted-foreground">
              These block components are the building blocks of the ShipAny template. 
              Each component is designed to be:
            </p>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li><strong>Reusable:</strong> Can be used across different pages and contexts</li>
              <li><strong>Customizable:</strong> Supports various props and configurations</li>
              <li><strong>Responsive:</strong> Works seamlessly across all device sizes</li>
              <li><strong>Accessible:</strong> Built with accessibility best practices</li>
              <li><strong>Type-safe:</strong> Full TypeScript support with proper interfaces</li>
            </ul>
            <div className="pt-4 border-t">
              <Link href={"/" as any} className="inline-flex items-center text-primary hover:underline">
                <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
                Back to Home
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
