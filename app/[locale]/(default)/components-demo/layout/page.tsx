import Header from "@/components/blocks/header";
import Footer from "@/components/blocks/footer";
import Crumb from "@/components/blocks/crumb";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Header as HeaderType } from "@/types/blocks/header";
import { Footer as FooterType } from "@/types/blocks/footer";
import { NavItem } from "@/types/blocks/base";

export const runtime = "edge";

export default function LayoutComponentsDemo() {
  // Demo data for Header component
  const headerDemo: HeaderType = {
    brand: {
      title: "DemoApp",
      logo: {
        src: "/logo.png",
        alt: "DemoApp"
      },
      url: "/"
    },
    nav: {
      items: [
        {
          title: "Features",
          url: "/#features",
          icon: "RiSparkling2Line"
        },
        {
          title: "Pricing",
          url: "/pricing",
          icon: "RiMoneyDollarCircleLine"
        },
        {
          title: "About",
          url: "/about",
          icon: "RiInformationLine"
        },
        {
          title: "Resources",
          url: "#",
          icon: "RiBookLine",
          children: [
            {
              title: "Documentation",
              url: "/docs",
              icon: "RiFileTextLine"
            },
            {
              title: "API Reference",
              url: "/api",
              icon: "RiCodeLine"
            },
            {
              title: "Tutorials",
              url: "/tutorials",
              icon: "RiPlayLine"
            }
          ]
        }
      ]
    },
    buttons: [
      {
        title: "Get Started",
        url: "/signup",
        variant: "default",
        icon: "RiRocketLine"
      },
      {
        title: "Login",
        url: "/login",
        variant: "outline"
      }
    ],
    show_sign: true,
    show_theme: true,
    show_locale: true
  };

  // Demo data for Footer component
  const footerDemo: FooterType = {
    brand: {
      title: "DemoApp",
      description: "A powerful platform for building amazing applications with modern tools and best practices.",
      logo: {
        src: "/logo.png",
        alt: "DemoApp"
      },
      url: "/"
    },
    nav: {
      items: [
        {
          title: "Product",
          children: [
            { title: "Features", url: "/features" },
            { title: "Pricing", url: "/pricing" },
            { title: "API", url: "/api" },
            { title: "Integrations", url: "/integrations" }
          ]
        },
        {
          title: "Company",
          children: [
            { title: "About", url: "/about" },
            { title: "Blog", url: "/blog" },
            { title: "Careers", url: "/careers" },
            { title: "Contact", url: "/contact" }
          ]
        },
        {
          title: "Resources",
          children: [
            { title: "Documentation", url: "/docs" },
            { title: "Help Center", url: "/help" },
            { title: "Community", url: "/community" },
            { title: "Status", url: "/status" }
          ]
        }
      ]
    },
    social: {
      items: [
        {
          title: "Twitter",
          icon: "RiTwitterXLine",
          url: "https://twitter.com/demoapp"
        },
        {
          title: "GitHub",
          icon: "RiGithubLine",
          url: "https://github.com/demoapp"
        },
        {
          title: "Discord",
          icon: "RiDiscordLine",
          url: "https://discord.gg/demoapp"
        }
      ]
    },
    copyright: "© 2025 DemoApp. All rights reserved.",
    agreement: {
      items: [
        { title: "Privacy Policy", url: "/privacy" },
        { title: "Terms of Service", url: "/terms" },
        { title: "Cookie Policy", url: "/cookies" }
      ]
    }
  };

  // Demo data for Breadcrumb component
  const crumbItems: NavItem[] = [
    { title: "Home", url: "/" },
    { title: "Components", url: "/components-demo" },
    { title: "Layout", url: "/components-demo/layout" }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <Badge variant="outline" className="mb-4">
            Layout Components
          </Badge>
          <h1 className="text-3xl font-bold mb-4">Layout Components Demo</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Essential layout components for structuring your application: headers, footers, and navigation elements.
          </p>
        </div>

        <div className="mb-8">
          <Link href={"/components-demo" as any} className="inline-flex items-center text-primary hover:underline">
            <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
            Back to Components Demo
          </Link>
        </div>
      </div>

      {/* Header Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiLayoutLine" className="w-5 h-5" />
                Header Component
              </CardTitle>
              <CardDescription>
                Navigation header with brand, menu items, buttons, and utility toggles. 
                Supports responsive design with mobile menu.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-card">
          <Header header={headerDemo} />
        </div>
      </section>

      {/* Breadcrumb Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4">
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiNavigationLine" className="w-5 h-5" />
                Breadcrumb Component
              </CardTitle>
              <CardDescription>
                Navigation breadcrumbs to show the current page location within the site hierarchy.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg bg-muted/30">
                <Crumb items={crumbItems} />
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiLayoutBottomLine" className="w-5 h-5" />
                Footer Component
              </CardTitle>
              <CardDescription>
                Site footer with brand information, navigation links, social media, and legal links.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-card">
          <Footer footer={footerDemo} />
        </div>
      </section>

      {/* Component Usage Information */}
      <div className="container mx-auto px-4 pb-16">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon name="RiCodeLine" className="w-5 h-5" />
              Usage Examples
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">Header Component</h4>
              <code className="text-sm bg-muted p-2 rounded block">
                {`<Header header={headerData} />`}
              </code>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Footer Component</h4>
              <code className="text-sm bg-muted p-2 rounded block">
                {`<Footer footer={footerData} />`}
              </code>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Breadcrumb Component</h4>
              <code className="text-sm bg-muted p-2 rounded block">
                {`<Crumb items={breadcrumbItems} />`}
              </code>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
