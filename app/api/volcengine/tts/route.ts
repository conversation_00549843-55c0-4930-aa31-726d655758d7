import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider, VolcengineTTSRequest } from "@/services/volcengine-provider";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { text, voice_type, options } = body;

    // 验证必需参数
    if (!text) {
      return respErr("Missing required parameter: text");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 验证文本长度
    if (text.length > 1024) {
      return respErr("Text length exceeds 1024 bytes limit for short TTS");
    }

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    // 构建TTS请求
    const ttsRequest: VolcengineTTSRequest = {
      text: text,
      voice_type: voice_type || "BV700_streaming",
      encoding: options?.encoding || "mp3",
      rate: options?.rate || 24000,
      speed_ratio: options?.speed_ratio || 1.0,
      volume_ratio: options?.volume_ratio || 1.0,
      pitch_ratio: options?.pitch_ratio || 1.0,
      emotion: options?.emotion,
      language: options?.language || "cn"
    };

    // 调用语音合成
    const result = await volcengineProvider.synthesizeText(ttsRequest);

    if (result.code === 3000) {
      return respData({
        type: 'audio',
        reqid: result.reqid,
        audio_data: result.data,
        duration: result.addition?.duration,
        format: ttsRequest.encoding
      });
    } else {
      return respErr(`TTS failed: ${result.message}`);
    }

  } catch (error) {
    console.error('Volcengine TTS error:', error);
    return respErr(`TTS service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
