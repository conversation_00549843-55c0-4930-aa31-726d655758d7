import { respData, respErr } from "@/lib/resp";
import { NextRequest } from "next/server";

export const runtime = "edge";

/**
 * 处理火山引擎服务的回调通知
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // 记录回调数据
    console.log('Volcengine callback received:', {
      timestamp: new Date().toISOString(),
      body: body
    });

    // 根据回调类型处理不同的业务逻辑
    if (body.task_id) {
      // 异步TTS或ASR任务完成回调
      await handleTaskCallback(body);
    } else if (body.speaker_id) {
      // 声音复刻训练完成回调
      await handleVoiceCloneCallback(body);
    }

    // 返回成功响应
    return respData({ received: true });

  } catch (error) {
    console.error('Volcengine callback processing error:', error);
    return respErr(`Callback processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * 处理任务完成回调
 */
async function handleTaskCallback(data: any) {
  try {
    const { task_id, status, code, message } = data;
    
    console.log(`Task ${task_id} completed with status: ${status}`);
    
    if (status === 'success') {
      // 任务成功完成
      if (data.audio_url) {
        // TTS任务完成
        console.log(`TTS task ${task_id} completed. Audio URL: ${data.audio_url}`);
        
        // 这里可以添加业务逻辑，比如：
        // 1. 保存音频URL到数据库
        // 2. 发送通知给用户
        // 3. 触发后续处理流程
        
      } else if (data.result) {
        // ASR任务完成
        console.log(`ASR task ${task_id} completed. Text: ${data.result.text}`);
        
        // 这里可以添加业务逻辑，比如：
        // 1. 保存识别结果到数据库
        // 2. 触发文本后处理
        // 3. 发送结果通知
      }
    } else if (status === 'failed') {
      // 任务失败
      console.error(`Task ${task_id} failed: ${message}`);
      
      // 这里可以添加错误处理逻辑，比如：
      // 1. 记录错误日志
      // 2. 发送失败通知
      // 3. 触发重试机制
    }
    
  } catch (error) {
    console.error('Task callback processing error:', error);
  }
}

/**
 * 处理声音复刻回调
 */
async function handleVoiceCloneCallback(data: any) {
  try {
    const { speaker_id, status, demo_audio } = data;
    
    console.log(`Voice clone ${speaker_id} status: ${status}`);
    
    if (status === 'success' || status === 2) {
      // 训练成功
      console.log(`Voice clone ${speaker_id} training completed successfully`);
      
      if (demo_audio) {
        console.log(`Demo audio available: ${demo_audio}`);
      }
      
      // 这里可以添加业务逻辑，比如：
      // 1. 更新音色状态到数据库
      // 2. 发送训练完成通知
      // 3. 启用音色供用户使用
      
    } else if (status === 'failed' || status === 3) {
      // 训练失败
      console.error(`Voice clone ${speaker_id} training failed`);
      
      // 这里可以添加错误处理逻辑，比如：
      // 1. 记录失败原因
      // 2. 发送失败通知
      // 3. 提供重新训练选项
    }
    
  } catch (error) {
    console.error('Voice clone callback processing error:', error);
  }
}

/**
 * 验证回调签名（可选的安全措施）
 */
function verifyCallbackSignature(signature: string, body: string, secret: string): boolean {
  try {
    // 这里可以实现HMAC签名验证
    // const expectedSignature = crypto
    //   .createHmac('sha256', secret)
    //   .update(body)
    //   .digest('hex');
    // return signature === expectedSignature;
    
    // 暂时返回true，实际使用时应该实现真正的签名验证
    return true;
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
}
