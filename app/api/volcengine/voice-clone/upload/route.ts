import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider, VolcengineVoiceCloneRequest } from "@/services/volcengine-provider";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    const formData = await req.formData();
    const audioFile = formData.get('audio') as File;
    const speakerId = formData.get('speaker_id') as string;
    const referenceText = formData.get('reference_text') as string;
    const language = parseInt(formData.get('language') as string) || 0;
    const modelType = parseInt(formData.get('model_type') as string) || 1;

    // 验证必需参数
    if (!audioFile || !speakerId) {
      return respErr("Missing required parameters: audio file and speaker_id");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 验证文件大小 (最大10MB)
    if (audioFile.size > 10 * 1024 * 1024) {
      return respErr("Audio file size exceeds 10MB limit");
    }

    // 验证文件格式
    const allowedFormats = ['wav', 'mp3', 'ogg', 'm4a', 'aac', 'pcm'];
    const fileExtension = audioFile.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !allowedFormats.includes(fileExtension)) {
      return respErr("Unsupported audio format. Supported formats: wav, mp3, ogg, m4a, aac, pcm");
    }

    // 将文件转换为base64
    const arrayBuffer = await audioFile.arrayBuffer();
    const audioBytes = Buffer.from(arrayBuffer).toString('base64');

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    // 构建声音复刻请求
    const voiceCloneRequest: VolcengineVoiceCloneRequest = {
      speaker_id: speakerId,
      audio_bytes: audioBytes,
      audio_format: fileExtension,
      text: referenceText,
      language: language,
      model_type: modelType
    };

    // 上传音频进行训练
    const result = await volcengineProvider.uploadVoiceClone(voiceCloneRequest);

    if (result.BaseResp.StatusCode === 0) {
      return respData({
        speaker_id: result.speaker_id,
        status: 'uploaded',
        message: 'Voice clone training started'
      });
    } else {
      return respErr(`Voice clone upload failed: ${result.BaseResp.StatusMessage}`);
    }

  } catch (error) {
    console.error('Volcengine Voice Clone upload error:', error);
    return respErr(`Voice clone service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
