/**
 * GRSAI 模型配置
 * 按照功能对模型进行分类，支持灵活的提供商切换
 */

export enum ModelType {
  TEXT = 'text',
  IMAGE = 'image', 
  VIDEO = 'video',
  MULTIMODAL = 'multimodal'
}

export enum Provider {
  GRSAI = 'grsai',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

export enum UnitType {
  TOKENS = 'tokens',
  IMAGES = 'images', 
  VIDEOS = 'videos'
}

export interface ModelConfig {
  id: string;
  name: string;
  type: ModelType;
  provider: Provider;
  apiEndpoint: string;
  creditsPerUnit: number;
  unitType: UnitType;
  isActive: boolean;
  description?: string;
  maxInputSize?: number;
  supportedFeatures?: string[];
}

/**
 * GRSAI 模型配置
 */
export const GRSAI_MODELS: ModelConfig[] = [
  // 文本生成模型
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 10,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '高级对话模型，适合复杂任务',
    supportedFeatures: ['chat', 'stream']
  },
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 5,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '快速对话模型，响应迅速',
    supportedFeatures: ['chat', 'stream']
  },
  {
    id: 'gemini-2.5-flash-lite',
    name: 'Gemini 2.5 Flash Lite',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 3,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: '轻量级对话模型，成本低廉',
    supportedFeatures: ['chat', 'stream']
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    type: ModelType.TEXT,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 6,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o 轻量版本',
    supportedFeatures: ['chat', 'stream']
  },
  {
    id: 'o4-mini-all',
    name: 'GPT-4o Mini All',
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 8,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o Mini 全功能版本',
    supportedFeatures: ['chat', 'stream', 'vision', 'image_upload']
  },
  {
    id: 'gpt-4o-all',
    name: 'GPT-4o All',
    type: ModelType.MULTIMODAL,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/chat/completions',
    creditsPerUnit: 12,
    unitType: UnitType.TOKENS,
    isActive: true,
    description: 'GPT-4o 全功能版本',
    supportedFeatures: ['chat', 'stream', 'vision', 'image_upload']
  },

  // 图像生成模型
  {
    id: 'sora-image',
    name: 'Sora Image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/completions',
    creditsPerUnit: 50,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'Sora 图像生成模型',
    supportedFeatures: ['text2image', 'variants', 'reference', 'image_upload']
  },
  {
    id: 'gpt-4o-image',
    name: 'GPT-4o Image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/completions',
    creditsPerUnit: 60,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'GPT-4o 图像生成模型',
    supportedFeatures: ['text2image', 'variants', 'reference', 'image_upload']
  },
  {
    id: 'flux-pro-1.1',
    name: 'Flux Pro 1.1',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 40,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'Flux 专业版图像生成',
    supportedFeatures: ['text2image', 'aspectRatio']
  },
  {
    id: 'flux-pro-1.1-ultra',
    name: 'Flux Pro 1.1 Ultra',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 80,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'Flux 超级版图像生成',
    supportedFeatures: ['text2image', 'aspectRatio']
  },
  {
    id: 'flux-kontext-pro',
    name: 'Flux Kontext Pro',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 45,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'Flux 上下文专业版',
    supportedFeatures: ['text2image', 'aspectRatio', 'reference', 'image_upload']
  },
  {
    id: 'flux-kontext-max',
    name: 'Flux Kontext Max',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 70,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: 'Flux 上下文最大版',
    supportedFeatures: ['text2image', 'aspectRatio', 'reference', 'image_upload']
  },

  // 视频生成模型
  {
    id: 'veo3-fast',
    name: 'Veo3 Fast',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/video/veo',
    creditsPerUnit: 200,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: 'Veo3 快速视频生成',
    supportedFeatures: ['text2video', 'firstFrame', 'image_upload']
  },
  {
    id: 'veo3-pro',
    name: 'Veo3 Pro',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/video/veo',
    creditsPerUnit: 400,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: 'Veo3 专业视频生成',
    supportedFeatures: ['text2video', 'firstFrame', 'image_upload']
  }
];

/**
 * 根据模型类型获取模型列表
 */
export function getModelsByType(type: ModelType): ModelConfig[] {
  return GRSAI_MODELS.filter(model => model.type === type && model.isActive);
}

/**
 * 根据模型ID获取模型配置
 */
export function getModelById(id: string): ModelConfig | undefined {
  return GRSAI_MODELS.find(model => model.id === id);
}

/**
 * 获取所有活跃模型
 */
export function getActiveModels(): ModelConfig[] {
  return GRSAI_MODELS.filter(model => model.isActive);
}

/**
 * 根据提供商获取模型列表
 */
export function getModelsByProvider(provider: Provider): ModelConfig[] {
  return GRSAI_MODELS.filter(model => model.provider === provider && model.isActive);
}

/**
 * GRSAI API 基础配置
 */
export const GRSAI_CONFIG = {
  baseURL: {
    overseas: 'https://api.grsai.com',
    domestic: 'https://grsai.dakka.com.cn'
  },
  defaultRegion: 'overseas', // 默认使用海外节点
  timeout: 60000, // 60秒超时
  retryAttempts: 3,
  retryDelay: 1000
};
