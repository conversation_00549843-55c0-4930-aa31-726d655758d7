"use client";

import Analytics from "@/components/analytics";
import { <PERSON>ache<PERSON><PERSON> } from "@/services/constant";
import SignModal from "@/components/sign/modal";
import { Toaster } from "@/components/ui/sonner";
import { cacheGet } from "@/lib/cache";
import { useAppContext } from "@/contexts/app";
import { useEffect } from "react";

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const { theme, setTheme } = useAppContext();

  useEffect(() => {
    const themeInCache = cacheGet(CacheKey.Theme);
    if (themeInCache) {
      // theme setted
      if (["dark", "light"].includes(themeInCache)) {
        setTheme(themeInCache);
        return;
      }
    } else {
      // theme not set
      const defaultTheme = process.env.NEXT_PUBLIC_DEFAULT_THEME;
      if (defaultTheme && ["dark", "light"].includes(defaultTheme)) {
        setTheme(defaultTheme);
        return;
      }
    }

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    setTheme(mediaQuery.matches ? "dark" : "light");

    const handleChange = () => {
      setTheme(mediaQuery.matches ? "dark" : "light");
    };
    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  // 应用主题类到body元素
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const body = document.body;

      // 移除所有主题类
      body.classList.remove('dark', 'light');

      // 添加当前主题类
      if (theme) {
        body.classList.add(theme);
      }
    }
  }, [theme]);

  return (
    <>
      {children}
      <Toaster position="top-center" richColors />
      <SignModal />
      <Analytics />
    </>
  );
}
